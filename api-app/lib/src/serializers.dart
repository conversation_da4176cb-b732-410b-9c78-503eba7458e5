//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_import

import 'package:one_of_serializer/any_of_serializer.dart';
import 'package:one_of_serializer/one_of_serializer.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:built_value/standard_json_plugin.dart';
import 'package:built_value/iso_8601_date_time_serializer.dart';
import 'package:openapi/src/date_serializer.dart';
import 'package:openapi/src/model/date.dart';

import 'package:openapi/src/model/assessment_answer.dart';
import 'package:openapi/src/model/assessment_question.dart';
import 'package:openapi/src/model/assessment_question_data.dart';
import 'package:openapi/src/model/assessment_questions_response_model.dart';
import 'package:openapi/src/model/bool_response_model.dart';
import 'package:openapi/src/model/captcha.dart';
import 'package:openapi/src/model/captcha_and_user_status.dart';
import 'package:openapi/src/model/captcha_response_model.dart';
import 'package:openapi/src/model/category_skill.dart';
import 'package:openapi/src/model/chat.dart';
import 'package:openapi/src/model/check_captcha_require_in_login.dart';
import 'package:openapi/src/model/check_captcha_require_in_login_response_model.dart';
import 'package:openapi/src/model/company_my_recruitment_details_response_model.dart';
import 'package:openapi/src/model/company_recruit_detail.dart';
import 'package:openapi/src/model/confirm_login.dart';
import 'package:openapi/src/model/confirm_whatsapp_code.dart';
import 'package:openapi/src/model/convert_currency.dart';
import 'package:openapi/src/model/count_unread_notify.dart';
import 'package:openapi/src/model/count_unread_notify_response_model.dart';
import 'package:openapi/src/model/create_email_schedule.dart';
import 'package:openapi/src/model/create_recruitment.dart';
import 'package:openapi/src/model/delete_engineers.dart';
import 'package:openapi/src/model/delete_recruitment.dart';
import 'package:openapi/src/model/description.dart';
import 'package:openapi/src/model/email_schedule.dart';
import 'package:openapi/src/model/eng_academic.dart';
import 'package:openapi/src/model/eng_career.dart';
import 'package:openapi/src/model/eng_career_job_skill.dart';
import 'package:openapi/src/model/eng_high_light_project.dart';
import 'package:openapi/src/model/eng_hope.dart';
import 'package:openapi/src/model/eng_language.dart';
import 'package:openapi/src/model/eng_license.dart';
import 'package:openapi/src/model/eng_self_assesment.dart';
import 'package:openapi/src/model/eng_skill.dart';
import 'package:openapi/src/model/eng_work_history.dart';
import 'package:openapi/src/model/engineer_agency_company.dart';
import 'package:openapi/src/model/engineer_agency_company_response_model.dart';
import 'package:openapi/src/model/engineer_best_company.dart';
import 'package:openapi/src/model/engineer_get_list_best_companies_response_model.dart';
import 'package:openapi/src/model/engineer_list_agency_company.dart';
import 'package:openapi/src/model/engineer_list_agency_company_response_model.dart';
import 'package:openapi/src/model/engineer_list_apply_company.dart';
import 'package:openapi/src/model/engineer_list_apply_company_information.dart';
import 'package:openapi/src/model/engineer_list_apply_company_pagination.dart';
import 'package:openapi/src/model/engineer_list_apply_company_response_model.dart';
import 'package:openapi/src/model/engineer_list_best_company_pagination.dart';
import 'package:openapi/src/model/engineer_remove_agency_company.dart';
import 'package:openapi/src/model/engineer_self_assesment_details.dart';
import 'package:openapi/src/model/engineer_update_agency_company.dart';
import 'package:openapi/src/model/engineer_update_data_policy.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:openapi/src/model/evaluation_method.dart';
import 'package:openapi/src/model/export_user_data_body.dart';
import 'package:openapi/src/model/featured_job.dart';
import 'package:openapi/src/model/general_company.dart';
import 'package:openapi/src/model/general_company_applied_engineers.dart';
import 'package:openapi/src/model/general_company_apply_details.dart';
import 'package:openapi/src/model/general_company_apply_details_response_model.dart';
import 'package:openapi/src/model/general_company_calendar_interview_response_model.dart';
import 'package:openapi/src/model/general_company_compare_details.dart';
import 'package:openapi/src/model/general_company_contract_details_response_model.dart';
import 'package:openapi/src/model/general_company_details.dart';
import 'package:openapi/src/model/general_company_explore_user.dart';
import 'package:openapi/src/model/general_company_filter.dart';
import 'package:openapi/src/model/general_company_get_apply_details.dart';
import 'package:openapi/src/model/general_company_get_contract_details.dart';
import 'package:openapi/src/model/general_company_get_user_explore_count_response_model.dart';
import 'package:openapi/src/model/general_company_interview_data.dart';
import 'package:openapi/src/model/general_company_list_applied_engineer.dart';
import 'package:openapi/src/model/general_company_list_filter_response_model.dart';
import 'package:openapi/src/model/general_company_list_group_chat_response_model.dart';
import 'package:openapi/src/model/general_company_list_user_applied_company_response_model.dart';
import 'package:openapi/src/model/general_company_list_user_explore_response_model.dart';
import 'package:openapi/src/model/general_company_pr_comment.dart';
import 'package:openapi/src/model/general_company_param_get_contract_details.dart';
import 'package:openapi/src/model/general_company_recruit_info.dart';
import 'package:openapi/src/model/general_company_register.dart';
import 'package:openapi/src/model/general_company_reject_apply.dart';
import 'package:openapi/src/model/general_company_request_interview_apply.dart';
import 'package:openapi/src/model/general_company_request_interview_apply_response_model.dart';
import 'package:openapi/src/model/general_company_response_model.dart';
import 'package:openapi/src/model/general_company_save_filter.dart';
import 'package:openapi/src/model/general_company_update.dart';
import 'package:openapi/src/model/general_company_update_favorite_user.dart';
import 'package:openapi/src/model/general_company_update_interview_date_time.dart';
import 'package:openapi/src/model/general_company_user.dart';
import 'package:openapi/src/model/general_company_user_applied_company.dart';
import 'package:openapi/src/model/get_email_detail_response_model.dart';
import 'package:openapi/src/model/get_email_schedule_response_model.dart';
import 'package:openapi/src/model/get_list_engineer.dart';
import 'package:openapi/src/model/get_list_engineer_response_model.dart';
import 'package:openapi/src/model/get_list_registrar_response_model.dart';
import 'package:openapi/src/model/get_video_social_source_response_model.dart';
import 'package:openapi/src/model/group_chat.dart';
import 'package:openapi/src/model/hope_category_skill.dart';
import 'package:openapi/src/model/hope_job_skill.dart';
import 'package:openapi/src/model/host_company.dart';
import 'package:openapi/src/model/host_company_get_support_company.dart';
import 'package:openapi/src/model/host_company_list_applied_engineer.dart';
import 'package:openapi/src/model/host_company_list_support_company_response_model.dart';
import 'package:openapi/src/model/host_company_list_user_applied_company_response_model.dart';
import 'package:openapi/src/model/host_company_param_list_user_applied_company.dart';
import 'package:openapi/src/model/host_company_request_interview.dart';
import 'package:openapi/src/model/host_company_subscribe_support_company.dart';
import 'package:openapi/src/model/host_company_unsubscribe_support_company.dart';
import 'package:openapi/src/model/host_company_update_interview_admission.dart';
import 'package:openapi/src/model/image.dart';
import 'package:openapi/src/model/list_group_chat.dart';
import 'package:openapi/src/model/list_manage_host_company.dart';
import 'package:openapi/src/model/list_manage_host_company_response_model.dart';
import 'package:openapi/src/model/list_notify_response_model.dart';
import 'package:openapi/src/model/list_recruit_serializers.dart';
import 'package:openapi/src/model/list_recruit_uploaded_response_model.dart';
import 'package:openapi/src/model/list_top_featured_jobs_response_model.dart';
import 'package:openapi/src/model/login.dart';
import 'package:openapi/src/model/login_success.dart';
import 'package:openapi/src/model/login_success_response_model.dart';
import 'package:openapi/src/model/login_with_sns.dart';
import 'package:openapi/src/model/login_with_sns_response_data.dart';
import 'package:openapi/src/model/login_with_sns_response_model.dart';
import 'package:openapi/src/model/logout.dart';
import 'package:openapi/src/model/main_skill.dart';
import 'package:openapi/src/model/manage_host_company.dart';
import 'package:openapi/src/model/mark_read_all_messages_response_model.dart';
import 'package:openapi/src/model/media_response_model.dart';
import 'package:openapi/src/model/notify.dart';
import 'package:openapi/src/model/option.dart';
import 'package:openapi/src/model/paging_email_schedule.dart';
import 'package:openapi/src/model/paging_general_company_explore_user_response_model.dart';
import 'package:openapi/src/model/paging_get_list_engineer.dart';
import 'package:openapi/src/model/paging_response_model.dart';
import 'package:openapi/src/model/question.dart';
import 'package:openapi/src/model/questions.dart';
import 'package:openapi/src/model/rec_recruit.dart';
import 'package:openapi/src/model/recruit_company.dart';
import 'package:openapi/src/model/recruit_company_information.dart';
import 'package:openapi/src/model/recruit_contract_details_response_model.dart';
import 'package:openapi/src/model/recruit_count_filter_response_model.dart';
import 'package:openapi/src/model/recruit_detail.dart';
import 'package:openapi/src/model/recruit_explore.dart';
import 'package:openapi/src/model/recruit_explore_response_model.dart';
import 'package:openapi/src/model/recruit_get_contract_details.dart';
import 'package:openapi/src/model/recruit_uploaded_serializers.dart';
import 'package:openapi/src/model/recruitment_details_response_model.dart';
import 'package:openapi/src/model/recruitment_management_detail.dart';
import 'package:openapi/src/model/recruitment_management_details_response_model.dart';
import 'package:openapi/src/model/register.dart';
import 'package:openapi/src/model/request_interview_success.dart';
import 'package:openapi/src/model/reset_password.dart';
import 'package:openapi/src/model/reset_password_confirm.dart';
import 'package:openapi/src/model/response_model.dart';
import 'package:openapi/src/model/sp_company_registered.dart';
import 'package:openapi/src/model/sp_list_registered_company_response_model.dart';
import 'package:openapi/src/model/sp_request_interview_body.dart';
import 'package:openapi/src/model/save_cv_uploaded.dart';
import 'package:openapi/src/model/save_cv_uploaded_response.dart';
import 'package:openapi/src/model/self_assessment_answer.dart';
import 'package:openapi/src/model/self_assessment_data.dart';
import 'package:openapi/src/model/self_assessment_response_model.dart';
import 'package:openapi/src/model/self_assessment_sheet.dart';
import 'package:openapi/src/model/send_code_request.dart';
import 'package:openapi/src/model/send_whatsapp_code.dart';
import 'package:openapi/src/model/set_read_notify.dart';
import 'package:openapi/src/model/set_read_notify_response_model.dart';
import 'package:openapi/src/model/sign_contract.dart';
import 'package:openapi/src/model/sign_contract_response_model.dart';
import 'package:openapi/src/model/support_language200_response.dart';
import 'package:openapi/src/model/token_refresh.dart';
import 'package:openapi/src/model/token_verify.dart';
import 'package:openapi/src/model/update_career_job_skill.dart';
import 'package:openapi/src/model/update_eng_academic.dart';
import 'package:openapi/src/model/update_eng_career.dart';
import 'package:openapi/src/model/update_eng_high_light_project.dart';
import 'package:openapi/src/model/update_eng_hope.dart';
import 'package:openapi/src/model/update_eng_language.dart';
import 'package:openapi/src/model/update_eng_license.dart';
import 'package:openapi/src/model/update_eng_self_assesment.dart';
import 'package:openapi/src/model/update_eng_skill.dart';
import 'package:openapi/src/model/update_engineer.dart';
import 'package:openapi/src/model/update_hope_job_skill.dart';
import 'package:openapi/src/model/update_interview_datetime.dart';
import 'package:openapi/src/model/update_password.dart';
import 'package:openapi/src/model/update_recruitment.dart';
import 'package:openapi/src/model/update_user.dart';
import 'package:openapi/src/model/update_user_response_model.dart';
import 'package:openapi/src/model/upload_cv_uploaded.dart';
import 'package:openapi/src/model/upload_cv_uploaded_response.dart';
import 'package:openapi/src/model/user_chat.dart';
import 'package:openapi/src/model/user_details_response_model.dart';
import 'package:openapi/src/model/user_details_serializers.dart';
import 'package:openapi/src/model/user_explore_details_response_model.dart';
import 'package:openapi/src/model/user_explore_details_serializers.dart';
import 'package:openapi/src/model/verify_email.dart';

part 'serializers.g.dart';

@SerializersFor([
  AssessmentAnswer,
  AssessmentQuestion,
  AssessmentQuestionData,
  AssessmentQuestionsResponseModel,
  BoolResponseModel,
  Captcha,
  CaptchaAndUserStatus,
  CaptchaResponseModel,
  CategorySkill,
  Chat,
  CheckCaptchaRequireInLogin,
  CheckCaptchaRequireInLoginResponseModel,
  CompanyMyRecruitmentDetailsResponseModel,
  CompanyRecruitDetail,
  ConfirmLogin,
  ConfirmWhatsappCode,
  ConvertCurrency,
  CountUnreadNotify,
  CountUnreadNotifyResponseModel,
  CreateEmailSchedule,
  CreateRecruitment,
  DeleteEngineers,
  DeleteRecruitment,
  Description,
  EmailSchedule,
  EngAcademic,
  EngCareer,
  EngCareerJobSkill,
  EngHighLightProject,
  EngHope,
  EngLanguage,
  EngLicense,
  EngSelfAssesment,
  EngSkill,
  EngWorkHistory,
  EngineerAgencyCompany,
  EngineerAgencyCompanyResponseModel,
  EngineerBestCompany,
  EngineerGetListBestCompaniesResponseModel,
  EngineerListAgencyCompany,
  EngineerListAgencyCompanyResponseModel,
  EngineerListApplyCompany,
  EngineerListApplyCompanyInformation,
  EngineerListApplyCompanyPagination,
  EngineerListApplyCompanyResponseModel,
  EngineerListBestCompanyPagination,
  EngineerRemoveAgencyCompany,
  EngineerSelfAssesmentDetails,
  EngineerUpdateAgencyCompany,
  EngineerUpdateDataPolicy,
  ErrorDetail,
  EvaluationMethod,
  ExportUserDataBody,
  FeaturedJob,
  GeneralCompany,
  GeneralCompanyAppliedEngineers,
  GeneralCompanyApplyDetails,
  GeneralCompanyApplyDetailsResponseModel,
  GeneralCompanyCalendarInterviewResponseModel,
  GeneralCompanyCompareDetails,
  GeneralCompanyContractDetailsResponseModel,
  GeneralCompanyDetails,
  GeneralCompanyExploreUser,
  GeneralCompanyFilter,
  GeneralCompanyGetApplyDetails,
  GeneralCompanyGetContractDetails,
  GeneralCompanyGetUserExploreCountResponseModel,
  GeneralCompanyInterviewData,
  GeneralCompanyListAppliedEngineer,
  GeneralCompanyListFilterResponseModel,
  GeneralCompanyListGroupChatResponseModel,
  GeneralCompanyListUserAppliedCompanyResponseModel,
  GeneralCompanyListUserExploreResponseModel,
  GeneralCompanyPRComment,
  GeneralCompanyParamGetContractDetails,
  GeneralCompanyRecruitInfo,
  GeneralCompanyRegister,
  GeneralCompanyRejectApply,
  GeneralCompanyRequestInterviewApply,
  GeneralCompanyRequestInterviewApplyResponseModel,
  GeneralCompanyResponseModel,
  GeneralCompanySaveFilter,
  GeneralCompanyUpdate,
  GeneralCompanyUpdateFavoriteUser,
  GeneralCompanyUpdateInterviewDateTime,
  GeneralCompanyUser,
  GeneralCompanyUserAppliedCompany,
  GetEmailDetailResponseModel,
  GetEmailScheduleResponseModel,
  GetListEngineer,
  GetListEngineerResponseModel,
  GetListRegistrarResponseModel,
  GetVideoSocialSourceResponseModel,
  GroupChat,
  HopeCategorySkill,
  HopeJobSkill,
  HostCompany,
  HostCompanyGetSupportCompany,
  HostCompanyListAppliedEngineer,
  HostCompanyListSupportCompanyResponseModel,
  HostCompanyListUserAppliedCompanyResponseModel,
  HostCompanyParamListUserAppliedCompany,
  HostCompanyRequestInterview,
  HostCompanySubscribeSupportCompany,
  HostCompanyUnsubscribeSupportCompany,
  HostCompanyUpdateInterviewAdmission,
  Image,
  ListGroupChat,
  ListManageHostCompany,
  ListManageHostCompanyResponseModel,
  ListNotifyResponseModel,
  ListRecruitSerializers,
  ListRecruitUploadedResponseModel,
  ListTopFeaturedJobsResponseModel,
  Login,
  LoginSuccess,
  LoginSuccessResponseModel,
  LoginWithSNS,
  LoginWithSNSResponseData,
  LoginWithSNSResponseModel,
  Logout,
  MainSkill,
  ManageHostCompany,
  MarkReadAllMessagesResponseModel,
  MediaResponseModel,
  Notify,
  Option,
  PagingEmailSchedule,
  PagingGeneralCompanyExploreUserResponseModel,
  PagingGetListEngineer,
  PagingResponseModel,
  Question,
  Questions,
  RecRecruit,
  RecruitCompany,
  RecruitCompanyInformation,
  RecruitContractDetailsResponseModel,
  RecruitCountFilterResponseModel,
  RecruitDetail,
  RecruitExplore,
  RecruitExploreResponseModel,
  RecruitGetContractDetails,
  RecruitUploadedSerializers,
  RecruitmentDetailsResponseModel,
  RecruitmentManagementDetail,
  RecruitmentManagementDetailsResponseModel,
  Register,
  RequestInterviewSuccess,
  ResetPassword,
  ResetPasswordConfirm,
  ResponseModel,
  SPCompanyRegistered,
  SPListRegisteredCompanyResponseModel,
  SPRequestInterviewBody,
  SaveCVUploaded,
  SaveCVUploadedResponse,
  SelfAssessmentAnswer,
  SelfAssessmentData,
  SelfAssessmentResponseModel,
  SelfAssessmentSheet,
  SendCodeRequest,
  SendWhatsappCode,
  SetReadNotify,
  SetReadNotifyResponseModel,
  SignContract,
  SignContractResponseModel,
  SupportLanguage200Response,
  TokenRefresh,
  TokenVerify,
  UpdateCareerJobSkill,
  UpdateEngAcademic,
  UpdateEngCareer,
  UpdateEngHighLightProject,
  UpdateEngHope,
  UpdateEngLanguage,
  UpdateEngLicense,
  UpdateEngSelfAssesment,
  UpdateEngSkill,
  UpdateEngineer,
  UpdateHopeJobSkill,
  UpdateInterviewDatetime,
  UpdatePassword,
  UpdateRecruitment,
  UpdateUser,
  UpdateUserResponseModel,
  UploadCVUploaded,
  UploadCVUploadedResponse,
  UserChat,
  UserDetailsResponseModel,
  UserDetailsSerializers,
  UserExploreDetailsResponseModel,
  UserExploreDetailsSerializers,
  VerifyEmail,
])
Serializers serializers = (_$serializers.toBuilder()
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(UserChat)]),
        () => ListBuilder<UserChat>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(int)]),
        () => ListBuilder<int>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType.nullable(String)]),
        () => ListBuilder<String>(),
      )
      ..addBuilderFactory(
        const FullType(BuiltList, [FullType(String)]),
        () => ListBuilder<String>(),
      )
      ..add(const OneOfSerializer())
      ..add(const AnyOfSerializer())
      ..add(const DateSerializer())
      ..add(Iso8601DateTimeSerializer()))
    .build();

Serializers standardSerializers =
    (serializers.toBuilder()..addPlugin(StandardJsonPlugin())).build();
