// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'serializers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

Serializers _$serializers = (new Serializers().toBuilder()
      ..add(AssessmentAnswer.serializer)
      ..add(AssessmentQuestion.serializer)
      ..add(AssessmentQuestionData.serializer)
      ..add(AssessmentQuestionsResponseModel.serializer)
      ..add(BoolResponseModel.serializer)
      ..add(Captcha.serializer)
      ..add(CaptchaAndUserStatus.serializer)
      ..add(CaptchaResponseModel.serializer)
      ..add(CategorySkill.serializer)
      ..add(Chat.serializer)
      ..add(CheckCaptchaRequireInLogin.serializer)
      ..add(CheckCaptchaRequireInLoginResponseModel.serializer)
      ..add(CompanyMyRecruitmentDetailsResponseModel.serializer)
      ..add(CompanyRecruitDetail.serializer)
      ..add(ConfirmLogin.serializer)
      ..add(ConfirmWhatsappCode.serializer)
      ..add(ConvertCurrency.serializer)
      ..add(CountUnreadNotify.serializer)
      ..add(CountUnreadNotifyResponseModel.serializer)
      ..add(CreateEmailSchedule.serializer)
      ..add(CreateRecruitment.serializer)
      ..add(DeleteEngineers.serializer)
      ..add(DeleteRecruitment.serializer)
      ..add(Description.serializer)
      ..add(EmailSchedule.serializer)
      ..add(EngAcademic.serializer)
      ..add(EngCareer.serializer)
      ..add(EngCareerJobSkill.serializer)
      ..add(EngHighLightProject.serializer)
      ..add(EngHope.serializer)
      ..add(EngLanguage.serializer)
      ..add(EngLicense.serializer)
      ..add(EngSelfAssesment.serializer)
      ..add(EngSkill.serializer)
      ..add(EngWorkHistory.serializer)
      ..add(EngineerAgencyCompany.serializer)
      ..add(EngineerAgencyCompanyResponseModel.serializer)
      ..add(EngineerBestCompany.serializer)
      ..add(EngineerGetListBestCompaniesResponseModel.serializer)
      ..add(EngineerListAgencyCompany.serializer)
      ..add(EngineerListAgencyCompanyResponseModel.serializer)
      ..add(EngineerListApplyCompany.serializer)
      ..add(EngineerListApplyCompanyInformation.serializer)
      ..add(EngineerListApplyCompanyPagination.serializer)
      ..add(EngineerListApplyCompanyResponseModel.serializer)
      ..add(EngineerListBestCompanyPagination.serializer)
      ..add(EngineerRemoveAgencyCompany.serializer)
      ..add(EngineerSelfAssesmentDetails.serializer)
      ..add(EngineerUpdateAgencyCompany.serializer)
      ..add(EngineerUpdateDataPolicy.serializer)
      ..add(ErrorDetail.serializer)
      ..add(EvaluationMethod.serializer)
      ..add(ExportUserDataBody.serializer)
      ..add(FeaturedJob.serializer)
      ..add(GeneralCompany.serializer)
      ..add(GeneralCompanyAppliedEngineers.serializer)
      ..add(GeneralCompanyApplyDetails.serializer)
      ..add(GeneralCompanyApplyDetailsResponseModel.serializer)
      ..add(GeneralCompanyCalendarInterviewResponseModel.serializer)
      ..add(GeneralCompanyCompareDetails.serializer)
      ..add(GeneralCompanyContractDetailsResponseModel.serializer)
      ..add(GeneralCompanyDetails.serializer)
      ..add(GeneralCompanyExploreUser.serializer)
      ..add(GeneralCompanyFilter.serializer)
      ..add(GeneralCompanyGetApplyDetails.serializer)
      ..add(GeneralCompanyGetContractDetails.serializer)
      ..add(GeneralCompanyGetUserExploreCountResponseModel.serializer)
      ..add(GeneralCompanyInterviewData.serializer)
      ..add(GeneralCompanyListAppliedEngineer.serializer)
      ..add(GeneralCompanyListFilterResponseModel.serializer)
      ..add(GeneralCompanyListGroupChatResponseModel.serializer)
      ..add(GeneralCompanyListUserAppliedCompanyResponseModel.serializer)
      ..add(GeneralCompanyListUserExploreResponseModel.serializer)
      ..add(GeneralCompanyPRComment.serializer)
      ..add(GeneralCompanyParamGetContractDetails.serializer)
      ..add(GeneralCompanyRecruitInfo.serializer)
      ..add(GeneralCompanyRegister.serializer)
      ..add(GeneralCompanyRejectApply.serializer)
      ..add(GeneralCompanyRequestInterviewApply.serializer)
      ..add(GeneralCompanyRequestInterviewApplyResponseModel.serializer)
      ..add(GeneralCompanyResponseModel.serializer)
      ..add(GeneralCompanySaveFilter.serializer)
      ..add(GeneralCompanyUpdate.serializer)
      ..add(GeneralCompanyUpdateFavoriteUser.serializer)
      ..add(GeneralCompanyUpdateInterviewDateTime.serializer)
      ..add(GeneralCompanyUser.serializer)
      ..add(GeneralCompanyUserAppliedCompany.serializer)
      ..add(GetEmailDetailResponseModel.serializer)
      ..add(GetEmailScheduleResponseModel.serializer)
      ..add(GetListEngineer.serializer)
      ..add(GetListEngineerResponseModel.serializer)
      ..add(GetListRegistrarResponseModel.serializer)
      ..add(GetVideoSocialSourceResponseModel.serializer)
      ..add(GroupChat.serializer)
      ..add(HopeCategorySkill.serializer)
      ..add(HopeJobSkill.serializer)
      ..add(HostCompany.serializer)
      ..add(HostCompanyGetSupportCompany.serializer)
      ..add(HostCompanyListAppliedEngineer.serializer)
      ..add(HostCompanyListSupportCompanyResponseModel.serializer)
      ..add(HostCompanyListUserAppliedCompanyResponseModel.serializer)
      ..add(HostCompanyParamListUserAppliedCompany.serializer)
      ..add(HostCompanyRequestInterview.serializer)
      ..add(HostCompanySubscribeSupportCompany.serializer)
      ..add(HostCompanyUnsubscribeSupportCompany.serializer)
      ..add(HostCompanyUpdateInterviewAdmission.serializer)
      ..add(Image.serializer)
      ..add(ListGroupChat.serializer)
      ..add(ListManageHostCompany.serializer)
      ..add(ListManageHostCompanyResponseModel.serializer)
      ..add(ListNotifyResponseModel.serializer)
      ..add(ListRecruitSerializers.serializer)
      ..add(ListRecruitUploadedResponseModel.serializer)
      ..add(ListTopFeaturedJobsResponseModel.serializer)
      ..add(Login.serializer)
      ..add(LoginSuccess.serializer)
      ..add(LoginSuccessResponseModel.serializer)
      ..add(LoginWithSNS.serializer)
      ..add(LoginWithSNSResponseData.serializer)
      ..add(LoginWithSNSResponseModel.serializer)
      ..add(Logout.serializer)
      ..add(MainSkill.serializer)
      ..add(ManageHostCompany.serializer)
      ..add(MarkReadAllMessagesResponseModel.serializer)
      ..add(MediaResponseModel.serializer)
      ..add(Notify.serializer)
      ..add(Option.serializer)
      ..add(PagingEmailSchedule.serializer)
      ..add(PagingGeneralCompanyExploreUserResponseModel.serializer)
      ..add(PagingGetListEngineer.serializer)
      ..add(PagingResponseModel.serializer)
      ..add(Question.serializer)
      ..add(Questions.serializer)
      ..add(RecRecruit.serializer)
      ..add(RecruitCompany.serializer)
      ..add(RecruitCompanyInformation.serializer)
      ..add(RecruitContractDetailsResponseModel.serializer)
      ..add(RecruitCountFilterResponseModel.serializer)
      ..add(RecruitDetail.serializer)
      ..add(RecruitExplore.serializer)
      ..add(RecruitExploreResponseModel.serializer)
      ..add(RecruitGetContractDetails.serializer)
      ..add(RecruitUploadedSerializers.serializer)
      ..add(RecruitmentDetailsResponseModel.serializer)
      ..add(RecruitmentManagementDetail.serializer)
      ..add(RecruitmentManagementDetailsResponseModel.serializer)
      ..add(Register.serializer)
      ..add(RequestInterviewSuccess.serializer)
      ..add(ResetPassword.serializer)
      ..add(ResetPasswordConfirm.serializer)
      ..add(ResponseModel.serializer)
      ..add(SPCompanyRegistered.serializer)
      ..add(SPListRegisteredCompanyResponseModel.serializer)
      ..add(SPRequestInterviewBody.serializer)
      ..add(SaveCVUploaded.serializer)
      ..add(SaveCVUploadedResponse.serializer)
      ..add(SelfAssessmentAnswer.serializer)
      ..add(SelfAssessmentData.serializer)
      ..add(SelfAssessmentResponseModel.serializer)
      ..add(SelfAssessmentSheet.serializer)
      ..add(SendCodeRequest.serializer)
      ..add(SendWhatsappCode.serializer)
      ..add(SetReadNotify.serializer)
      ..add(SetReadNotifyResponseModel.serializer)
      ..add(SignContract.serializer)
      ..add(SignContractResponseModel.serializer)
      ..add(SupportLanguage200Response.serializer)
      ..add(TokenRefresh.serializer)
      ..add(TokenVerify.serializer)
      ..add(UpdateCareerJobSkill.serializer)
      ..add(UpdateEngAcademic.serializer)
      ..add(UpdateEngCareer.serializer)
      ..add(UpdateEngHighLightProject.serializer)
      ..add(UpdateEngHope.serializer)
      ..add(UpdateEngLanguage.serializer)
      ..add(UpdateEngLicense.serializer)
      ..add(UpdateEngSelfAssesment.serializer)
      ..add(UpdateEngSkill.serializer)
      ..add(UpdateEngineer.serializer)
      ..add(UpdateHopeJobSkill.serializer)
      ..add(UpdateInterviewDatetime.serializer)
      ..add(UpdatePassword.serializer)
      ..add(UpdateRecruitment.serializer)
      ..add(UpdateUser.serializer)
      ..add(UpdateUserResponseModel.serializer)
      ..add(UploadCVUploaded.serializer)
      ..add(UploadCVUploadedResponse.serializer)
      ..add(UserChat.serializer)
      ..add(UserDetailsResponseModel.serializer)
      ..add(UserDetailsSerializers.serializer)
      ..add(UserExploreDetailsResponseModel.serializer)
      ..add(UserExploreDetailsSerializers.serializer)
      ..add(VerifyEmail.serializer)
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AssessmentAnswer)]),
          () => new ListBuilder<AssessmentAnswer>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(AssessmentQuestionData)]),
          () => new ListBuilder<AssessmentQuestionData>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => new ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EmailSchedule)]),
          () => new ListBuilder<EmailSchedule>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngAcademic)]),
          () => new ListBuilder<EngAcademic>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngLanguage)]),
          () => new ListBuilder<EngLanguage>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngLicense)]),
          () => new ListBuilder<EngLicense>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngSkill)]),
          () => new ListBuilder<EngSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngCareer)]),
          () => new ListBuilder<EngCareer>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngAcademic)]),
          () => new ListBuilder<EngAcademic>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngLanguage)]),
          () => new ListBuilder<EngLanguage>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngLicense)]),
          () => new ListBuilder<EngLicense>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngSkill)]),
          () => new ListBuilder<EngSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngCareer)]),
          () => new ListBuilder<EngCareer>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(CategorySkill)]),
          () => new ListBuilder<CategorySkill>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(EngHighLightProject)]),
          () => new ListBuilder<EngHighLightProject>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngCareerJobSkill)]),
          () => new ListBuilder<EngCareerJobSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngLanguage)]),
          () => new ListBuilder<EngLanguage>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngSkill)]),
          () => new ListBuilder<EngSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngLicense)]),
          () => new ListBuilder<EngLicense>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(MainSkill)]),
          () => new ListBuilder<MainSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => new ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngWorkHistory)]),
          () => new ListBuilder<EngWorkHistory>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngSkill)]),
          () => new ListBuilder<EngSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngSkill)]),
          () => new ListBuilder<EngSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngSkill)]),
          () => new ListBuilder<EngSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EngLanguage)]),
          () => new ListBuilder<EngLanguage>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(EngineerAgencyCompany)]),
          () => new ListBuilder<EngineerAgencyCompany>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(EngineerBestCompany)]),
          () => new ListBuilder<EngineerBestCompany>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(EngineerListApplyCompany)]),
          () => new ListBuilder<EngineerListApplyCompany>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(EngineerListAgencyCompany)]),
          () => new ListBuilder<EngineerListAgencyCompany>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(GeneralCompanyInterviewData)]),
          () => new ListBuilder<GeneralCompanyInterviewData>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(HostCompanyGetSupportCompany)]),
          () => new ListBuilder<HostCompanyGetSupportCompany>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(SPCompanyRegistered)]),
          () => new ListBuilder<SPCompanyRegistered>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(EvaluationMethod)]),
          () => new ListBuilder<EvaluationMethod>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(Questions)]),
          () => new ListBuilder<Questions>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(FeaturedJob)]),
          () => new ListBuilder<FeaturedJob>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType(GeneralCompanyAppliedEngineers)]),
          () => new ListBuilder<GeneralCompanyAppliedEngineers>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(GeneralCompanyExploreUser)]),
          () => new ListBuilder<GeneralCompanyExploreUser>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(GeneralCompanyFilter)]),
          () => new ListBuilder<GeneralCompanyFilter>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(GeneralCompanyPRComment)]),
          () => new ListBuilder<GeneralCompanyPRComment>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(GetListEngineer)]),
          () => new ListBuilder<GetListEngineer>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(GroupChat)]),
          () => new ListBuilder<GroupChat>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(HopeJobSkill)]),
          () => new ListBuilder<HopeJobSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(HopeJobSkill)]),
          () => new ListBuilder<HopeJobSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(HopeCategorySkill)]),
          () => new ListBuilder<HopeCategorySkill>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType(HostCompanyParamListUserAppliedCompany)]),
          () => new ListBuilder<HostCompanyParamListUserAppliedCompany>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ManageHostCompany)]),
          () => new ListBuilder<ManageHostCompany>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(Notify)]),
          () => new ListBuilder<Notify>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => new ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(Option)]),
          () => new ListBuilder<Option>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(RecRecruit)]),
          () => new ListBuilder<RecRecruit>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(RecRecruit)]),
          () => new ListBuilder<RecRecruit>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(RecruitExplore)]),
          () => new ListBuilder<RecruitExplore>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(RecruitUploadedSerializers)]),
          () => new ListBuilder<RecruitUploadedSerializers>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => new ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => new ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => new ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => new ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => new ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(UpdateCareerJobSkill)]),
          () => new ListBuilder<UpdateCareerJobSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(UpdateHopeJobSkill)]),
          () => new ListBuilder<UpdateHopeJobSkill>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(UserChat)]),
          () => new ListBuilder<UserChat>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(int)]),
          () => new ListBuilder<int>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(int)]),
          () => new ListBuilder<int>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(int)]),
          () => new ListBuilder<int>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(int)]),
          () => new ListBuilder<int>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(int)]),
          () => new ListBuilder<int>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType.nullable(String)]),
          () => new ListBuilder<String?>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType.nullable(UpdateEngAcademic)]),
          () => new ListBuilder<UpdateEngAcademic?>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType.nullable(UpdateEngLanguage)]),
          () => new ListBuilder<UpdateEngLanguage?>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType.nullable(UpdateEngLicense)]),
          () => new ListBuilder<UpdateEngLicense?>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType.nullable(UpdateEngSkill)]),
          () => new ListBuilder<UpdateEngSkill?>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType.nullable(UpdateEngCareer)]),
          () => new ListBuilder<UpdateEngCareer?>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType.nullable(UpdateEngHighLightProject)]),
          () => new ListBuilder<UpdateEngHighLightProject?>())
      ..addBuilderFactory(
          const FullType(BuiltMap,
              const [const FullType(String), const FullType.nullable(String)]),
          () => new MapBuilder<String, String?>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ErrorDetail)]),
          () => new ListBuilder<ErrorDetail>()))
    .build();

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
