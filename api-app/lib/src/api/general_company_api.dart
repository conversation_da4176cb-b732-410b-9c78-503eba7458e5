//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'dart:async';

import 'package:built_value/serializer.dart';
import 'package:dio/dio.dart';

import 'dart:typed_data';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/api_util.dart';
import 'package:openapi/src/model/assessment_questions_response_model.dart';
import 'package:openapi/src/model/date.dart';
import 'package:openapi/src/model/general_company_apply_details_response_model.dart';
import 'package:openapi/src/model/general_company_calendar_interview_response_model.dart';
import 'package:openapi/src/model/general_company_contract_details_response_model.dart';
import 'package:openapi/src/model/general_company_get_user_explore_count_response_model.dart';
import 'package:openapi/src/model/general_company_list_filter_response_model.dart';
import 'package:openapi/src/model/general_company_list_group_chat_response_model.dart';
import 'package:openapi/src/model/general_company_list_user_applied_company_response_model.dart';
import 'package:openapi/src/model/general_company_list_user_explore_response_model.dart';
import 'package:openapi/src/model/general_company_param_get_contract_details.dart';
import 'package:openapi/src/model/general_company_register.dart';
import 'package:openapi/src/model/general_company_reject_apply.dart';
import 'package:openapi/src/model/general_company_request_interview_apply.dart';
import 'package:openapi/src/model/general_company_response_model.dart';
import 'package:openapi/src/model/general_company_save_filter.dart';
import 'package:openapi/src/model/general_company_update.dart';
import 'package:openapi/src/model/general_company_update_favorite_user.dart';
import 'package:openapi/src/model/general_company_update_interview_date_time.dart';
import 'package:openapi/src/model/response_model.dart';
import 'package:openapi/src/model/user_explore_details_response_model.dart';

class GeneralCompanyApi {

  final Dio _dio;

  final Serializers _serializers;

  const GeneralCompanyApi(this._dio, this._serializers);

  /// acceptApply
  /// 
  ///
  /// Parameters:
  /// * [data] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [ResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<ResponseModel>> acceptApply({ 
    required GeneralCompanyRequestInterviewApply data,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/apply/request-interview';
    final _options = Options(
      method: r'POST',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    dynamic _bodyData;

    try {
      const _type = FullType(GeneralCompanyRequestInterviewApply);
      _bodyData = _serializers.serialize(data, specifiedType: _type);

    } catch(error, stackTrace) {
      throw DioException(
         requestOptions: _options.compose(
          _dio.options,
          _path,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    ResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(ResponseModel),
      ) as ResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<ResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// addFavoriteUser
  /// 
  ///
  /// Parameters:
  /// * [data] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [ResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<ResponseModel>> addFavoriteUser({ 
    required GeneralCompanyUpdateFavoriteUser data,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/update-favorite';
    final _options = Options(
      method: r'POST',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    dynamic _bodyData;

    try {
      const _type = FullType(GeneralCompanyUpdateFavoriteUser);
      _bodyData = _serializers.serialize(data, specifiedType: _type);

    } catch(error, stackTrace) {
      throw DioException(
         requestOptions: _options.compose(
          _dio.options,
          _path,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    ResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(ResponseModel),
      ) as ResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<ResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// companyDetails
  /// 
  ///
  /// Parameters:
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [GeneralCompanyResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<GeneralCompanyResponseModel>> companyDetails({ 
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/details/';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    GeneralCompanyResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(GeneralCompanyResponseModel),
      ) as GeneralCompanyResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<GeneralCompanyResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// deleteFilter
  /// 
  ///
  /// Parameters:
  /// * [filterId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [ResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<ResponseModel>> deleteFilter({ 
    required String filterId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/saved-filter/{filter_id}/'.replaceAll('{' r'filter_id' '}', encodeQueryParameter(_serializers, filterId, const FullType(String)).toString());
    final _options = Options(
      method: r'DELETE',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    ResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(ResponseModel),
      ) as ResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<ResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// downloadEmployeeContract
  /// 
  ///
  /// Parameters:
  /// * [data] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [Uint8List] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<Uint8List>> downloadEmployeeContract({ 
    required GeneralCompanyParamGetContractDetails data,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/apply/download-employment-contract';
    final _options = Options(
      method: r'POST',
      responseType: ResponseType.bytes,
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    dynamic _bodyData;

    try {
      const _type = FullType(GeneralCompanyParamGetContractDetails);
      _bodyData = _serializers.serialize(data, specifiedType: _type);

    } catch(error, stackTrace) {
      throw DioException(
         requestOptions: _options.compose(
          _dio.options,
          _path,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    Uint8List? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : rawResponse as Uint8List;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<Uint8List>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// exploreUsers
  /// 
  ///
  /// Parameters:
  /// * [filterId] 
  /// * [pageSize] 
  /// * [page] 
  /// * [ordering] 
  /// * [showFavorite] 
  /// * [remoteWorkSkillPointType] 
  /// * [globalSkillPointType] 
  /// * [communicationSkillPointType] 
  /// * [horensoSkillPointType] 
  /// * [projectManagementSkillPointType] 
  /// * [payrollPriceFrom] 
  /// * [payrollPriceTo] 
  /// * [globalWorkExp] 
  /// * [searchType] 
  /// * [searchQuery] 
  /// * [careerType] 
  /// * [skills] 
  /// * [recruitId] 
  /// * [ageFrom] 
  /// * [ageTo] 
  /// * [sexType] 
  /// * [countryCode] 
  /// * [addressCode1] 
  /// * [addressCode2] 
  /// * [lastAcademicCode] 
  /// * [languageCode1] 
  /// * [languageLevelType1] 
  /// * [languageCode2] 
  /// * [languageLevelType2] 
  /// * [experiencedJobCode1] 
  /// * [yearsOfExperience1] 
  /// * [licenceCode1] 
  /// * [licencePoint1] 
  /// * [licenceCode2] 
  /// * [licencePoint2] 
  /// * [licenceCode3] 
  /// * [licencePoint3] 
  /// * [recruitingJobCode] 
  /// * [recruitingEmployCode] 
  /// * [workPlaceCode1] 
  /// * [workPlaceCode2] 
  /// * [workPlaceCode3] 
  /// * [payrollCode] 
  /// * [agentFee] 
  /// * [agentFeeCurrCode] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [GeneralCompanyListUserExploreResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<GeneralCompanyListUserExploreResponseModel>> exploreUsers({ 
    int? filterId,
    int? pageSize,
    int? page,
    String? ordering,
    bool? showFavorite,
    int? remoteWorkSkillPointType,
    int? globalSkillPointType,
    int? communicationSkillPointType,
    int? horensoSkillPointType,
    int? projectManagementSkillPointType,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    int? globalWorkExp,
    String? searchType,
    String? searchQuery,
    int? careerType,
    BuiltList<String>? skills,
    String? recruitId,
    int? ageFrom,
    int? ageTo,
    String? sexType,
    String? countryCode,
    String? addressCode1,
    String? addressCode2,
    String? lastAcademicCode,
    String? languageCode1,
    int? languageLevelType1,
    String? languageCode2,
    int? languageLevelType2,
    String? experiencedJobCode1,
    int? yearsOfExperience1,
    String? licenceCode1,
    int? licencePoint1,
    String? licenceCode2,
    int? licencePoint2,
    String? licenceCode3,
    int? licencePoint3,
    String? recruitingJobCode,
    String? recruitingEmployCode,
    String? workPlaceCode1,
    String? workPlaceCode2,
    String? workPlaceCode3,
    String? payrollCode,
    double? agentFee,
    String? agentFeeCurrCode,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/explore/';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (filterId != null) r'filter_id': encodeQueryParameter(_serializers, filterId, const FullType(int)),
      if (pageSize != null) r'page_size': encodeQueryParameter(_serializers, pageSize, const FullType(int)),
      if (page != null) r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (ordering != null) r'ordering': encodeQueryParameter(_serializers, ordering, const FullType(String)),
      r'show_favorite': encodeQueryParameter(_serializers, showFavorite, const FullType(bool)),
      r'remote_work_skill_point_type': encodeQueryParameter(_serializers, remoteWorkSkillPointType, const FullType(int)),
      r'global_skill_point_type': encodeQueryParameter(_serializers, globalSkillPointType, const FullType(int)),
      r'communication_skill_point_type': encodeQueryParameter(_serializers, communicationSkillPointType, const FullType(int)),
      r'horenso_skill_point_type': encodeQueryParameter(_serializers, horensoSkillPointType, const FullType(int)),
      r'project_management_skill_point_type': encodeQueryParameter(_serializers, projectManagementSkillPointType, const FullType(int)),
      r'payroll_price_from': encodeQueryParameter(_serializers, payrollPriceFrom, const FullType(String)),
      r'payroll_price_to': encodeQueryParameter(_serializers, payrollPriceTo, const FullType(String)),
      r'global_work_exp': encodeQueryParameter(_serializers, globalWorkExp, const FullType(int)),
      r'search_type': encodeQueryParameter(_serializers, searchType, const FullType(String)),
      r'search_query': encodeQueryParameter(_serializers, searchQuery, const FullType(String)),
      r'career_type': encodeQueryParameter(_serializers, careerType, const FullType(int)),
      r'skills': encodeCollectionQueryParameter<String>(_serializers, skills, const FullType(BuiltList, [FullType(String)]), format: ListFormat.csv,),
      r'recruit_id': encodeQueryParameter(_serializers, recruitId, const FullType(String)),
      r'age_from': encodeQueryParameter(_serializers, ageFrom, const FullType(int)),
      r'age_to': encodeQueryParameter(_serializers, ageTo, const FullType(int)),
      r'sex_type': encodeQueryParameter(_serializers, sexType, const FullType(String)),
      r'country_code': encodeQueryParameter(_serializers, countryCode, const FullType(String)),
      r'address_code1': encodeQueryParameter(_serializers, addressCode1, const FullType(String)),
      r'address_code2': encodeQueryParameter(_serializers, addressCode2, const FullType(String)),
      r'last_academic_code': encodeQueryParameter(_serializers, lastAcademicCode, const FullType(String)),
      r'language_code1': encodeQueryParameter(_serializers, languageCode1, const FullType(String)),
      r'language_level_type1': encodeQueryParameter(_serializers, languageLevelType1, const FullType(int)),
      r'language_code2': encodeQueryParameter(_serializers, languageCode2, const FullType(String)),
      r'language_level_type2': encodeQueryParameter(_serializers, languageLevelType2, const FullType(int)),
      r'experienced_job_code1': encodeQueryParameter(_serializers, experiencedJobCode1, const FullType(String)),
      r'years_of_experience1': encodeQueryParameter(_serializers, yearsOfExperience1, const FullType(int)),
      r'licence_code1': encodeQueryParameter(_serializers, licenceCode1, const FullType(String)),
      r'licence_point1': encodeQueryParameter(_serializers, licencePoint1, const FullType(int)),
      r'licence_code2': encodeQueryParameter(_serializers, licenceCode2, const FullType(String)),
      r'licence_point2': encodeQueryParameter(_serializers, licencePoint2, const FullType(int)),
      r'licence_code3': encodeQueryParameter(_serializers, licenceCode3, const FullType(String)),
      r'licence_point3': encodeQueryParameter(_serializers, licencePoint3, const FullType(int)),
      r'recruiting_job_code': encodeQueryParameter(_serializers, recruitingJobCode, const FullType(String)),
      r'recruiting_employ_code': encodeQueryParameter(_serializers, recruitingEmployCode, const FullType(String)),
      r'work_place_code1': encodeQueryParameter(_serializers, workPlaceCode1, const FullType(String)),
      r'work_place_code2': encodeQueryParameter(_serializers, workPlaceCode2, const FullType(String)),
      r'work_place_code3': encodeQueryParameter(_serializers, workPlaceCode3, const FullType(String)),
      r'payroll_code': encodeQueryParameter(_serializers, payrollCode, const FullType(String)),
      r'agent_fee': encodeQueryParameter(_serializers, agentFee, const FullType(double)),
      r'agent_fee_curr_code': encodeQueryParameter(_serializers, agentFeeCurrCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    GeneralCompanyListUserExploreResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(GeneralCompanyListUserExploreResponseModel),
      ) as GeneralCompanyListUserExploreResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<GeneralCompanyListUserExploreResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getApplyDetails
  /// 
  ///
  /// Parameters:
  /// * [applyId] 
  /// * [hostCompanyId] 
  /// * [acceptLanguage] - en,ja,etc
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [GeneralCompanyApplyDetailsResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<GeneralCompanyApplyDetailsResponseModel>> getApplyDetails({ 
    required int applyId,
    int? hostCompanyId,
    String? acceptLanguage,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/apply/details/';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        if (acceptLanguage != null) r'Accept-Language': acceptLanguage,
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'apply_id': encodeQueryParameter(_serializers, applyId, const FullType(int)),
      r'host_company_id': encodeQueryParameter(_serializers, hostCompanyId, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    GeneralCompanyApplyDetailsResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(GeneralCompanyApplyDetailsResponseModel),
      ) as GeneralCompanyApplyDetailsResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<GeneralCompanyApplyDetailsResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getCalendarInterview
  /// 
  ///
  /// Parameters:
  /// * [date] 
  /// * [hostCompanyId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [GeneralCompanyCalendarInterviewResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<GeneralCompanyCalendarInterviewResponseModel>> getCalendarInterview({ 
    required Date date,
    int? hostCompanyId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/apply/get-calendar-interview';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'date': encodeQueryParameter(_serializers, date, const FullType(Date)),
      r'host_company_id': encodeQueryParameter(_serializers, hostCompanyId, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    GeneralCompanyCalendarInterviewResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(GeneralCompanyCalendarInterviewResponseModel),
      ) as GeneralCompanyCalendarInterviewResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<GeneralCompanyCalendarInterviewResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getCommunicationSkillsSelfAssessmentEngineer
  /// 
  ///
  /// Parameters:
  /// * [userId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [AssessmentQuestionsResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<AssessmentQuestionsResponseModel>> getCommunicationSkillsSelfAssessmentEngineer({ 
    required String userId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/engineer-details/get-communication-skills-self-assessment-engineer/{user_id}/'.replaceAll('{' r'user_id' '}', encodeQueryParameter(_serializers, userId, const FullType(String)).toString());
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    AssessmentQuestionsResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(AssessmentQuestionsResponseModel),
      ) as AssessmentQuestionsResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<AssessmentQuestionsResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getCompanyContractDetails
  /// 
  ///
  /// Parameters:
  /// * [applyId] 
  /// * [hostCompanyId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [GeneralCompanyContractDetailsResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<GeneralCompanyContractDetailsResponseModel>> getCompanyContractDetails({ 
    required int applyId,
    int? hostCompanyId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/apply/get-contract-details';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'apply_id': encodeQueryParameter(_serializers, applyId, const FullType(int)),
      r'host_company_id': encodeQueryParameter(_serializers, hostCompanyId, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    GeneralCompanyContractDetailsResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(GeneralCompanyContractDetailsResponseModel),
      ) as GeneralCompanyContractDetailsResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<GeneralCompanyContractDetailsResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getExploreUserCount
  /// 
  ///
  /// Parameters:
  /// * [filterId] 
  /// * [pageSize] 
  /// * [page] 
  /// * [ordering] 
  /// * [showFavorite] 
  /// * [remoteWorkSkillPointType] 
  /// * [globalSkillPointType] 
  /// * [communicationSkillPointType] 
  /// * [horensoSkillPointType] 
  /// * [projectManagementSkillPointType] 
  /// * [payrollPriceFrom] 
  /// * [payrollPriceTo] 
  /// * [globalWorkExp] 
  /// * [searchType] 
  /// * [searchQuery] 
  /// * [careerType] 
  /// * [skills] 
  /// * [recruitId] 
  /// * [ageFrom] 
  /// * [ageTo] 
  /// * [sexType] 
  /// * [countryCode] 
  /// * [addressCode1] 
  /// * [addressCode2] 
  /// * [lastAcademicCode] 
  /// * [languageCode1] 
  /// * [languageLevelType1] 
  /// * [languageCode2] 
  /// * [languageLevelType2] 
  /// * [experiencedJobCode1] 
  /// * [yearsOfExperience1] 
  /// * [licenceCode1] 
  /// * [licencePoint1] 
  /// * [licenceCode2] 
  /// * [licencePoint2] 
  /// * [licenceCode3] 
  /// * [licencePoint3] 
  /// * [recruitingJobCode] 
  /// * [recruitingEmployCode] 
  /// * [workPlaceCode1] 
  /// * [workPlaceCode2] 
  /// * [workPlaceCode3] 
  /// * [payrollCode] 
  /// * [agentFee] 
  /// * [agentFeeCurrCode] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [GeneralCompanyGetUserExploreCountResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<GeneralCompanyGetUserExploreCountResponseModel>> getExploreUserCount({ 
    int? filterId,
    int? pageSize,
    int? page,
    String? ordering,
    bool? showFavorite,
    int? remoteWorkSkillPointType,
    int? globalSkillPointType,
    int? communicationSkillPointType,
    int? horensoSkillPointType,
    int? projectManagementSkillPointType,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    int? globalWorkExp,
    String? searchType,
    String? searchQuery,
    int? careerType,
    BuiltList<String>? skills,
    String? recruitId,
    int? ageFrom,
    int? ageTo,
    String? sexType,
    String? countryCode,
    String? addressCode1,
    String? addressCode2,
    String? lastAcademicCode,
    String? languageCode1,
    int? languageLevelType1,
    String? languageCode2,
    int? languageLevelType2,
    String? experiencedJobCode1,
    int? yearsOfExperience1,
    String? licenceCode1,
    int? licencePoint1,
    String? licenceCode2,
    int? licencePoint2,
    String? licenceCode3,
    int? licencePoint3,
    String? recruitingJobCode,
    String? recruitingEmployCode,
    String? workPlaceCode1,
    String? workPlaceCode2,
    String? workPlaceCode3,
    String? payrollCode,
    double? agentFee,
    String? agentFeeCurrCode,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/get-explore-user-count';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (filterId != null) r'filter_id': encodeQueryParameter(_serializers, filterId, const FullType(int)),
      if (pageSize != null) r'page_size': encodeQueryParameter(_serializers, pageSize, const FullType(int)),
      if (page != null) r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (ordering != null) r'ordering': encodeQueryParameter(_serializers, ordering, const FullType(String)),
      r'show_favorite': encodeQueryParameter(_serializers, showFavorite, const FullType(bool)),
      r'remote_work_skill_point_type': encodeQueryParameter(_serializers, remoteWorkSkillPointType, const FullType(int)),
      r'global_skill_point_type': encodeQueryParameter(_serializers, globalSkillPointType, const FullType(int)),
      r'communication_skill_point_type': encodeQueryParameter(_serializers, communicationSkillPointType, const FullType(int)),
      r'horenso_skill_point_type': encodeQueryParameter(_serializers, horensoSkillPointType, const FullType(int)),
      r'project_management_skill_point_type': encodeQueryParameter(_serializers, projectManagementSkillPointType, const FullType(int)),
      r'payroll_price_from': encodeQueryParameter(_serializers, payrollPriceFrom, const FullType(String)),
      r'payroll_price_to': encodeQueryParameter(_serializers, payrollPriceTo, const FullType(String)),
      r'global_work_exp': encodeQueryParameter(_serializers, globalWorkExp, const FullType(int)),
      r'search_type': encodeQueryParameter(_serializers, searchType, const FullType(String)),
      r'search_query': encodeQueryParameter(_serializers, searchQuery, const FullType(String)),
      r'career_type': encodeQueryParameter(_serializers, careerType, const FullType(int)),
      r'skills': encodeCollectionQueryParameter<String>(_serializers, skills, const FullType(BuiltList, [FullType(String)]), format: ListFormat.csv,),
      r'recruit_id': encodeQueryParameter(_serializers, recruitId, const FullType(String)),
      r'age_from': encodeQueryParameter(_serializers, ageFrom, const FullType(int)),
      r'age_to': encodeQueryParameter(_serializers, ageTo, const FullType(int)),
      r'sex_type': encodeQueryParameter(_serializers, sexType, const FullType(String)),
      r'country_code': encodeQueryParameter(_serializers, countryCode, const FullType(String)),
      r'address_code1': encodeQueryParameter(_serializers, addressCode1, const FullType(String)),
      r'address_code2': encodeQueryParameter(_serializers, addressCode2, const FullType(String)),
      r'last_academic_code': encodeQueryParameter(_serializers, lastAcademicCode, const FullType(String)),
      r'language_code1': encodeQueryParameter(_serializers, languageCode1, const FullType(String)),
      r'language_level_type1': encodeQueryParameter(_serializers, languageLevelType1, const FullType(int)),
      r'language_code2': encodeQueryParameter(_serializers, languageCode2, const FullType(String)),
      r'language_level_type2': encodeQueryParameter(_serializers, languageLevelType2, const FullType(int)),
      r'experienced_job_code1': encodeQueryParameter(_serializers, experiencedJobCode1, const FullType(String)),
      r'years_of_experience1': encodeQueryParameter(_serializers, yearsOfExperience1, const FullType(int)),
      r'licence_code1': encodeQueryParameter(_serializers, licenceCode1, const FullType(String)),
      r'licence_point1': encodeQueryParameter(_serializers, licencePoint1, const FullType(int)),
      r'licence_code2': encodeQueryParameter(_serializers, licenceCode2, const FullType(String)),
      r'licence_point2': encodeQueryParameter(_serializers, licencePoint2, const FullType(int)),
      r'licence_code3': encodeQueryParameter(_serializers, licenceCode3, const FullType(String)),
      r'licence_point3': encodeQueryParameter(_serializers, licencePoint3, const FullType(int)),
      r'recruiting_job_code': encodeQueryParameter(_serializers, recruitingJobCode, const FullType(String)),
      r'recruiting_employ_code': encodeQueryParameter(_serializers, recruitingEmployCode, const FullType(String)),
      r'work_place_code1': encodeQueryParameter(_serializers, workPlaceCode1, const FullType(String)),
      r'work_place_code2': encodeQueryParameter(_serializers, workPlaceCode2, const FullType(String)),
      r'work_place_code3': encodeQueryParameter(_serializers, workPlaceCode3, const FullType(String)),
      r'payroll_code': encodeQueryParameter(_serializers, payrollCode, const FullType(String)),
      r'agent_fee': encodeQueryParameter(_serializers, agentFee, const FullType(double)),
      r'agent_fee_curr_code': encodeQueryParameter(_serializers, agentFeeCurrCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    GeneralCompanyGetUserExploreCountResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(GeneralCompanyGetUserExploreCountResponseModel),
      ) as GeneralCompanyGetUserExploreCountResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<GeneralCompanyGetUserExploreCountResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getGlobalResponsivenessSkillsEngineer
  /// 
  ///
  /// Parameters:
  /// * [userId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [AssessmentQuestionsResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<AssessmentQuestionsResponseModel>> getGlobalResponsivenessSkillsEngineer({ 
    required String userId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/engineer-details/get-global-responsiveness-skills-engineer/{user_id}/'.replaceAll('{' r'user_id' '}', encodeQueryParameter(_serializers, userId, const FullType(String)).toString());
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    AssessmentQuestionsResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(AssessmentQuestionsResponseModel),
      ) as AssessmentQuestionsResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<AssessmentQuestionsResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getListGroupChat
  /// 
  ///
  /// Parameters:
  /// * [pageSize] 
  /// * [cursor] 
  /// * [ordering] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [GeneralCompanyListGroupChatResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<GeneralCompanyListGroupChatResponseModel>> getListGroupChat({ 
    int? pageSize,
    String? cursor,
    String? ordering,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/get-list-group-chat/';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (pageSize != null) r'page_size': encodeQueryParameter(_serializers, pageSize, const FullType(int)),
      if (cursor != null) r'cursor': encodeQueryParameter(_serializers, cursor, const FullType(String)),
      if (ordering != null) r'ordering': encodeQueryParameter(_serializers, ordering, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    GeneralCompanyListGroupChatResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(GeneralCompanyListGroupChatResponseModel),
      ) as GeneralCompanyListGroupChatResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<GeneralCompanyListGroupChatResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getProjectManagementSkillsSelfEvaluationEngineer
  /// 
  ///
  /// Parameters:
  /// * [userId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [AssessmentQuestionsResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<AssessmentQuestionsResponseModel>> getProjectManagementSkillsSelfEvaluationEngineer({ 
    required String userId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/engineer-details/get-project-management-skills-self-evaluation-engineer/{user_id}/'.replaceAll('{' r'user_id' '}', encodeQueryParameter(_serializers, userId, const FullType(String)).toString());
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    AssessmentQuestionsResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(AssessmentQuestionsResponseModel),
      ) as AssessmentQuestionsResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<AssessmentQuestionsResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getRemoteWorkSkillsEngineer
  /// 
  ///
  /// Parameters:
  /// * [userId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [AssessmentQuestionsResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<AssessmentQuestionsResponseModel>> getRemoteWorkSkillsEngineer({ 
    required String userId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/engineer-details/get-remote-work-skills-engineer/{user_id}/'.replaceAll('{' r'user_id' '}', encodeQueryParameter(_serializers, userId, const FullType(String)).toString());
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    AssessmentQuestionsResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(AssessmentQuestionsResponseModel),
      ) as AssessmentQuestionsResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<AssessmentQuestionsResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getReportingConsultationSkillsSelfEvaluationEngineer
  /// 
  ///
  /// Parameters:
  /// * [userId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [AssessmentQuestionsResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<AssessmentQuestionsResponseModel>> getReportingConsultationSkillsSelfEvaluationEngineer({ 
    required String userId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/engineer-details/get-reporting-consultation-skills-self-evaluation-engineer/{user_id}/'.replaceAll('{' r'user_id' '}', encodeQueryParameter(_serializers, userId, const FullType(String)).toString());
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    AssessmentQuestionsResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(AssessmentQuestionsResponseModel),
      ) as AssessmentQuestionsResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<AssessmentQuestionsResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getSavedFilters
  /// 
  ///
  /// Parameters:
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [GeneralCompanyListFilterResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<GeneralCompanyListFilterResponseModel>> getSavedFilters({ 
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/saved-filters/';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    GeneralCompanyListFilterResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(GeneralCompanyListFilterResponseModel),
      ) as GeneralCompanyListFilterResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<GeneralCompanyListFilterResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getUserDetails
  /// 
  ///
  /// Parameters:
  /// * [userId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [UserExploreDetailsResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<UserExploreDetailsResponseModel>> getUserDetails({ 
    required String userId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/engineer-details/{user_id}/'.replaceAll('{' r'user_id' '}', encodeQueryParameter(_serializers, userId, const FullType(String)).toString());
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    UserExploreDetailsResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(UserExploreDetailsResponseModel),
      ) as UserExploreDetailsResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<UserExploreDetailsResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// listAppliedEngineers
  /// 
  ///
  /// Parameters:
  /// * [recruitIds] 
  /// * [ordering] 
  /// * [applyStatusFilterCodes] 
  /// * [search] 
  /// * [hostCompanyId] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [GeneralCompanyListUserAppliedCompanyResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<GeneralCompanyListUserAppliedCompanyResponseModel>> listAppliedEngineers({ 
    BuiltList<int>? recruitIds,
    String? ordering,
    BuiltList<int>? applyStatusFilterCodes,
    String? search,
    int? hostCompanyId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/list-applied-engineers/';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'recruit_ids': encodeCollectionQueryParameter<int>(_serializers, recruitIds, const FullType(BuiltList, [FullType(int)]), format: ListFormat.csv,),
      if (ordering != null) r'ordering': encodeQueryParameter(_serializers, ordering, const FullType(String)),
      r'apply_status_filter_codes': encodeCollectionQueryParameter<int>(_serializers, applyStatusFilterCodes, const FullType(BuiltList, [FullType(int)]), format: ListFormat.csv,),
      if (search != null) r'search': encodeQueryParameter(_serializers, search, const FullType(String)),
      r'host_company_id': encodeQueryParameter(_serializers, hostCompanyId, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    GeneralCompanyListUserAppliedCompanyResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(GeneralCompanyListUserAppliedCompanyResponseModel),
      ) as GeneralCompanyListUserAppliedCompanyResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<GeneralCompanyListUserAppliedCompanyResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// registerCompany
  /// 
  ///
  /// Parameters:
  /// * [data] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [ResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<ResponseModel>> registerCompany({ 
    required GeneralCompanyRegister data,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/register/';
    final _options = Options(
      method: r'POST',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    dynamic _bodyData;

    try {
      const _type = FullType(GeneralCompanyRegister);
      _bodyData = _serializers.serialize(data, specifiedType: _type);

    } catch(error, stackTrace) {
      throw DioException(
         requestOptions: _options.compose(
          _dio.options,
          _path,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    ResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(ResponseModel),
      ) as ResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<ResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// rejectApply
  /// 
  ///
  /// Parameters:
  /// * [data] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [ResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<ResponseModel>> rejectApply({ 
    required GeneralCompanyRejectApply data,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/apply/reject/';
    final _options = Options(
      method: r'POST',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    dynamic _bodyData;

    try {
      const _type = FullType(GeneralCompanyRejectApply);
      _bodyData = _serializers.serialize(data, specifiedType: _type);

    } catch(error, stackTrace) {
      throw DioException(
         requestOptions: _options.compose(
          _dio.options,
          _path,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    ResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(ResponseModel),
      ) as ResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<ResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// saveFilter
  /// 
  ///
  /// Parameters:
  /// * [data] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [ResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<ResponseModel>> saveFilter({ 
    required GeneralCompanySaveFilter data,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/save-filter/';
    final _options = Options(
      method: r'POST',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    dynamic _bodyData;

    try {
      const _type = FullType(GeneralCompanySaveFilter);
      _bodyData = _serializers.serialize(data, specifiedType: _type);

    } catch(error, stackTrace) {
      throw DioException(
         requestOptions: _options.compose(
          _dio.options,
          _path,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    ResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(ResponseModel),
      ) as ResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<ResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// updateCompany
  /// 
  ///
  /// Parameters:
  /// * [data] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [ResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<ResponseModel>> updateCompany({ 
    required GeneralCompanyUpdate data,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/details/';
    final _options = Options(
      method: r'PATCH',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    dynamic _bodyData;

    try {
      const _type = FullType(GeneralCompanyUpdate);
      _bodyData = _serializers.serialize(data, specifiedType: _type);

    } catch(error, stackTrace) {
      throw DioException(
         requestOptions: _options.compose(
          _dio.options,
          _path,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    ResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(ResponseModel),
      ) as ResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<ResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// updateInterviewDate
  /// 
  ///
  /// Parameters:
  /// * [data] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [ResponseModel] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<ResponseModel>> updateInterviewDate({ 
    required GeneralCompanyUpdateInterviewDateTime data,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/general-company/apply/update-interview-datetime';
    final _options = Options(
      method: r'POST',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'apiKey',
            'name': 'Bearer',
            'keyName': 'Authorization',
            'where': 'header',
          },
        ],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    dynamic _bodyData;

    try {
      const _type = FullType(GeneralCompanyUpdateInterviewDateTime);
      _bodyData = _serializers.serialize(data, specifiedType: _type);

    } catch(error, stackTrace) {
      throw DioException(
         requestOptions: _options.compose(
          _dio.options,
          _path,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    ResponseModel? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(ResponseModel),
      ) as ResponseModel;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<ResponseModel>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

}
