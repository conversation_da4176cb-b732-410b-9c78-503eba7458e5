// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_applied_engineers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyAppliedEngineers extends GeneralCompanyAppliedEngineers {
  @override
  final int? applyId;
  @override
  final int recruitId;
  @override
  final int? groupId;
  @override
  final String? title;
  @override
  final int? engineerId;
  @override
  final int? hostCompanyId;
  @override
  final DateTime? progressUpdateDatetime;
  @override
  final int? recruitProgressCode;
  @override
  final int? engineerAcceptSignId;
  @override
  final DateTime? interviewDatetime;
  @override
  final bool? isNew;
  @override
  final int? unreadMessageCount;
  @override
  final GeneralCompanyUserAppliedCompany engineer;
  @override
  final DateTime? created;

  factory _$GeneralCompanyAppliedEngineers(
          [void Function(GeneralCompanyAppliedEngineersBuilder)? updates]) =>
      (new GeneralCompanyAppliedEngineersBuilder()..update(updates))._build();

  _$GeneralCompanyAppliedEngineers._(
      {this.applyId,
      required this.recruitId,
      this.groupId,
      this.title,
      this.engineerId,
      this.hostCompanyId,
      this.progressUpdateDatetime,
      this.recruitProgressCode,
      this.engineerAcceptSignId,
      this.interviewDatetime,
      this.isNew,
      this.unreadMessageCount,
      required this.engineer,
      this.created})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        recruitId, r'GeneralCompanyAppliedEngineers', 'recruitId');
    BuiltValueNullFieldError.checkNotNull(
        engineer, r'GeneralCompanyAppliedEngineers', 'engineer');
  }

  @override
  GeneralCompanyAppliedEngineers rebuild(
          void Function(GeneralCompanyAppliedEngineersBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyAppliedEngineersBuilder toBuilder() =>
      new GeneralCompanyAppliedEngineersBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyAppliedEngineers &&
        applyId == other.applyId &&
        recruitId == other.recruitId &&
        groupId == other.groupId &&
        title == other.title &&
        engineerId == other.engineerId &&
        hostCompanyId == other.hostCompanyId &&
        progressUpdateDatetime == other.progressUpdateDatetime &&
        recruitProgressCode == other.recruitProgressCode &&
        engineerAcceptSignId == other.engineerAcceptSignId &&
        interviewDatetime == other.interviewDatetime &&
        isNew == other.isNew &&
        unreadMessageCount == other.unreadMessageCount &&
        engineer == other.engineer &&
        created == other.created;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, applyId.hashCode);
    _$hash = $jc(_$hash, recruitId.hashCode);
    _$hash = $jc(_$hash, groupId.hashCode);
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, engineerId.hashCode);
    _$hash = $jc(_$hash, hostCompanyId.hashCode);
    _$hash = $jc(_$hash, progressUpdateDatetime.hashCode);
    _$hash = $jc(_$hash, recruitProgressCode.hashCode);
    _$hash = $jc(_$hash, engineerAcceptSignId.hashCode);
    _$hash = $jc(_$hash, interviewDatetime.hashCode);
    _$hash = $jc(_$hash, isNew.hashCode);
    _$hash = $jc(_$hash, unreadMessageCount.hashCode);
    _$hash = $jc(_$hash, engineer.hashCode);
    _$hash = $jc(_$hash, created.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyAppliedEngineers')
          ..add('applyId', applyId)
          ..add('recruitId', recruitId)
          ..add('groupId', groupId)
          ..add('title', title)
          ..add('engineerId', engineerId)
          ..add('hostCompanyId', hostCompanyId)
          ..add('progressUpdateDatetime', progressUpdateDatetime)
          ..add('recruitProgressCode', recruitProgressCode)
          ..add('engineerAcceptSignId', engineerAcceptSignId)
          ..add('interviewDatetime', interviewDatetime)
          ..add('isNew', isNew)
          ..add('unreadMessageCount', unreadMessageCount)
          ..add('engineer', engineer)
          ..add('created', created))
        .toString();
  }
}

class GeneralCompanyAppliedEngineersBuilder
    implements
        Builder<GeneralCompanyAppliedEngineers,
            GeneralCompanyAppliedEngineersBuilder> {
  _$GeneralCompanyAppliedEngineers? _$v;

  int? _applyId;
  int? get applyId => _$this._applyId;
  set applyId(int? applyId) => _$this._applyId = applyId;

  int? _recruitId;
  int? get recruitId => _$this._recruitId;
  set recruitId(int? recruitId) => _$this._recruitId = recruitId;

  int? _groupId;
  int? get groupId => _$this._groupId;
  set groupId(int? groupId) => _$this._groupId = groupId;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  int? _engineerId;
  int? get engineerId => _$this._engineerId;
  set engineerId(int? engineerId) => _$this._engineerId = engineerId;

  int? _hostCompanyId;
  int? get hostCompanyId => _$this._hostCompanyId;
  set hostCompanyId(int? hostCompanyId) =>
      _$this._hostCompanyId = hostCompanyId;

  DateTime? _progressUpdateDatetime;
  DateTime? get progressUpdateDatetime => _$this._progressUpdateDatetime;
  set progressUpdateDatetime(DateTime? progressUpdateDatetime) =>
      _$this._progressUpdateDatetime = progressUpdateDatetime;

  int? _recruitProgressCode;
  int? get recruitProgressCode => _$this._recruitProgressCode;
  set recruitProgressCode(int? recruitProgressCode) =>
      _$this._recruitProgressCode = recruitProgressCode;

  int? _engineerAcceptSignId;
  int? get engineerAcceptSignId => _$this._engineerAcceptSignId;
  set engineerAcceptSignId(int? engineerAcceptSignId) =>
      _$this._engineerAcceptSignId = engineerAcceptSignId;

  DateTime? _interviewDatetime;
  DateTime? get interviewDatetime => _$this._interviewDatetime;
  set interviewDatetime(DateTime? interviewDatetime) =>
      _$this._interviewDatetime = interviewDatetime;

  bool? _isNew;
  bool? get isNew => _$this._isNew;
  set isNew(bool? isNew) => _$this._isNew = isNew;

  int? _unreadMessageCount;
  int? get unreadMessageCount => _$this._unreadMessageCount;
  set unreadMessageCount(int? unreadMessageCount) =>
      _$this._unreadMessageCount = unreadMessageCount;

  GeneralCompanyUserAppliedCompanyBuilder? _engineer;
  GeneralCompanyUserAppliedCompanyBuilder get engineer =>
      _$this._engineer ??= new GeneralCompanyUserAppliedCompanyBuilder();
  set engineer(GeneralCompanyUserAppliedCompanyBuilder? engineer) =>
      _$this._engineer = engineer;

  DateTime? _created;
  DateTime? get created => _$this._created;
  set created(DateTime? created) => _$this._created = created;

  GeneralCompanyAppliedEngineersBuilder() {
    GeneralCompanyAppliedEngineers._defaults(this);
  }

  GeneralCompanyAppliedEngineersBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _applyId = $v.applyId;
      _recruitId = $v.recruitId;
      _groupId = $v.groupId;
      _title = $v.title;
      _engineerId = $v.engineerId;
      _hostCompanyId = $v.hostCompanyId;
      _progressUpdateDatetime = $v.progressUpdateDatetime;
      _recruitProgressCode = $v.recruitProgressCode;
      _engineerAcceptSignId = $v.engineerAcceptSignId;
      _interviewDatetime = $v.interviewDatetime;
      _isNew = $v.isNew;
      _unreadMessageCount = $v.unreadMessageCount;
      _engineer = $v.engineer.toBuilder();
      _created = $v.created;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyAppliedEngineers other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyAppliedEngineers;
  }

  @override
  void update(void Function(GeneralCompanyAppliedEngineersBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyAppliedEngineers build() => _build();

  _$GeneralCompanyAppliedEngineers _build() {
    _$GeneralCompanyAppliedEngineers _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyAppliedEngineers._(
            applyId: applyId,
            recruitId: BuiltValueNullFieldError.checkNotNull(
                recruitId, r'GeneralCompanyAppliedEngineers', 'recruitId'),
            groupId: groupId,
            title: title,
            engineerId: engineerId,
            hostCompanyId: hostCompanyId,
            progressUpdateDatetime: progressUpdateDatetime,
            recruitProgressCode: recruitProgressCode,
            engineerAcceptSignId: engineerAcceptSignId,
            interviewDatetime: interviewDatetime,
            isNew: isNew,
            unreadMessageCount: unreadMessageCount,
            engineer: engineer.build(),
            created: created,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'engineer';
        engineer.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyAppliedEngineers', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
