//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:openapi/src/model/general_company_user_applied_company.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_applied_engineers.g.dart';

/// GeneralCompanyAppliedEngineers
///
/// Properties:
/// * [applyId] 
/// * [recruitId] 
/// * [groupId] 
/// * [title] 
/// * [engineerId] 
/// * [hostCompanyId] 
/// * [progressUpdateDatetime] 
/// * [recruitProgressCode] 
/// * [engineerAcceptSignId] 
/// * [interviewDatetime] 
/// * [isNew] 
/// * [unreadMessageCount] 
/// * [engineer] 
/// * [created] 
@BuiltValue()
abstract class GeneralCompanyAppliedEngineers implements Built<GeneralCompanyAppliedEngineers, GeneralCompanyAppliedEngineersBuilder> {
  @BuiltValueField(wireName: r'apply_id')
  int? get applyId;

  @BuiltValueField(wireName: r'recruit_id')
  int get recruitId;

  @BuiltValueField(wireName: r'group_id')
  int? get groupId;

  @BuiltValueField(wireName: r'title')
  String? get title;

  @BuiltValueField(wireName: r'engineer_id')
  int? get engineerId;

  @BuiltValueField(wireName: r'host_company_id')
  int? get hostCompanyId;

  @BuiltValueField(wireName: r'progress_update_datetime')
  DateTime? get progressUpdateDatetime;

  @BuiltValueField(wireName: r'recruit_progress_code')
  int? get recruitProgressCode;

  @BuiltValueField(wireName: r'engineer_accept_sign_id')
  int? get engineerAcceptSignId;

  @BuiltValueField(wireName: r'interview_datetime')
  DateTime? get interviewDatetime;

  @BuiltValueField(wireName: r'is_new')
  bool? get isNew;

  @BuiltValueField(wireName: r'unread_message_count')
  int? get unreadMessageCount;

  @BuiltValueField(wireName: r'engineer')
  GeneralCompanyUserAppliedCompany get engineer;

  @BuiltValueField(wireName: r'created')
  DateTime? get created;

  GeneralCompanyAppliedEngineers._();

  factory GeneralCompanyAppliedEngineers([void updates(GeneralCompanyAppliedEngineersBuilder b)]) = _$GeneralCompanyAppliedEngineers;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyAppliedEngineersBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyAppliedEngineers> get serializer => _$GeneralCompanyAppliedEngineersSerializer();
}

class _$GeneralCompanyAppliedEngineersSerializer implements PrimitiveSerializer<GeneralCompanyAppliedEngineers> {
  @override
  final Iterable<Type> types = const [GeneralCompanyAppliedEngineers, _$GeneralCompanyAppliedEngineers];

  @override
  final String wireName = r'GeneralCompanyAppliedEngineers';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyAppliedEngineers object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.applyId != null) {
      yield r'apply_id';
      yield serializers.serialize(
        object.applyId,
        specifiedType: const FullType(int),
      );
    }
    yield r'recruit_id';
    yield serializers.serialize(
      object.recruitId,
      specifiedType: const FullType(int),
    );
    yield r'group_id';
    yield object.groupId == null ? null : serializers.serialize(
      object.groupId,
      specifiedType: const FullType.nullable(int),
    );
    if (object.title != null) {
      yield r'title';
      yield serializers.serialize(
        object.title,
        specifiedType: const FullType(String),
      );
    }
    yield r'engineer_id';
    yield object.engineerId == null ? null : serializers.serialize(
      object.engineerId,
      specifiedType: const FullType.nullable(int),
    );
    yield r'host_company_id';
    yield object.hostCompanyId == null ? null : serializers.serialize(
      object.hostCompanyId,
      specifiedType: const FullType.nullable(int),
    );
    if (object.progressUpdateDatetime != null) {
      yield r'progress_update_datetime';
      yield serializers.serialize(
        object.progressUpdateDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.recruitProgressCode != null) {
      yield r'recruit_progress_code';
      yield serializers.serialize(
        object.recruitProgressCode,
        specifiedType: const FullType.nullable(int),
      );
    }
    yield r'engineer_accept_sign_id';
    yield object.engineerAcceptSignId == null ? null : serializers.serialize(
      object.engineerAcceptSignId,
      specifiedType: const FullType.nullable(int),
    );
    if (object.interviewDatetime != null) {
      yield r'interview_datetime';
      yield serializers.serialize(
        object.interviewDatetime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.isNew != null) {
      yield r'is_new';
      yield serializers.serialize(
        object.isNew,
        specifiedType: const FullType(bool),
      );
    }
    if (object.unreadMessageCount != null) {
      yield r'unread_message_count';
      yield serializers.serialize(
        object.unreadMessageCount,
        specifiedType: const FullType(int),
      );
    }
    yield r'engineer';
    yield serializers.serialize(
      object.engineer,
      specifiedType: const FullType(GeneralCompanyUserAppliedCompany),
    );
    if (object.created != null) {
      yield r'created';
      yield serializers.serialize(
        object.created,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyAppliedEngineers object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyAppliedEngineersBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'apply_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.applyId = valueDes;
          break;
        case r'recruit_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.recruitId = valueDes;
          break;
        case r'group_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.groupId = valueDes;
          break;
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.title = valueDes;
          break;
        case r'engineer_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.engineerId = valueDes;
          break;
        case r'host_company_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.hostCompanyId = valueDes;
          break;
        case r'progress_update_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.progressUpdateDatetime = valueDes;
          break;
        case r'recruit_progress_code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.recruitProgressCode = valueDes;
          break;
        case r'engineer_accept_sign_id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.engineerAcceptSignId = valueDes;
          break;
        case r'interview_datetime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.interviewDatetime = valueDes;
          break;
        case r'is_new':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.isNew = valueDes;
          break;
        case r'unread_message_count':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.unreadMessageCount = valueDes;
          break;
        case r'engineer':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompanyUserAppliedCompany),
          ) as GeneralCompanyUserAppliedCompany;
          result.engineer.replace(valueDes);
          break;
        case r'created':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.created = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyAppliedEngineers deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyAppliedEngineersBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

