// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_company_user_applied_company.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GeneralCompanyUserAppliedCompany
    extends GeneralCompanyUserAppliedCompany {
  @override
  final int? userId;
  @override
  final String? profileImagePath;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final Date? birthDate;
  @override
  final int? age;
  @override
  final String? countryCode;
  @override
  final String? nickname;
  @override
  final BuiltList<EngSkill>? skills;
  @override
  final int? sexType;
  @override
  final int? totalCompanyChangedCount;
  @override
  final EngHope? requirements;

  factory _$GeneralCompanyUserAppliedCompany(
          [void Function(GeneralCompanyUserAppliedCompanyBuilder)? updates]) =>
      (new GeneralCompanyUserAppliedCompanyBuilder()..update(updates))._build();

  _$GeneralCompanyUserAppliedCompany._(
      {this.userId,
      this.profileImagePath,
      this.firstName,
      this.lastName,
      this.birthDate,
      this.age,
      this.countryCode,
      this.nickname,
      this.skills,
      this.sexType,
      this.totalCompanyChangedCount,
      this.requirements})
      : super._();

  @override
  GeneralCompanyUserAppliedCompany rebuild(
          void Function(GeneralCompanyUserAppliedCompanyBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GeneralCompanyUserAppliedCompanyBuilder toBuilder() =>
      new GeneralCompanyUserAppliedCompanyBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GeneralCompanyUserAppliedCompany &&
        userId == other.userId &&
        profileImagePath == other.profileImagePath &&
        firstName == other.firstName &&
        lastName == other.lastName &&
        birthDate == other.birthDate &&
        age == other.age &&
        countryCode == other.countryCode &&
        nickname == other.nickname &&
        skills == other.skills &&
        sexType == other.sexType &&
        totalCompanyChangedCount == other.totalCompanyChangedCount &&
        requirements == other.requirements;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, profileImagePath.hashCode);
    _$hash = $jc(_$hash, firstName.hashCode);
    _$hash = $jc(_$hash, lastName.hashCode);
    _$hash = $jc(_$hash, birthDate.hashCode);
    _$hash = $jc(_$hash, age.hashCode);
    _$hash = $jc(_$hash, countryCode.hashCode);
    _$hash = $jc(_$hash, nickname.hashCode);
    _$hash = $jc(_$hash, skills.hashCode);
    _$hash = $jc(_$hash, sexType.hashCode);
    _$hash = $jc(_$hash, totalCompanyChangedCount.hashCode);
    _$hash = $jc(_$hash, requirements.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GeneralCompanyUserAppliedCompany')
          ..add('userId', userId)
          ..add('profileImagePath', profileImagePath)
          ..add('firstName', firstName)
          ..add('lastName', lastName)
          ..add('birthDate', birthDate)
          ..add('age', age)
          ..add('countryCode', countryCode)
          ..add('nickname', nickname)
          ..add('skills', skills)
          ..add('sexType', sexType)
          ..add('totalCompanyChangedCount', totalCompanyChangedCount)
          ..add('requirements', requirements))
        .toString();
  }
}

class GeneralCompanyUserAppliedCompanyBuilder
    implements
        Builder<GeneralCompanyUserAppliedCompany,
            GeneralCompanyUserAppliedCompanyBuilder> {
  _$GeneralCompanyUserAppliedCompany? _$v;

  int? _userId;
  int? get userId => _$this._userId;
  set userId(int? userId) => _$this._userId = userId;

  String? _profileImagePath;
  String? get profileImagePath => _$this._profileImagePath;
  set profileImagePath(String? profileImagePath) =>
      _$this._profileImagePath = profileImagePath;

  String? _firstName;
  String? get firstName => _$this._firstName;
  set firstName(String? firstName) => _$this._firstName = firstName;

  String? _lastName;
  String? get lastName => _$this._lastName;
  set lastName(String? lastName) => _$this._lastName = lastName;

  Date? _birthDate;
  Date? get birthDate => _$this._birthDate;
  set birthDate(Date? birthDate) => _$this._birthDate = birthDate;

  int? _age;
  int? get age => _$this._age;
  set age(int? age) => _$this._age = age;

  String? _countryCode;
  String? get countryCode => _$this._countryCode;
  set countryCode(String? countryCode) => _$this._countryCode = countryCode;

  String? _nickname;
  String? get nickname => _$this._nickname;
  set nickname(String? nickname) => _$this._nickname = nickname;

  ListBuilder<EngSkill>? _skills;
  ListBuilder<EngSkill> get skills =>
      _$this._skills ??= new ListBuilder<EngSkill>();
  set skills(ListBuilder<EngSkill>? skills) => _$this._skills = skills;

  int? _sexType;
  int? get sexType => _$this._sexType;
  set sexType(int? sexType) => _$this._sexType = sexType;

  int? _totalCompanyChangedCount;
  int? get totalCompanyChangedCount => _$this._totalCompanyChangedCount;
  set totalCompanyChangedCount(int? totalCompanyChangedCount) =>
      _$this._totalCompanyChangedCount = totalCompanyChangedCount;

  EngHopeBuilder? _requirements;
  EngHopeBuilder get requirements =>
      _$this._requirements ??= new EngHopeBuilder();
  set requirements(EngHopeBuilder? requirements) =>
      _$this._requirements = requirements;

  GeneralCompanyUserAppliedCompanyBuilder() {
    GeneralCompanyUserAppliedCompany._defaults(this);
  }

  GeneralCompanyUserAppliedCompanyBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userId = $v.userId;
      _profileImagePath = $v.profileImagePath;
      _firstName = $v.firstName;
      _lastName = $v.lastName;
      _birthDate = $v.birthDate;
      _age = $v.age;
      _countryCode = $v.countryCode;
      _nickname = $v.nickname;
      _skills = $v.skills?.toBuilder();
      _sexType = $v.sexType;
      _totalCompanyChangedCount = $v.totalCompanyChangedCount;
      _requirements = $v.requirements?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GeneralCompanyUserAppliedCompany other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GeneralCompanyUserAppliedCompany;
  }

  @override
  void update(void Function(GeneralCompanyUserAppliedCompanyBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GeneralCompanyUserAppliedCompany build() => _build();

  _$GeneralCompanyUserAppliedCompany _build() {
    _$GeneralCompanyUserAppliedCompany _$result;
    try {
      _$result = _$v ??
          new _$GeneralCompanyUserAppliedCompany._(
            userId: userId,
            profileImagePath: profileImagePath,
            firstName: firstName,
            lastName: lastName,
            birthDate: birthDate,
            age: age,
            countryCode: countryCode,
            nickname: nickname,
            skills: _skills?.build(),
            sexType: sexType,
            totalCompanyChangedCount: totalCompanyChangedCount,
            requirements: _requirements?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'skills';
        _skills?.build();

        _$failedField = 'requirements';
        _requirements?.build();
      } catch (e) {
        throw new BuiltValueNestedFieldError(
            r'GeneralCompanyUserAppliedCompany', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
