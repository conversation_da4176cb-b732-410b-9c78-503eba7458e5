//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:openapi/src/model/error_detail.dart';
import 'package:openapi/src/model/general_company_list_applied_engineer.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'general_company_list_user_applied_company_response_model.g.dart';

/// GeneralCompanyListUserAppliedCompanyResponseModel
///
/// Properties:
/// * [message] 
/// * [errors] 
/// * [data] 
@BuiltValue()
abstract class GeneralCompanyListUserAppliedCompanyResponseModel implements Built<GeneralCompanyListUserAppliedCompanyResponseModel, GeneralCompanyListUserAppliedCompanyResponseModelBuilder> {
  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'errors')
  BuiltList<ErrorDetail>? get errors;

  @BuiltValueField(wireName: r'data')
  GeneralCompanyListAppliedEngineer get data;

  GeneralCompanyListUserAppliedCompanyResponseModel._();

  factory GeneralCompanyListUserAppliedCompanyResponseModel([void updates(GeneralCompanyListUserAppliedCompanyResponseModelBuilder b)]) = _$GeneralCompanyListUserAppliedCompanyResponseModel;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GeneralCompanyListUserAppliedCompanyResponseModelBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GeneralCompanyListUserAppliedCompanyResponseModel> get serializer => _$GeneralCompanyListUserAppliedCompanyResponseModelSerializer();
}

class _$GeneralCompanyListUserAppliedCompanyResponseModelSerializer implements PrimitiveSerializer<GeneralCompanyListUserAppliedCompanyResponseModel> {
  @override
  final Iterable<Type> types = const [GeneralCompanyListUserAppliedCompanyResponseModel, _$GeneralCompanyListUserAppliedCompanyResponseModel];

  @override
  final String wireName = r'GeneralCompanyListUserAppliedCompanyResponseModel';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GeneralCompanyListUserAppliedCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield object.message == null ? null : serializers.serialize(
      object.message,
      specifiedType: const FullType.nullable(String),
    );
    yield r'errors';
    yield object.errors == null ? null : serializers.serialize(
      object.errors,
      specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
    );
    yield r'data';
    yield serializers.serialize(
      object.data,
      specifiedType: const FullType(GeneralCompanyListAppliedEngineer),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    GeneralCompanyListUserAppliedCompanyResponseModel object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GeneralCompanyListUserAppliedCompanyResponseModelBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'errors':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(BuiltList, [FullType(ErrorDetail)]),
          ) as BuiltList<ErrorDetail>?;
          if (valueDes == null) continue;
          result.errors.replace(valueDes);
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GeneralCompanyListAppliedEngineer),
          ) as GeneralCompanyListAppliedEngineer;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GeneralCompanyListUserAppliedCompanyResponseModel deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GeneralCompanyListUserAppliedCompanyResponseModelBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

