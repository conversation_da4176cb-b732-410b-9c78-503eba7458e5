import 'package:asiantech_link/layers/domain/repository/general_company_repository.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/openapi.dart';

class GetManageApplyUsecase {
  final GeneralCompanyRepository generalCompanyRepository;

  GetManageApplyUsecase({required this.generalCompanyRepository});

  Future<PagingGeneralCompanyAppliedEngineersResponseModel> call({
    BuiltList<int>? recruitIds,
    String? ordering,
    String? search,
    BuiltList<int>? applyStatusFilterCodes,
    int? hostCompanyId,
    int? page,
  }) async {
    return generalCompanyRepository.getManageApply(
      recruitIds: recruitIds,
      ordering: ordering,
      search: search,
      applyStatusFilterCodes: applyStatusFilterCodes,
      hostCompanyId: hostCompanyId,
      page: page
    );
  }
}
