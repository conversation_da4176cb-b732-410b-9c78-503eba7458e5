import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/domain/entity/company_model.dart';

import 'package:dio/dio.dart';

import 'package:openapi/openapi.dart';

abstract class HostCompanyRepository {
  Future<void> subscribeSupportCompany({required String companyId});
  Future<void> unSubscribeSupportCompany({required String companyId});
  Future<List<CompanyModel>> getSupportCompany({String? email});
  Future<List<HostCompanyGetSupportCompany>> getSupportCompanySubscribed();

  Future<int?> requestInterview({required HostCompanyRequestInterview data});

  Future<void> updateApplyAdmission(
      {required HostCompanyUpdateInterviewAdmission data});

  Future<void> deleteRecruit({required String id});
  Future<void> createRecruit({required CreateRecruitment createRecruitment});
  Future<PagingModelDto<RecruitUploadedSerializers>> getMyRecruits({
    String? cursor,
    int? pageSize,
    required bool showOldPost,
    bool? showNewest,
    int? engineerId,
    int? displayFlag,
  });
  Future<CompanyRecruitDetail?> getCompanyRecruitmentDetails(
      {required String id});

  Future<void> updateCompanyRecruitmentDetails(
      {required String id, required UpdateRecruitment data});
  Future<Response<RecruitmentDetailsResponseModel>> getRecruitDetails(
      {required String id});
}
