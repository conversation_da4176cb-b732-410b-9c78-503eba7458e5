import 'package:dio/dio.dart';

abstract class MediaRepository {
  Future uploadImage({required MultipartFile image});
  Future deleteAvatar();
  Future uploadPassportImage({required MultipartFile image});
  Future deleteAvatarPassPort();
  Future<void> uploadCompanyLogo({required MultipartFile image});
  Future<void> uploadCompanyPrImage(
      {required MultipartFile image, required int index});
  Future<void> uploadContractImage(
      {required MultipartFile image, required String recruitId});
  Future<String> uploadRecruitCover({required MultipartFile image});
  Future<void> uploadSignatureApply(
      {required MultipartFile image, required String applyId});
  Future<void> uploadAvatarEngineer(
      {required MultipartFile image, required int engineerId});

  Future<void> deleteAvatarEngineer({required int engineerId});
}
