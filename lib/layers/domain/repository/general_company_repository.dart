import 'dart:typed_data';

import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/openapi.dart';

abstract class GeneralCompanyRepository {
  Future<void> registerCompany({
    required GeneralCompanyRegister registerCompanyData,
  });
  Future<void> updateCompany({
    required GeneralCompanyUpdate data,
  });
  Future<GeneralCompanyDetails> getMyCompany();

  Future<PagingModelDto<GeneralCompanyExploreUser>> exploreUser({
    int? filterId,
    int? pageSize,
    int? page,
    String? searchType,
    String? ordering,
    int? ageFrom,
    int? ageTo,
    String? sexType,
    String? countryCode,
    String? addressCode1,
    String? addressCode2,
    String? lastAcademicCode,
    String? languageCode1,
    int? languageLevelType1,
    String? languageCode2,
    int? languageLevelType2,
    String? experiencedJobCode1,
    int? yearsOfExperience1,
    String? experiencedJobCode2,
    int? yearsOfExperience2,
    String? experiencedJobCode3,
    int? yearsOfExperience3,
    String? licenceCode1,
    int? licencePoint1,
    String? licenceCode2,
    int? licencePoint2,
    String? licenceCode3,
    int? licencePoint3,
    String? recruitingJobCode,
    String? recruitingEmployCode,
    String? workPlaceCode1,
    String? workPlaceCode2,
    String? workPlaceCode3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    double? agentFee,
    String? agentFeeCurrCode,
    bool? showFavorite,
    int? remoteWorkSkillPointType,
    int? globalSkillPointType,
    int? communicationSkillPointType,
    int? horensoSkillPointType,
    int? projectManagementSkillPointType,
    int? globalWorkExperience,
    String? searchQuery,
    int? careerType,
    BuiltList<String>? skills,
    String? recruitId,
  });

  Future<void> deleteFilter({required String filterId});
  Future<void> saveFilter({required GeneralCompanySaveFilter filter});

  Future<List<GeneralCompanyFilter>> getSavedFilter();

  Future<void> addFavoriteUser(
      {required GeneralCompanyUpdateFavoriteUser data});

  Future<int> getUserExploreCount({
    int? filterId,
    int? pageSize,
    String? cursor,
    String? ordering,
    int? ageFrom,
    int? ageTo,
    String? sexType,
    String? countryCode,
    String? addressCode1,
    String? addressCode2,
    String? lastAcademicCode,
    String? languageCode1,
    int? languageLevelType1,
    String? languageCode2,
    int? languageLevelType2,
    String? experiencedJobCode1,
    int? yearsOfExperience1,
    String? licenceCode1,
    int? licencePoint1,
    String? licenceCode2,
    int? licencePoint2,
    String? licenceCode3,
    int? licencePoint3,
    String? recruitingJobCode,
    String? recruitingEmployCode,
    String? workPlaceCode1,
    String? workPlaceCode2,
    String? workPlaceCode3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    double? agentFee,
    String? agentFeeCurrCode,
    bool? showFavorite,
    int? remoteWorkSkillPointType,
    int? globalSkillPointType,
    int? communicationSkillPointType,
    int? horensoSkillPointType,
    int? projectManagementSkillPointType,
    int? globalWorkExperience,
    String? searchQuery,
    int? careerType,
    String? searchType,
    BuiltList<String>? skills,
  });
  Future<UserExploreDetailsSerializers> getUserDetails(
      {required String userId});

  Future<List<AssessmentQuestionData>> getRemoteWorkSkillsEngineer(
      {required String userId});

  Future<List<AssessmentQuestionData>>
      getCommunicationSkillsSelfAssessmentEngineer({required String userId});

  Future<List<AssessmentQuestionData>> getGlobalResponsivenessSkillsEngineer(
      {required String userId});

  Future<List<AssessmentQuestionData>>
      getReportingConsultationSkillsSelfEvaluationEngineer(
          {required String userId});

  Future<List<AssessmentQuestionData>>
      getProjectManagementSkillsSelfEvaluationEngineer(
          {required String userId});

  Future<PagingGeneralCompanyAppliedEngineersResponseModel> getManageApply({
    BuiltList<int>? recruitIds,
    String? ordering,
    String? search,
    BuiltList<int>? applyStatusFilterCodes,
    int? hostCompanyId,
    int? page,
  });

  Future<void> acceptApply({required GeneralCompanyRequestInterviewApply data});
  Future<GeneralCompanyGetApplyDetails?> getApplyDetails(
      {required int applyId, required int? hostCompanyId});
  Future<void> rejectApply({required GeneralCompanyRejectApply data});
  Future<List<GeneralCompanyInterviewData>> getCalendarInterview(
      {required Date date, required int? hostCompanyId});
  Future<void> updateInterviewDate(
      {required GeneralCompanyUpdateInterviewDateTime data});
  Future<GeneralCompanyGetContractDetails?> getCompanyContractDetails(
      {required int applyId, required int? hostCompanyId});
  Future<Uint8List?> downloadEmployeeContract(
      {required GeneralCompanyParamGetContractDetails data});
  Future<PagingModelDto<GroupChat>> getListGroupChat({
    String? ordering,
    int? pageSize,
    String? cursor,
  });
}
