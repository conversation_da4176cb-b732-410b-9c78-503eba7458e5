import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:openapi/openapi.dart';

abstract class ProfileRepository {
  Future<Response<UserDetailsResponseModel>> getProfile({
    int? optionalEngineerId,
  });
  Future<ResponseModel?> updatePassword(
      {required String oldPassword,
      required String newPassword,
      required String confirmPassword});
  Future<UpdateUserResponseModel?> updateProfile(UpdateUser updateUser);
  Future<SelfAssessmentResponseModel?> getRemoteWorkSkills();
  Future<ResponseModel?> saveRemoteWorkSkills(SelfAssessmentAnswer data);
  Future<SelfAssessmentResponseModel?> getGlobalResponsivenessSkills();
  Future<ResponseModel?> saveGlobalResponsivenessSkills(
      SelfAssessmentAnswer data);
  Future<SelfAssessmentResponseModel?> getCommunicationSkillsSelfAssessment();
  Future<ResponseModel?> saveCommunicationSkillsSelfAssessment(
      SelfAssessmentAnswer data);
  Future<SelfAssessmentResponseModel?>
      getReportingConsultationSkillsSelfEvaluation();
  Future<ResponseModel?> saveReportingConsultationSkillsSelfEvaluation(
      SelfAssessmentAnswer data);
  Future<SelfAssessmentResponseModel?>
      getProjectManagementSkillsSelfEvaluation();
  Future<ResponseModel?> saveProjectManagementSkillsSelfEvaluation(
      SelfAssessmentAnswer data);
  Future<void> saveCVUploaded({required SaveCVUploaded cvFile});
  Future<UploadCVUploaded?> uploadCvPDF({
    required MultipartFile cvPdf,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  });

  Future<Uint8List?> downloadCv();
}
