import 'dart:typed_data';

import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api/general_company_api.dart';
import 'package:asiantech_link/layers/domain/repository/general_company_repository.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/openapi.dart';

class GeneralCompanyRepositoryImpl extends GeneralCompanyRepository {
  final AppGeneralCompanyApi _api = AppGeneralCompanyApi();
  @override
  Future<GeneralCompanyDetails> getMyCompany() {
    return _api.getMyCompany();
  }

  @override
  Future<void> registerCompany(
      {required GeneralCompanyRegister registerCompanyData}) {
    return _api.registerCompany(registerCompanyData: registerCompanyData);
  }

  @override
  Future<void> updateCompany({required GeneralCompanyUpdate data}) {
    return _api.updateCompany(data: data);
  }

  @override
  Future<void> addFavoriteUser(
      {required GeneralCompanyUpdateFavoriteUser data}) {
    return _api.addFavoriteUser(data: data);
  }

  @override
  Future<void> deleteFilter({required String filterId}) {
    return _api.deleteFilter(filterId: filterId);
  }

  @override
  Future<PagingModelDto<GeneralCompanyExploreUser>> exploreUser({
    int? filterId,
    int? pageSize,
    int? page,
    String? searchType,
    String? ordering,
    int? ageFrom,
    int? ageTo,
    String? sexType,
    String? countryCode,
    String? addressCode1,
    String? addressCode2,
    String? lastAcademicCode,
    String? languageCode1,
    int? languageLevelType1,
    String? languageCode2,
    int? languageLevelType2,
    String? experiencedJobCode1,
    int? yearsOfExperience1,
    String? experiencedJobCode2,
    int? yearsOfExperience2,
    String? experiencedJobCode3,
    int? yearsOfExperience3,
    String? licenceCode1,
    int? licencePoint1,
    String? licenceCode2,
    int? licencePoint2,
    String? licenceCode3,
    int? licencePoint3,
    String? recruitingJobCode,
    String? recruitingEmployCode,
    String? workPlaceCode1,
    String? workPlaceCode2,
    String? workPlaceCode3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    double? agentFee,
    String? agentFeeCurrCode,
    bool? showFavorite,
    BuiltList<String>? skills,
    int? remoteWorkSkillPointType,
    int? globalSkillPointType,
    int? communicationSkillPointType,
    int? horensoSkillPointType,
    int? projectManagementSkillPointType,
    int? globalWorkExperience,
    String? searchQuery,
    int? careerType,
    String? recruitId,
  }) {
    return _api.exploreUser(
        filterId: filterId,
        pageSize: pageSize,
        page: page,
        searchType: searchType,
        ordering: ordering,
        ageFrom: ageFrom,
        ageTo: ageTo,
        sexType: sexType,
        countryCode: countryCode,
        addressCode1: addressCode1,
        addressCode2: addressCode2,
        lastAcademicCode: lastAcademicCode,
        languageCode1: languageCode1,
        languageLevelType1: languageLevelType1,
        languageCode2: languageCode2,
        languageLevelType2: languageLevelType2,
        experiencedJobCode1: experiencedJobCode1,
        yearsOfExperience1: yearsOfExperience1,
        experiencedJobCode2: experiencedJobCode2,
        yearsOfExperience2: yearsOfExperience2,
        experiencedJobCode3: experiencedJobCode3,
        yearsOfExperience3: yearsOfExperience3,
        licenceCode1: licenceCode1,
        licencePoint1: licencePoint1,
        licenceCode2: licenceCode2,
        licencePoint2: licencePoint2,
        licenceCode3: licenceCode3,
        licencePoint3: licencePoint3,
        recruitingJobCode: recruitingJobCode,
        recruitingEmployCode: recruitingEmployCode,
        workPlaceCode1: workPlaceCode1,
        workPlaceCode2: workPlaceCode2,
        workPlaceCode3: workPlaceCode3,
        payrollCode: payrollCode,
        payrollPriceFrom: payrollPriceFrom,
        payrollPriceTo: payrollPriceTo,
        agentFee: agentFee,
        agentFeeCurrCode: agentFeeCurrCode,
        showFavorite: showFavorite,
        remoteWorkSkillPointType: remoteWorkSkillPointType,
        globalSkillPointType: globalSkillPointType,
        communicationSkillPointType: communicationSkillPointType,
        horensoSkillPointType: horensoSkillPointType,
        projectManagementSkillPointType: projectManagementSkillPointType,
        globalWorkExperience: globalWorkExperience,
        searchQuery: searchQuery,
        careerType: careerType,
        skills: skills,
        recruitId: recruitId);
  }

  @override
  Future<List<AssessmentQuestionData>>
      getCommunicationSkillsSelfAssessmentEngineer({required String userId}) {
    return _api.getCommunicationSkillsSelfAssessmentEngineer(userId: userId);
  }

  @override
  Future<List<AssessmentQuestionData>> getGlobalResponsivenessSkillsEngineer(
      {required String userId}) {
    return _api.getGlobalResponsivenessSkillsEngineer(userId: userId);
  }

  @override
  Future<List<AssessmentQuestionData>>
      getProjectManagementSkillsSelfEvaluationEngineer(
          {required String userId}) {
    return _api.getProjectManagementSkillsSelfEvaluationEngineer(
        userId: userId);
  }

  @override
  Future<List<AssessmentQuestionData>> getRemoteWorkSkillsEngineer(
      {required String userId}) {
    return _api.getRemoteWorkSkillsEngineer(userId: userId);
  }

  @override
  Future<List<AssessmentQuestionData>>
      getReportingConsultationSkillsSelfEvaluationEngineer(
          {required String userId}) {
    return _api.getReportingConsultationSkillsSelfEvaluationEngineer(
        userId: userId);
  }

  @override
  Future<List<GeneralCompanyFilter>> getSavedFilter() {
    return _api.getSavedFilter();
  }

  @override
  Future<UserExploreDetailsSerializers> getUserDetails(
      {required String userId}) {
    return _api.getUserDetails(userId: userId);
  }

  @override
  Future<int> getUserExploreCount({
    int? filterId,
    int? pageSize,
    String? cursor,
    String? ordering,
    int? ageFrom,
    int? ageTo,
    String? sexType,
    String? countryCode,
    String? addressCode1,
    String? addressCode2,
    String? lastAcademicCode,
    String? languageCode1,
    int? languageLevelType1,
    String? languageCode2,
    int? languageLevelType2,
    String? experiencedJobCode1,
    int? yearsOfExperience1,
    String? licenceCode1,
    int? licencePoint1,
    String? licenceCode2,
    int? licencePoint2,
    String? licenceCode3,
    int? licencePoint3,
    String? recruitingJobCode,
    String? recruitingEmployCode,
    String? workPlaceCode1,
    String? workPlaceCode2,
    String? workPlaceCode3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    double? agentFee,
    String? agentFeeCurrCode,
    bool? showFavorite,
    int? remoteWorkSkillPointType,
    int? globalSkillPointType,
    int? communicationSkillPointType,
    int? horensoSkillPointType,
    int? projectManagementSkillPointType,
    String? searchQuery,
    int? careerType,
    int? globalWorkExperience,
    BuiltList<String>? skills,
    String? searchType,
  }) {
    return _api.getUserExploreCount(
        filterId: filterId,
        pageSize: pageSize,
        cursor: cursor,
        ordering: ordering,
        ageFrom: ageFrom,
        ageTo: ageTo,
        sexType: sexType,
        countryCode: countryCode,
        addressCode1: addressCode1,
        addressCode2: addressCode2,
        lastAcademicCode: lastAcademicCode,
        languageCode1: languageCode1,
        languageLevelType1: languageLevelType1,
        languageCode2: languageCode2,
        languageLevelType2: languageLevelType2,
        experiencedJobCode1: experiencedJobCode1,
        yearsOfExperience1: yearsOfExperience1,
        licenceCode1: licenceCode1,
        licencePoint1: licencePoint1,
        licenceCode2: licenceCode2,
        licencePoint2: licencePoint2,
        licenceCode3: licenceCode3,
        licencePoint3: licencePoint3,
        recruitingJobCode: recruitingJobCode,
        recruitingEmployCode: recruitingEmployCode,
        workPlaceCode1: workPlaceCode1,
        workPlaceCode2: workPlaceCode2,
        workPlaceCode3: workPlaceCode3,
        payrollCode: payrollCode,
        payrollPriceFrom: payrollPriceFrom,
        payrollPriceTo: payrollPriceTo,
        agentFee: agentFee,
        agentFeeCurrCode: agentFeeCurrCode,
        showFavorite: showFavorite,
        remoteWorkSkillPointType: remoteWorkSkillPointType,
        globalSkillPointType: globalSkillPointType,
        communicationSkillPointType: communicationSkillPointType,
        horensoSkillPointType: horensoSkillPointType,
        projectManagementSkillPointType: projectManagementSkillPointType,
        globalWorkExperience: globalWorkExperience,
        searchQuery: searchQuery,
        careerType: careerType,
        skills: skills,
        searchType: searchType);
  }

  @override
  Future<void> saveFilter({required GeneralCompanySaveFilter filter}) {
    return _api.saveFilter(filter: filter);
  }

  @override
  Future<PagingGeneralCompanyAppliedEngineersResponseModel> getManageApply({
    BuiltList<int>? recruitIds,
    String? ordering,
    String? search,
    BuiltList<int>? applyStatusFilterCodes,
    int? hostCompanyId,
  }) {
    return _api.getManageApply(
      recruitIds: recruitIds,
      ordering: ordering,
      search: search,
      applyStatusFilterCodes: applyStatusFilterCodes,
      hostCompanyId: hostCompanyId,
    );
  }

  @override
  Future<Uint8List?> downloadEmployeeContract(
      {required GeneralCompanyParamGetContractDetails data}) {
    return _api.downloadEmployeeContract(data: data);
  }

  @override
  Future<List<GeneralCompanyInterviewData>> getCalendarInterview(
      {required Date date, required int? hostCompanyId}) {
    return _api.getCalendarInterview(date: date, hostCompanyId: hostCompanyId);
  }

  @override
  Future<GeneralCompanyGetContractDetails?> getCompanyContractDetails(
      {required int applyId, required int? hostCompanyId}) {
    return _api.getCompanyContractDetails(
        applyId: applyId, hostCompanyId: hostCompanyId);
  }

  @override
  Future<void> rejectApply({required GeneralCompanyRejectApply data}) {
    return _api.rejectApply(data: data);
  }

  @override
  Future<void> updateInterviewDate(
      {required GeneralCompanyUpdateInterviewDateTime data}) {
    return _api.updateInterviewDate(data: data);
  }

  @override
  Future<void> acceptApply(
      {required GeneralCompanyRequestInterviewApply data}) {
    return _api.acceptApply(data: data);
  }

  @override
  Future<GeneralCompanyGetApplyDetails?> getApplyDetails(
      {required int applyId, required int? hostCompanyId}) {
    return _api.getApplyDetails(applyId: applyId, hostCompanyId: hostCompanyId);
  }

  @override
  Future<PagingModelDto<GroupChat>> getListGroupChat({
    String? ordering,
    int? pageSize,
    String? cursor,
  }) {
    return _api.getListGroupChat(
        ordering: ordering, pageSize: pageSize, cursor: cursor);
  }
}
