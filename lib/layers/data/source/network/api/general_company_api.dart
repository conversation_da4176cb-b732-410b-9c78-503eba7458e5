import 'dart:typed_data';

import 'package:asiantech_link/layers/data/dto/paging_model_dto.dart';
import 'package:asiantech_link/layers/data/source/network/api.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:asiantech_link/layers/presentation/utils/parse_error.dart';
import 'package:built_collection/built_collection.dart';
import 'package:openapi/openapi.dart';

class AppGeneralCompanyApi extends Api {
  late final GeneralCompanyApi _api = initOpenApi().getGeneralCompanyApi();

  Future<void> registerCompany({
    required GeneralCompanyRegister registerCompanyData,
  }) async {
    await _api.registerCompany(data: registerCompanyData);
  }

  Future<void> updateCompany({
    required GeneralCompanyUpdate data,
  }) async {
    await _api.updateCompany(data: data);
  }

  Future<GeneralCompanyDetails> getMyCompany() async {
    final response = await _api.companyDetails();
    return response.data?.data ?? GeneralCompanyDetails();
  }

  Future<PagingModelDto<GeneralCompanyExploreUser>> exploreUser({
    int? filterId,
    int? pageSize,
    int? page,
    String? searchType,
    String? ordering,
    int? ageFrom,
    int? ageTo,
    String? sexType,
    String? countryCode,
    String? addressCode1,
    String? addressCode2,
    String? lastAcademicCode,
    String? languageCode1,
    int? languageLevelType1,
    String? languageCode2,
    int? languageLevelType2,
    String? experiencedJobCode1,
    int? yearsOfExperience1,
    String? experiencedJobCode2,
    int? yearsOfExperience2,
    String? experiencedJobCode3,
    int? yearsOfExperience3,
    String? licenceCode1,
    int? licencePoint1,
    String? licenceCode2,
    int? licencePoint2,
    String? licenceCode3,
    int? licencePoint3,
    String? recruitingJobCode,
    String? recruitingEmployCode,
    String? workPlaceCode1,
    String? workPlaceCode2,
    String? workPlaceCode3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    double? agentFee,
    String? agentFeeCurrCode,
    bool? showFavorite,
    int? remoteWorkSkillPointType,
    BuiltList<String>? skills,
    int? globalSkillPointType,
    int? communicationSkillPointType,
    int? horensoSkillPointType,
    int? projectManagementSkillPointType,
    int? globalWorkExperience,
    String? searchQuery,
    int? careerType,
    String? recruitId,
  }) async {
    final response = await _api.exploreUsers(
      recruitId: recruitId,
      remoteWorkSkillPointType: remoteWorkSkillPointType,
      globalSkillPointType: globalSkillPointType,
      communicationSkillPointType: communicationSkillPointType,
      horensoSkillPointType: horensoSkillPointType,
      projectManagementSkillPointType: projectManagementSkillPointType,
      recruitingJobCode: recruitingJobCode,
      recruitingEmployCode: recruitingEmployCode,
      page: page,
      searchType: searchType,
      filterId: filterId,
      pageSize: pageSize,
      ordering: ordering,
      ageFrom: ageFrom,
      ageTo: ageTo,
      sexType: sexType,
      countryCode: countryCode,
      addressCode1: addressCode1,
      addressCode2: addressCode2,
      lastAcademicCode: lastAcademicCode,
      languageCode1: languageCode1,
      languageLevelType1: languageLevelType1,
      languageCode2: languageCode2,
      languageLevelType2: languageLevelType2,
      experiencedJobCode1: experiencedJobCode1,
      yearsOfExperience1: yearsOfExperience1,
      skills: skills,
      licenceCode1: licenceCode1,
      licencePoint1: licencePoint1,
      licenceCode2: licenceCode2,
      licencePoint2: licencePoint2,
      licenceCode3: licenceCode3,
      licencePoint3: licencePoint3,
      workPlaceCode1: workPlaceCode1,
      workPlaceCode2: workPlaceCode2,
      workPlaceCode3: workPlaceCode3,
      payrollCode: payrollCode,
      payrollPriceFrom: payrollPriceFrom,
      payrollPriceTo: payrollPriceTo,
      agentFee: agentFee,
      agentFeeCurrCode: agentFeeCurrCode,
      showFavorite: showFavorite,
      globalWorkExp: globalWorkExperience,
      searchQuery: searchQuery,
      careerType: careerType,
    );
    final model = response.data!.data;

    return PagingModelDto(
        results: model.results.toList(),
        next: model.next,
        previous: model.previous,
        totalCount: model.count,
        metaData: {
          "ai_summary": model.aiSummary,
          "ai_summary_ja": model.aiSummaryJa,
          "ai_summary_vi": model.aiSummaryVi,
          "recruit_id": model.recruitId?.toString(),
        });
  }

  Future<void> deleteFilter({required String filterId}) {
    return _api.deleteFilter(filterId: filterId);
  }

  Future<void> saveFilter({required GeneralCompanySaveFilter filter}) {
    return _api.saveFilter(data: filter);
  }

  Future<List<GeneralCompanyFilter>> getSavedFilter() async {
    final response = await _api.getSavedFilters();
    return response.data?.data.toList() ?? [];
  }

  Future<void> addFavoriteUser(
      {required GeneralCompanyUpdateFavoriteUser data}) async {
    await _api.addFavoriteUser(data: data);
  }

  Future<int> getUserExploreCount({
    int? filterId,
    int? pageSize,
    String? cursor,
    String? ordering,
    int? ageFrom,
    int? ageTo,
    String? sexType,
    String? countryCode,
    String? addressCode1,
    String? addressCode2,
    String? lastAcademicCode,
    String? languageCode1,
    int? languageLevelType1,
    String? languageCode2,
    int? languageLevelType2,
    String? experiencedJobCode1,
    int? yearsOfExperience1,
    String? experiencedJobCode2,
    int? yearsOfExperience2,
    String? experiencedJobCode3,
    int? yearsOfExperience3,
    String? licenceCode1,
    int? licencePoint1,
    String? licenceCode2,
    int? licencePoint2,
    String? licenceCode3,
    int? licencePoint3,
    String? recruitingJobCode,
    String? recruitingEmployCode,
    String? workPlaceCode1,
    String? workPlaceCode2,
    String? workPlaceCode3,
    String? payrollCode,
    String? payrollPriceFrom,
    String? payrollPriceTo,
    double? agentFee,
    String? agentFeeCurrCode,
    bool? showFavorite,
    String? skillCode1_1,
    String? skillCode1_2,
    String? skillCode1_3,
    String? skillCode2_1,
    String? skillCode2_2,
    String? skillCode2_3,
    String? skillCode3_1,
    String? skillCode3_2,
    String? skillCode3_3,
    int? skillLevelType1_1,
    int? skillLevelType1_2,
    int? skillLevelType1_3,
    int? skillLevelType2_1,
    int? skillLevelType2_2,
    int? skillLevelType2_3,
    int? skillLevelType3_1,
    int? skillLevelType3_2,
    int? skillLevelType3_3,
    int? remoteWorkSkillPointType,
    int? globalSkillPointType,
    int? communicationSkillPointType,
    int? horensoSkillPointType,
    int? projectManagementSkillPointType,
    int? globalWorkExperience,
    String? searchQuery,
    int? careerType,
    String? searchType,
    BuiltList<String>? skills,
  }) async {
    final response = await _api.getExploreUserCount(
      remoteWorkSkillPointType: remoteWorkSkillPointType,
      globalSkillPointType: globalSkillPointType,
      communicationSkillPointType: communicationSkillPointType,
      horensoSkillPointType: horensoSkillPointType,
      projectManagementSkillPointType: projectManagementSkillPointType,
      recruitingJobCode: recruitingJobCode,
      recruitingEmployCode: recruitingEmployCode,
      filterId: filterId,
      pageSize: pageSize,
      ordering: ordering,
      ageFrom: ageFrom,
      ageTo: ageTo,
      sexType: sexType,
      countryCode: countryCode,
      addressCode1: addressCode1,
      addressCode2: addressCode2,
      lastAcademicCode: lastAcademicCode,
      languageCode1: languageCode1,
      languageLevelType1: languageLevelType1,
      languageCode2: languageCode2,
      languageLevelType2: languageLevelType2,
      experiencedJobCode1: experiencedJobCode1,
      yearsOfExperience1: yearsOfExperience1,
      licenceCode1: licenceCode1,
      licencePoint1: licencePoint1,
      licenceCode2: licenceCode2,
      licencePoint2: licencePoint2,
      licenceCode3: licenceCode3,
      licencePoint3: licencePoint3,
      workPlaceCode1: workPlaceCode1,
      workPlaceCode2: workPlaceCode2,
      workPlaceCode3: workPlaceCode3,
      payrollCode: payrollCode,
      payrollPriceFrom: payrollPriceFrom,
      payrollPriceTo: payrollPriceTo,
      agentFee: agentFee,
      agentFeeCurrCode: agentFeeCurrCode,
      showFavorite: showFavorite,
      globalWorkExp: globalWorkExperience,
      searchQuery: searchQuery,
      careerType: careerType,
      skills: skills,
      searchType: searchType,
    );
    return response.data!.data;
  }

  Future<UserExploreDetailsSerializers> getUserDetails(
      {required String userId}) async {
    final response = await _api.getUserDetails(userId: userId);
    var data = response.data;
    if (data != null) {
      return data.data;
    } else {
      throw ApiException(
          title: "No data found",
          statusCode: "400",
          message: "Response data is null");
    }
  }

  Future<List<AssessmentQuestionData>> getRemoteWorkSkillsEngineer(
      {required String userId}) async {
    final response = await _api.getRemoteWorkSkillsEngineer(userId: userId);
    return response.data?.data.toList() ?? [];
  }

  Future<List<AssessmentQuestionData>>
      getCommunicationSkillsSelfAssessmentEngineer(
          {required String userId}) async {
    final response =
        await _api.getCommunicationSkillsSelfAssessmentEngineer(userId: userId);
    return response.data?.data.toList() ?? [];
  }

  Future<List<AssessmentQuestionData>> getGlobalResponsivenessSkillsEngineer(
      {required String userId}) async {
    final response =
        await _api.getGlobalResponsivenessSkillsEngineer(userId: userId);
    return response.data?.data.toList() ?? [];
  }

  Future<List<AssessmentQuestionData>>
      getReportingConsultationSkillsSelfEvaluationEngineer(
          {required String userId}) async {
    final response = await _api
        .getReportingConsultationSkillsSelfEvaluationEngineer(userId: userId);
    return response.data?.data.toList() ?? [];
  }

  Future<List<AssessmentQuestionData>>
      getProjectManagementSkillsSelfEvaluationEngineer(
          {required String userId}) async {
    final response = await _api
        .getProjectManagementSkillsSelfEvaluationEngineer(userId: userId);
    return response.data?.data.toList() ?? [];
  }

  Future<GeneralCompanyListAppliedEngineer> getManageApply({
    BuiltList<int>? recruitIds,
    String? ordering,
    String? search,
    BuiltList<int>? applyStatusFilterCodes,
    int? hostCompanyId,
  }) async {
    final response = await _api.listAppliedEngineers(
      recruitIds: recruitIds ??
          BuiltList<int>.build((b) {
            b = ListBuilder();
          }),
      search: search,
      ordering: ordering,
      hostCompanyId: hostCompanyId,
      applyStatusFilterCodes: applyStatusFilterCodes ??
          BuiltList.build((b) {
            b = ListBuilder();
          }),
    );
    return response.data?.data ??
        GeneralCompanyListAppliedEngineer((b) {
          b.data = ListBuilder([]);
          b.totalEngineers = 0;
        });
  }

  Future<GeneralCompanyGetApplyDetails> getApplyDetails(
      {required int applyId, required int? hostCompanyId}) async {
    final response = await _api.getApplyDetails(
        applyId: applyId, hostCompanyId: hostCompanyId);
    return response.data?.data ?? GeneralCompanyGetApplyDetails();
  }

  Future<void> rejectApply({required GeneralCompanyRejectApply data}) async {
    await _api.rejectApply(data: data);
  }

  Future<void> acceptApply(
      {required GeneralCompanyRequestInterviewApply data}) async {
    await _api.acceptApply(data: data);
  }

  Future<List<GeneralCompanyInterviewData>> getCalendarInterview(
      {required Date date, required int? hostCompanyId}) async {
    final response = await _api.getCalendarInterview(
        date: date, hostCompanyId: hostCompanyId);
    return response.data?.data.toList() ?? [];
  }

  Future<void> updateInterviewDate(
      {required GeneralCompanyUpdateInterviewDateTime data}) async {
    await _api.updateInterviewDate(data: data);
  }

  Future<GeneralCompanyGetContractDetails?> getCompanyContractDetails(
      {required int applyId, required int? hostCompanyId}) async {
    final response = await _api.getCompanyContractDetails(
        applyId: applyId, hostCompanyId: hostCompanyId);
    return response.data?.data;
  }

  Future<Uint8List?> downloadEmployeeContract(
      {required GeneralCompanyParamGetContractDetails data}) async {
    final response = await _api.downloadEmployeeContract(data: data);
    return response.data;
  }

  Future<PagingModelDto<GroupChat>> getListGroupChat({
    String? ordering,
    int? pageSize,
    String? cursor,
  }) async {
    final response = await _api.getListGroupChat(
      ordering: ordering,
      pageSize: pageSize,
      cursor: cursor,
    );
    final model = response.data.nonNullable.data;
    return PagingModelDto(
      results: model.results.toList(),
      next: model.next,
      previous: model.previous,
      totalCount: model.totalCount,
    );
  }
}
