import 'package:flutter/material.dart';

class AppColors {
  static Color getPrimaryColor(BuildContext context) {
    return Theme.of(context).primaryColor;
  }

  static Color primary = const Color(0xff0080d9);

  static Color secondary = const Color(0xff0093ED);
  static const Color disableColors = Color(0xffe0e4fc);
  static Color backgroundColor = const Color(0xffF4F7FC);

  static const Color shadow = Color(0x00000029);

  // engineer color
  static const Color engineerPrimary = Color(0xff0080d9);
  static const Color engineerSecondary = Color(0xff0093ED);
  static const Color engineerBackgroundColor = Color(0xffF4F7FC);

  // support company color
  static const Color supportCompanyPrimary = Color(0xff6495ed);
  static const Color supportCompanySecondary = Color(0xff6495ed);
  static const Color supportCompanyBackgroundColor = Color(0xffF4F7FC);

  // host company color
  static const Color hostCompanyPrimary = Color(0xff002564);
  static const Color hostCompanySecondary = Color(0xff002564);
  static const Color hostCompanyBackgroundColor = Color(0xffF0F3F8);

  // admin color
  static const Color adminPrimary = Color(0xffDAA520);
  static const Color adminSecondary = Color(0xffDAA520);
  static const Color adminBackgroundColor = Color(0xffF4F7FC);

  static const Color blackColor = Colors.black;
  static const Color grey = Colors.grey;
  static const Color errorColor = Color(0xffCC0000);
  static const Color iconInactive = Color(0xffA6B5D5);
  static const Color disabledButton = Color(0xffCCCCCC);
  static const Color errorBackground = Color(0xffFFF3F3);
  static const Color yellow = Color(0xfffffc00);

  static const Color brightOrange = Color(0xffEF725E);
  static const Color cancelButton = Color(0xff00C6B3);
  static const Color bgDeleteButton = Color(0xffFFEBEB);
  static const Color declineApplicationColor = Color(0xff365d7d);
  static const Color bgDeleteHoverButton = Color(0xffEED7D7);
  static const Color disable = Color(0xffAAAAAA);
  static const Color darkGrey = Color(0xff999999);
  static const Color bgImage = Color(0xffEEEEEE);
  static const Color hoverPrimaryColor = Color(0xff222222);
  static const Color white = Color(0xffFFFFFF);
  static const Color whiteText = Color(0xffFCFCFC);
  static const Color black = Color(0xff000000);
  static const Color disableBlack = Color(0x26000000);
  static const Color hoverTurquoise = Color(0xff0DA193);
  static const Color lightError = Color(0xffFFF8F8);
  static const Color disableInput = Color(0xffDDDDDD);
  static const Color shadowColor = Color(0x0D000000);
  static const Color bgGreyColor = Color(0x0DEDEDED);
  static const Color textGrey = Color(0xFF666666);
  static const Color grey1 = Color(0xff707070);
  static const Color red = Color(0xfff72a48);
  static const Color red2 = Color(0xfff72a48);
  static const Color purple = Color(0xff673AB7);
  static const Color grey2 = Color(0xffEFEFEF);
  static const Color grey3 = Color(0xff80CBC4);
  static const Color grey4 = Color(0xffB2DFDB);
  static const Color grey5 = Color(0xffF5F5F5);
  static const Color grey6 = Color(0xffEEEEEE);
  static const Color grey7 = Color(0xffDDE2EA);
  static const Color grey8 = Color(0xff5F5F5F);
  static const Color grey9 = Color(0xffE0F2F1);
  static const Color brown = Color(0xff3E3E3E);

  static const Color chatColor = Color(0xffDCE8FF);
  static const Color placeholder = Color(0xffECECEC);
  static const Color green = Color(0xff9CCC65);
  static const Color richBlack = Color(0xff08051D);
  static const List<Color> listSkillColor = [
    Color(0xff43A047),
    Color(0xffF4511E),
    Color(0xff0288D1),
    Color(0xff00897B),
    Color(0xff6D4C41),
  ];
  static const Color disableGrey = Color(0xFFD9D9D9);
  static const Color redBgColor = Color(0xFFFFF5F5);
  static const Color cyanBlue = Color(0xff4F7396);
  static const Color blackOlive = Color(0xff3E3E3E);
  static const Color whatsapp = Color(0xff01B55E);

  // skills color
  static const orange = Color(0xffFFD966);
  static const orange2 = Color.fromARGB(255, 236, 206, 117);
  static const yellow2 = Color(0xffFDD835);
  static const green2 = Color(0xffA9D18E);
  static const green3 = Color(0xff002564);

  // v2
  static const Color iconColor = Color(0xff8E9AAE);
  static const Color lineColor = Color(0xffDEE2E5);
  static const Color blue = Color(0xff227CE2);

  // Status colors for recruitment progress
  static const Color requestingAnAgent = Color(0xffFF8C00);
  static const Color application = Color(0xffFF6600);
  static const Color interviewRequest = Color(0xff1E90FF);
  static const Color interviewScheduling = Color(0xff00CED1);
  static const Color interviewDateConfirmed = Color(0xff32CD32);
  static const Color interviewCompleted = Color(0xff9ACD32);
  static const Color jobOffer = Color(0xffFF69B4);
  static const Color offerAccepted = Color(0xffDC143C);
}
