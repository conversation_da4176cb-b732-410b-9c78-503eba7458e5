import 'package:asiantech_link/layers/presentation/resources/config_export.dart';
import 'package:asiantech_link/layers/presentation/widgets/app_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'platform_text.dart';

class TitleTextField extends StatelessWidget {
  final String? title;
  final int? minLines;
  final int? maxLines;
  final int? maxLength;
  final TextEditingController? controller;
  final dynamic Function(String)? onChanged;
  final dynamic Function(String)? onSubmitted;
  final List<TextInputFormatter>? inputFormatters;
  final Color fillColor;
  final Widget? suffixIcon;
  final bool obscureText;
  final String? Function(String?)? validator;
  final String? errorText;
  final bool enabled;
  final Color? disableColor;
  final String? hintText;
  final Color? hintTextColor;
  final FocusNode? focusNode;
  final TextStyle? titleTextStyle;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final TextInputType? keyboardType;
  final String? initialValue;
  final bool isRequired;
  final bool autoFocus;
  final Color? borderColor;
  const TitleTextField({
    super.key,
    this.title,
    this.minLines,
    this.maxLines,
    this.maxLength,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.inputFormatters,
    this.suffixIcon,
    this.obscureText = false,
    this.fillColor = AppColors.white,
    this.validator,
    this.errorText,
    this.focusNode,
    this.enabled = true,
    this.disableColor,
    this.hintText,
    this.hintTextColor,
    this.titleTextStyle,
    this.textStyle,
    this.hintStyle,
    this.keyboardType,
    this.isRequired = false,
    this.autoFocus = false,
    this.initialValue,
    this.borderColor = Colors.transparent,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PlatformText(
                title ?? '',
                style: titleTextStyle ??
                    AppTextStyles.paragraph03(fontWeight: FontWeight.bold),
              ),
              if (isRequired)
                Text(
                  "*",
                  style: AppTextStyles.paragraph02v3(
                    color: Colors.red,
                  ),
                ),
            ],
          ),
        ),
        AppTextField(
          initialValue: initialValue,
          keyboardType: keyboardType,
          hintStyle: hintStyle,
          hintText: hintText,
          style: textStyle,
          controller: controller,
          onChanged: onChanged,
          onSubmitted: onSubmitted,
          inputFormatters: inputFormatters,
          maxLength: maxLength,
          minLines: minLines,
          maxLines: maxLines,
          fillColor: fillColor,
          focusNode: focusNode,
          suffixIcon: suffixIcon,
          obscureText: obscureText,
          validator: validator,
          errorText: errorText,
          enabled: enabled,
          disableColor: disableColor,
          hintTextColor: hintTextColor,
          autofocus: autoFocus,
          borderColor: borderColor,
        ),
      ],
    );
  }
}
