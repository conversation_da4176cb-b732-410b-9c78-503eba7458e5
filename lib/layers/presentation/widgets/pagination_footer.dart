import 'package:flutter/material.dart';
import '../resources/app_colors.dart';
import '../resources/app_text_styles.dart';

/// Reusable pagination footer widget that maintains the original styling
/// Shows page navigation with previous/next buttons and page numbers
class PaginationFooter extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final Function(int page) onPageChanged;
  final EdgeInsets? padding;
  final Color? primaryColor;
  final Color? iconColor;

  const PaginationFooter({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPageChanged,
    this.padding,
    this.primaryColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    // Always show pagination footer, even for single page

    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Previous button (show but disable for single page)
          if (totalPages > 1 && currentPage > 1)
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => onPageChanged(currentPage - 1),
              color: (primaryColor ?? AppColors.primary),
            )
          else if (totalPages > 1)
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: null, // Disabled
              color: (iconColor ?? AppColors.iconColor).withValues(alpha: 0.5),
            ),
          
          // Page numbers with original logic
          Builder(builder: (context) {
            final int current = currentPage;
            List<Widget> pageWidgets = [];

            // Handle single page case
            if (totalPages <= 1) {
              pageWidgets.add(_buildPageButton(context, 1, current, 1));
            } else {
              // Always add first page
              pageWidgets.add(_buildPageButton(context, 1, current, totalPages));

              if (totalPages > 5) {
              int blockStart, blockEnd;
              if (current <= 3) {
                blockStart = 2;
                blockEnd = 5;
              } else if (current >= totalPages - 2) {
                blockStart = totalPages - 4;
                blockEnd = totalPages - 1;
              } else {
                blockStart = current - 2;
                blockEnd = current + 2;
              }
              
              // Add ellipsis before block if needed
              if (blockStart > 2) {
                pageWidgets.add(_buildEllipsis());
              }

              // Add middle pages
              for (int i = blockStart; i <= blockEnd; i++) {
                pageWidgets.add(_buildPageButton(context, i, current, totalPages));
              }
              
              // Add ellipsis after block if needed
              if (blockEnd < totalPages - 1) {
                pageWidgets.add(_buildEllipsis());
              }
              
              // Always add last page
              pageWidgets.add(_buildPageButton(context, totalPages, current, totalPages));
            } else {
                // Show all pages if total is 5 or less
                for (int i = 2; i <= totalPages; i++) {
                  pageWidgets.add(_buildPageButton(context, i, current, totalPages));
                }
              }
            }

            return Row(children: pageWidgets);
          }),
          
          // Next button (show but disable for single page)
          if (totalPages > 1 && currentPage < totalPages)
            IconButton(
              icon: const Icon(Icons.arrow_forward),
              onPressed: () => onPageChanged(currentPage + 1),
              color: (primaryColor ?? AppColors.primary),
            )
          else if (totalPages > 1)
            IconButton(
              icon: const Icon(Icons.arrow_forward),
              onPressed: null, // Disabled
              color: (iconColor ?? AppColors.iconColor).withValues(alpha: 0.5),
            ),
        ],
      ),
    );
  }

  /// Build page button with original styling
  Widget _buildPageButton(BuildContext context, int page, int currentPage, int totalPages) {
    return GestureDetector(
      onTap: page != currentPage ? () => onPageChanged(page) : null,
      child: Container(
        width: 40,
        height: 40,
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: page == currentPage ? (primaryColor ?? AppColors.primary) : Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(
          child: Text(
            '$page',
            style: AppTextStyles.paragraph02v1(
              color: page == currentPage ? Colors.white : (primaryColor ?? AppColors.primary),
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ),
    );
  }

  /// Build ellipsis with original styling
  Widget _buildEllipsis() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Text(
        '…',
        style: AppTextStyles.paragraph02v1(
          color: iconColor ?? AppColors.iconColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

/// Extension to easily add pagination footer to any widget
extension PaginationFooterExtension on Widget {
  /// Add pagination footer below this widget
  Widget withPaginationFooter({
    required int currentPage,
    required int totalPages,
    required Function(int page) onPageChanged,
    EdgeInsets? padding,
    Color? primaryColor,
    Color? iconColor,
  }) {
    return Column(
      children: [
        this,
        PaginationFooter(
          currentPage: currentPage,
          totalPages: totalPages,
          onPageChanged: onPageChanged,
          padding: padding,
          primaryColor: primaryColor,
          iconColor: iconColor,
        ),
      ],
    );
  }
}

/// Helper class for pagination calculations
class PaginationHelper {
  /// Calculate total pages based on total items and items per page
  static int calculateTotalPages(int totalItems, int itemsPerPage) {
    if (totalItems <= 0 || itemsPerPage <= 0) return 1;
    return (totalItems / itemsPerPage).ceil();
  }

  /// Ensure page is within valid range
  static int clampPage(int page, int totalPages) {
    return page.clamp(1, totalPages);
  }

  /// Get start index for current page (0-based)
  static int getStartIndex(int page, int itemsPerPage) {
    return (page - 1) * itemsPerPage;
  }

  /// Get end index for current page (0-based, exclusive)
  static int getEndIndex(int page, int itemsPerPage, int totalItems) {
    final startIndex = getStartIndex(page, itemsPerPage);
    return (startIndex + itemsPerPage).clamp(0, totalItems);
  }
}
