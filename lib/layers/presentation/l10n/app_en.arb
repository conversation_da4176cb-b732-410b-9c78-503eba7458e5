{"@@locale": "en", "jp": "Japanese", "en": "English", "vi": "Vietnamese", "back": "Back", "basicInformation": "Basic Information", "save": "Save", "name": "Name", "firstName": "First name", "lastName": "Last name", "sex": "Sex", "male": "Male", "female": "Female", "unanswered": "Unanswered", "date_of_birth": "Date of birth", "year_of_birth": "Year of birth", "example": "Example: {value}", "nationality": "Nationality", "phoneNumber": "Phone number", "currentAddress": "Current address", "country": "Country", "states": "States", "city": "City", "passportNo": "Passport No.", "profile": "Profile", "add_to_favorites": "Add to Favorites", "remove_from_favorites": "Remove from Favorites", "key_no": "No", "key_ok": "Ok", "key_something_went_wrong": "Oops! Something went wrong", "key_an_error_occurred_during_communication": "An error occurred during communication", "connectionError": "It seems that the internet connection is offline.", "missingParameters": "Missing parameters.", "firstNameRequired": "First name is required.", "lastNameRequired": "Last name is required.", "dateOfBirthRequired": "Date of birth is required", "phoneNumberRequired": "Phone number is required", "phoneNumberInvalid": "Phone number is invalid", "nickname_invalid": "Nickname is invalid", "countryRequired": "Country is required", "key_error_code": "Error code", "oops": "Oops!", "didYouForgetYourPassword": "Forgot your password?", "resetInstructionsEmail": "Don't worry, we'll send you reset instructions to your email address.", "enterYourEmailAddress": "Enter your email address", "send": "Submit", "newUser": "New User?", "returnToLogin": "Return to Login", "verifyEmail": "Please verify your email address", "recruitment_overview": "Recruitment Overview", "clickTheLinkInTheEmail": "An email has been sent to your verified email address.\nPlease click the link in the email to verify your email address.", "resendVerificationEmail": "Resend verification email", "sportCareerPath": "Find Your Career Path in Sports", "jobPlacementServices": "Japan's only job matching service specializing in soccer team staff", "nameCompany": "合同会社 BeAT", "termsOfService": "Terms of service", "and": " and", "privacyPolicy": " Privacy policy", "agreeTo": "I agree to ", "include": "（This includes consent to providing registered information to companies seeking employees.）", "usedEmail": "This email address is already in use.", "createAnAccount": "Create an account", "alreadyHaveAnAccount": "If you already have an account, ", "sporpath": "<PERSON><PERSON><PERSON>", "email": "Email", "menu": "MENU", "email_too_long": "Email is too long (>= 203 characters)", "email_single_at_symbol": "Email must contain a single '@' symbol", "email_local_part_too_long": "Local part of the email is too long (>= 64 characters)", "email_invalid_format": "The email address format is invalid. Please enter a valid email address.", "email_consecutive_periods": "Email contains consecutive periods", "email_periods_invalid_positions": "Email has periods at invalid positions (start/end of local part or end of email)", "email_domain_no_ip": "Domain part of the email should not be an IP address", "password_length": "Password length must be between 8 and 20 characters", "password_full_width_characters": "Password contains full-width characters", "number_results": "{number} results", "sort": "Sort", "displayed_result": "Displayed result", "save2": "Save", "apply": "Apply", "application_status": "Application Status", "view_the_list_of_job_openings": "View all job listings for {companyName}", "job_information": "Job Information", "company_information": "Company Information", "application_information": "Application Information", "account_registration": "Account Registration", "login": "<PERSON><PERSON>", "user_information": "User Information", "email_address": "Email Address", "password": "Password", "representative_name": "Representative Name", "last_name": "Last Name", "first_name": "First Name", "representative_profile_image": "Representative Profile Image", "drop_here_to_upload_png_or_jpeg": "Drop PNG or JPEG file here to upload", "upload": "Upload", "basic_information": "Basic Information", "company_name": "Company Name", "number_of_employees": "Number of Employees", "state_city": "State/City", "address": "Address", "phone_number": "Phone Number", "contact_email_address": "Contact Email Address", "detailed_information": "Detailed Information", "company_profile_max_5000_chars": "Company Profile (max 5000 chars)", "business_description_max_5000_chars": "Business Description (max 5000 chars)", "holidays_and_benefits_max_1000_chars": "Holidays and Benefits (Maximum 1000 characters)", "company_website_url": "Company Website URL", "company_intro_page_url": "Company Introduction Page URL", "company_logo": "Company logo", "pr_photo": "PR Photo", "upload_here": "Drop PNG or JPEG file here to upload", "complete": "Complete", "congratulations_account_registration_complete": "Congratulations! Your account registration is complete. Please log in below.", "login_below": "<PERSON><PERSON>", "representative_full_name": "Representative Full Name", "next": "Next", "registration_complete": "Registration Complete", "congratulations_registration_complete": "Congratulations! Your account registration is complete.\nPlease log in below.", "search": "Search", "keep": "Keep", "createNew": "Create new", "recruitmentRequestList": "Case request list", "showAttendance": "<PERSON><PERSON><PERSON> pointed out unclear attendance", "date": "Date", "descending": "Descending", "ascending": "Ascending", "jobTitle": "Recruitment type", "salaryRange": "Salary", "postingPeriod": "Posting Period", "edit": "Edit", "createAccount": "Create an Account", "userNameOrEmail": "Username or email address", "cancel": "Cancel", "forgetPasswordUnderline": "Did you forget your password?", "rememberMe": "Remember Me", "chat_template": "<PERSON><PERSON>", "schedule_adjustment": "Schedule Adjustment", "employment_conditions": "Employment Conditions", "others": "Others", "source_station": "Source", "destination_station": "Destination", "vietnamese": "Vietnamese", "japanese": "Japanese", "interview_scheduling_in_progress": "Interview Date and Time Being Adjusted", "your_email_not_verified": "Your email is not verified", "verify_email_to_sign_in": "You need to verify your email address now in order to sign in to {appName}", "send_verification_email": "Send verification email", "enter_a_verification_code": "Enter a verification code", "verification_code_sent_content": "A verification code was just sent to your email address.", "enter_your_verification_code": "Enter your verification code", "submit": "Submit", "code_sent": "Code sent!", "success": "Success!", "please_enter_email": "Please enter Email", "please_enter_password": "Please enter Password", "email_or_password_is_incorrect": "Email or password is incorrect. Please check back.", "request_sent_success_message": "Request sent successfully, please check your mailbox", "email_address_register": "Email Address", "has_been_sent_to_your_registered_email_address": "An email has been sent to your registered email address. Please click the link in the email to verify your email address.", "back_to_the_top_page": "Back to the top page", "enter_the_verification_code": "Enter the verification code sent to you by email.", "confirmation_code": "Verification code", "verification_code_is_invalid": "The authentication code is invalid. Please check the code content and enter it again.", "have_not_received_your_verification_code": "Didn't receive your verification code?", "retransmission": "Resend", "logout": "Logout", "update_password": "Update Password", "old_password": "Old Password", "new_password": "New Password", "confirm_password": "Confirm Password", "please_enter_old_password": "Please enter old password", "please_enter_new_password": "Please enter new password", "please_enter_confirm_password": "Please enter confirm password", "confirm_password_not_match": "Confirm password doesn't match", "session_expired": "Your session has expired, please log in again to continue.", "password_update": "Password update", "logout_confirmation": "Are you sure you want to logout?", "password_requirements": "Password must contain at least one uppercase letter, lowercase letter, number, and symbol.", "verify_the_captcha": "Verify the Captcha", "input_text": "input text", "error_captcha_please_try_again": "Error <PERSON><PERSON><PERSON>, please try again", "profile_photo": "Profile Photo", "label_description": "Label Description", "revealed_when_matching": "Revealed When Matched", "public_worship": "Revealed Worship", "current_address_prefecture": "Current Address", "current_address_country": "(Country)", "current_address_state": " (State)", "current_address_city": " (City)", "port_no": "Port No.", "passport_photo": "Passport Photo", "education": "Educational background", "school_name": "School name", "school_type": "School type", "graduation_date": "Graduation date", "study_alcohol_study_half": "Study alcohol/study half", "language": "Language", "level": "Level", "skill": "Skill", "qualification": "Qualification", "qualification_name": "Qualification name", "date_of_acquisition": "Acquisition date", "experience": "Job Experience", "company_name_profile": "Company name", "department_position": "Department/job title", "duration_of_employment": "Duration of employment", "desired_conditions": "Desired conditions", "desired_job_type": "Desired job type", "desired_category": "Desired category", "desired_work_location": "Desired work location", "desired_salary": "Desired salary", "appealing_points_field": "Appealing points", "self_introduction_video": "Self-introduction video", "please_login_again_account_has_not_been_verified": "Please login again, a verification email will be sent to your registered email if your account has not been verified", "confirm_password_reset_successful": "Confirm password reset was successful, please check your email to receive a new password", "back_to_home": "Back to Home", "password_reset_instructions": "We have sent you instructions to reset your password. Please check your inbox.", "companyInformation": "Company Information", "companyName": "Company Name", "companyWebsiteUrl": "Company Website URL *SNS pages are also acceptable*", "capital": "Capital", "numberOfEmployees": "Number of Employees", "companyLocation": "Company Location", "contactEmail": "Contact Email Address", "firstNameIsRequired": "First name is required", "lastNameIsRequired": "Last name is required", "companyNameIsRequired": "Company name is required", "phoneNumberIsInvalid": "Phone number is invalid", "addressIsRequired": "Address is required", "countryIsRequired": "Country is required", "cityIsRequired": "City is required", "employeeTypeIsRequired": "Employee type is required", "pleaseEnterUrl": "Please enter URL", "urlIsInvalid": "URL is invalid", "capitalIsRequired": "Capital is required", "pleaseSelectCountryFirst": "Please select country first", "pleaseFillAllRequiredFields": "Please fill all required fields", "fromTo": "From {x} to {y}", "over1000": "Over 1000", "add_your_education": "Add your education", "faculty_department": "Faculty/Department", "skills_multiple_choices": "Skills (multiple choices)", "please_select": "Please select", "qualification_multiple_choices": "Qualifications (multiple choices)", "acquired_year": "Year acquired", "work_history": "Work History", "length_of_stay": "Length of stay", "currently_working_as_an_assistant": "Currently working in there", "add_desired_job_type": "Add desired job type", "preferred_work_location": "Preferred work location", "currency": "<PERSON><PERSON><PERSON><PERSON>", "payroll_department": "Payroll department", "add_your_requirement": "Add your requirement", "add_your_qualification": "Add your qualification", "add_your_language": "Add your language", "add_your_skill": "Add your skill", "add_your_experience": "Add experience", "appeal_point": "Appeal point", "please_enter_a_valid_url": "Please enter a valid URL", "deleteProfile": "Delete Profile", "editRecruitmentDetails": "Edit Recruitment Details", "catchphraseMax100": "Catchphrase (Maximum 100 characters)", "recruitmentDetailsMax10000": "Recruitment Details (Maximum 10000 characters)", "recruitmentType": "Recruitment Type", "recruitmentEmploymentType": "Recruitment Employment Type", "recruitmentWorkLocation": "Recruitment Work Location", "prefecture": "Prefecture", "addWorkLocation": "Add Work Location (Up to 3 locations)", "recruitmentSalary": "Recruitment Salary", "minimum": "Minimum", "maximum": "Maximum", "age": "Age", "gender": "Gender", "other": "Other", "addAddress": "Add Address (Up to 3 addresses)", "highestEducation": "Highest Education", "orHigher": "or Higher", "languageLevel": "Language Level", "addLanguage": "Add Language (Up to 2 languages)", "jobExperience": "Job Experience", "yearsOfExperience": "Years of Experience", "orMore": "or More", "jobSkills": "Job Skills", "skillLevel": "Skill Level", "addSkill": "Add Skill (Up to 3 skills)", "qualificationJobTitle": "Job Title", "qualificationScore": "Score", "pointsOrMore": "points or More", "jobListingName": "Job Listing Name *Not visible to job seekers", "listingStartDate": "Listing Start Date", "listingEndDate": "Listing End Date", "select": "Select", "deselect": "Deselect", "initialSupportCost": "Initial Support Cost", "displayCompanyPrText": "Display Company PR Text", "preview": "Preview", "saveEdits": "Save Edits", "fieldIsRequired": "This field is required", "createRecruitmentDetails": "Create Recruitment Details", "copyExistingRecruitment": "Copy Existing Recruitment", "recruitmentToCopy": "Recruitment to <PERSON><PERSON>", "copy": "Copy", "create": "Create", "recruitmentList": "Recruitment List", "displayExpiredRecruitment": "Display Expired Recruitment", "displayDate": "Display Date", "recruitmentJobTitle": "Recruitment Job Title", "salary": "Salary", "listingPeriod": "Listing Period", "applications": "Applications", "userInformation": "User Information", "detailedInformation": "Detailed Information", "contactPersonName": "Contact Person Name", "contactPersonProfileImage": "Contact Person Profile Image", "companyOverview": "Company Overview", "businessContent": "Business Content", "holidaysAndBenefits": "Holidays and Benefits", "branchCompanyWebsiteUrl": "Branch Company Website URL", "prVideoUrl": "PR Video URL ❖Youtube etc.", "companyPrPhoto": "Company PR Photo", "maximumCharacters": "(Maximum {x} characters)", "companyLogo": "Company logo", "mustLoginToAccess": "You must login to access this page", "deregister": "Deregister", "clear_all": "Clear all", "work_location": "Work location", "image": "Image", "annual_salary": "Annual salary", "skills": "Skills", "delete": "Delete", "enter_the_text_to_search_for": "Enter the text to search for", "latest_update": "Latest update:", "profile_completeness": "Profile completeness", "language_qualifications": "Language qualifications", "in_progress": "In progress", "appeal_points": "Appeal points", "set_up_an_introduction_agency": "Set an introduction recruitment agency", "private": "Private", "current_position": "Current position", "passport_image": "Passport image", "edit_educational_background": "Edit educational background", "language_and_qualifications": "Language and qualifications", "label": "Label", "edit_language": "Edit language", "edit_qualifications": "Edit qualifications", "edit_skills": "Edit skills", "edit_work_history": "Edit work history", "desired_employment_type": "Desired employment type", "edit_desired_conditions": "Edit your desire", "edit_appeal_point": "Edit appeal point", "deleteRecruitment": "Delete Recruitment", "title_of_the_recruitment_requirements": "Title of the recruitment requirements", "please_select_the_referral_agency": "Please select the recruitment agency you would like to work with for this application.", "to_add_a_referral_agency": "To add a recruitment agency", "click_here": "Click here", "recruit_date_time": "Posting ends in {dateLeft} days (until {dateTime})", "business_activities": "Business Overview", "work_location_interview_location": "Work/interview location", "desiredSalaryAmount": "Desired <PERSON> Amount", "useSavedConditions": "Use Saved Conditions", "close": "Close", "searchConditions": "Search Conditions", "setSearchConditions": "Set Search Conditions", "minAge": "Minimum", "maxAge": "Maximum", "experienceJobTitle": "Experience Job Title", "experienceYears": "Years of Experience", "orMoreYears": "or More Years", "addWorkHistory": "Add Work History (Up to 3)", "languages": "Languages", "score": "Score", "addQualification": "Add Qualification (Up to 3)", "addJobSkills": "Add Job Skills (Up to 3)", "employmentConditions": "Employment Conditions", "employmentType": "Employment Type", "recruitmentCountry": "Country", "recruitmentLocation": "Recruitment Location", "recruitmentPrefecture": "Prefecture", "salaryMin": "Minimum", "salaryMax": "Maximum", "transactionConditions": "Transaction Conditions", "introductionFeeCap": "Introduction Fee Cap", "upTo": "Up To", "saveSearchConditions": "Save Search Conditions", "educationHighSchoolOrHigher": "High School or Higher", "nationalityVietnam": "Vietnam", "languageJapanese": "Japanese", "searchResults": "Search Results", "savedSearchConditions": "Saved Search Conditions", "use": "Use", "inputFilterName": "Enter Filter Name", "month_year_format": "yyyy-MM", "edit_basic_information": "Edit basic information", "phone_number_can_not_empty": "Phone number can not empty", "phone_text_field_hint_text": "Example: 0901234567", "country_prefecture_city": "Country/Prefecture/City", "country_and_prefecture": "Country and prefecture", "city_and_place_name": "City and place name", "results": "Results", "item": "<PERSON><PERSON>", "now": "Now", "add_a_job": "Add a job", "add_your_preferred_location": "Add your preferred location", "annual_salary_with_icon": "※ Annual salary", "amount": "Amount", "currency_unit": "Currency unit", "age_not_appropriate": "Age not appropriate", "month_jan": "Jan", "month_feb": "Feb", "month_mar": "Mar", "month_apr": "Apr", "month_may": "May", "month_jun": "Jun", "month_jul": "Jul", "month_aug": "Aug", "month_sep": "Sep", "month_oct": "Oct", "month_nov": "Nov", "month_dec": "Dec", "email_address_for_confirmation": "Email address (for confirmation)", "email_not_match": "Email not match", "all": "All", "offered": "Offered", "interview_in_progress": "Interview in progress", "applied": "Applied", "under_consideration": "Under consideration", "not_successful": "Not Passed", "dateOfBirth": "Date of Birth", "countryPrefectureCity": "Country/Prefecture/City", "details": "Details", "experienceAndSkills": "Experience & Skills", "schoolName": "School Name", "faculty": "Faculty", "workExperience": "Work Experience", "qualifications": "Qualifications", "desiredJobTitle": "Desired Job Title", "desiredWorkLocation": "Desired Work Location", "workLocation": "Work Location", "desiredSalary": "<PERSON>d <PERSON>", "interviewRequest": "Interview Request", "engineerPRText": "Engineer PR Text", "scoutMessage": "Scout Message", "interviewRequestNotification": "Displayed when notifying the candidate of an interview request", "presentJobPosting": "Present Job Posting", "referral_agency_selection": "Select Recruitment Agency", "less_than_1_year": "Less than 1 year", "less_than_2_year": "Less than 2 years", "less_than_3_year": "Less than 3 years", "less_than_5_year": "Less than 5 years", "more_than_5_years": "More than 5 years", "end_date_must_be_later_than_start_date": "End date must be later than start date", "company_details": "Company details", "apply_here_content": "If you are looking for candidates?", "get_started": "Let's get started. First, tell us about yourself.", "fill_company_info": "Please fill in your company information.", "register": "Register", "referral_agency": "Recruitment agency", "add_a_referral_agency_content": "To add a recruitment agency, enter the email address provided by the recruitment agency", "edit_referral_agency": "Edit Recruitment Agency", "currently_configured_referral_agencies": "Currently configured recruitment agencies", "new_additions": "Add new", "interview_request": "Interview request", "interview_date_confirmed": "Interview confirmed", "interview_completed": "Interview completed", "rejected": "Rejected", "acceptance_of_job_offer": "acceptance of job offer", "application_decline": "Application declined", "declined_interview": "Interview declined", "declining_job_offer": "Decline job offer", "job_offer_from_other_company": "Accepted another offer", "recruitment_requirements_catchphrase": "Recruitment requirements catchphrase", "salary_conditions": "Salary", "location": "Location", "host_company_information": "Host company information", "company_address": "Email address", "similar_job_postings": "Similar Job Postings", "apply_to_complete_your_application": "If you are interested in the job posting, simply tap \"Apply\" to complete your application.", "chat": "Cha<PERSON>", "meeting": "Meeting", "companies_are_currently_recruiting": "The company is currently in the selection process.\nLet's see if there are any other job postings that interest you.", "received_an_interview_request_from_a_company": "You have received an interview request from the company.\nPlease chat with the representative and then tap the schedule button to register your interview date and time.", "interview_date_and_time_has_been_confirmed": "The interview date and time has been confirmed.\nTapping the Google Calendar icon will add the appointment to your Google Calendar.", "wish_to_change_the_date_and_time": "The interview date and time has been confirmed.\nIf you wish to change the date and time, please chat with the assigned contact and re-register the interview schedule.", "check_the_employment_conditions": "Check the employment conditions", "add_meeting_to_google_calendar_successfully": "Add meeting to google calendar successfully", "place_of_work": "Work Location", "scheduled_to_join": "scheduled to join", "check_the_employment_contract": "Check the employment contract", "sign": "Sign", "confirmed_the_employment_contract_please_sign_it": "Once you have confirmed the employment contract, please sign it.(If you upload signature image, please upload image with transparency background)", "clear": "Clear", "you_have_received_an_offer_from_the_company": "Congratulations!\nYou have received a job offer from the company.\nPlease review the hiring conditions.\nIf you have any questions, please confirm with the person in charge via chat.\nOnce you decide to join the company, tap the \"Agreement\" button to finish the process.\n\nNote!\nAfter accepting the job offer, you will not be able to apply to other companies.\n\nIf you wish to pursue other job opportunities, tap the \"Decline Offer\" button.", "you_have_received_a_letter_of_acceptance_from_the_company": "Congratulations! You have received a letter of acceptance from the company. \nPlease sign it. \nOnce you have signed, the process is complete.", "job_offer_acceptance_contract": "job offer acceptance contract", "name_of_host_company": "(Name of host company)", "introducing_agency_name": "(Name of introducing recruitment agency)", "your_signature": "Your signature", "select_image": "Select image", "enter_search": "Enter search", "filter_by": "Filter <PERSON>", "work_place": "Work place", "select_work_place": "Select work place", "select_employee_type": "Select employee type", "select_job_type": "Select job type", "select_salary": "Select salary", "select_skill": "Select skill", "select_language": "Select language", "select_job_code": "Select job code", "recruits": "Recruits", "enter_search_text": "Enter Search Text", "select_country": "Select Country", "filter": "Filter", "newest": "Newest", "oldest": "Oldest", "engineers_in_selection": "Engineers in Selection", "job_post_management": "Job Post Management", "engineer_search": "Engineer Search", "engineers_under_consideration": "Engineers Under Consideration", "profile_management": "Profile Management", "account_management": "Account Management", "notifications": "Notifications", "selected_engineers": "Selected Engineers", "view_host_company": "View Host Company", "host_company_management": "Host Company Management", "back_to_supported_host_companies": "Back to Supported Host Companies", "job_applications": "Job Applications", "job_search": "Job Search", "jobs_under_consideration": "Jobs Under Consideration", "people_applying": "{value} people applying", "job_info": "Job Info", "about": "About", "job_role": "Job Role", "employment_type": "Employment Type", "educational_background": "Educational background", "points": "Points", "overview": "Overview", "benefits": "Benefits", "website": "Website", "pr_video": "PR video URL", "map": "Map", "related_jobs": "Related Jobs", "select_a_referral_agency": "Select a recruitment agency", "saved": "Saved", "notice": "Notice", "account_logout_on_other_tab": "Account logout on other tab, please login again", "change_other_account": "Change other account, please reload again", "sort_by": "Sort by:", "from_age": "From age", "to_age": "To age", "current_address": "Current address", "select_address": "Select address", "highest_level_of_education": "Highest level of education", "select_level": "Select a level", "select_career_status": "Select career status", "select_skills": "Select skills", "work_conditions": "Work conditions", "select_employment_type": "Select employment type", "select_location": "Select location", "from": "From", "to": "To", "career": "Career", "select_state_city": "Select state", "fresher": "FRESHER", "junior": "JUNIOR", "middle": "MIDDLE", "senior": "SENIOR", "career_skills": "Career & Skills", "x_results": "{x} results", "show_x_results": "Show {x} results", "applicants": "Applicants", "updated_at": "Updated at", "please_register_interview_date_time": "Please register the interview date and time", "interview_date_time_begin": "Interview date and time begin", "requesting_agent": "Requesting an agent", "applying": "Applying", "requesting_interview": "Requesting an interview", "scheduling_interview_date": "Interview scheduling", "job_offering": "Job offering", "job_offer_accepted": "Job offer accepted", "job_offer_accepted_text_dialog": "Accept", "currently_employed": "Currently employed", "not_passed": "Not passed", "application_declined": "Application declined", "offer_declined": "Offer declined", "offer_from_another_company": "Offer from another company", "enter_recruit_title_user_name": "Enter recruit title, user name", "enter_user_name": "Enter username", "recruitment_requirements": "Recruitment requirements", "select_recruit": "Select recruit", "recruit_status": "Recruit status", "select_status": "Select status", "register_selection_results_update_status": "Please register the selection results and update the selection status.", "adjust_interview_schedule_chat_register_time": "Please adjust the interview schedule via chat and register the interview time.", "interview_time_x": "Interview time: {x}", "register_interview_results_update_status": "Please register the interview results and update the selection status.", "sign_contract_accept_offer": "Please sign the contract to accept the offer.", "check_contract_complete_job_entry": "Please check the contract and complete the job entry procedure.", "status": "Status", "recruitment": "Recruitment", "links": "Links", "present": "Present", "month": "Month", "year": "Year", "like": "Like", "liked": "Liked", "select_introduction_agency": "Select a recruitment agency", "introduction_fee": "Introduction fee", "show_pr_details": "Show PR details", "scout_message": "Scout Message", "interview_request_notification": "This will be displayed when notifying job seekers of an interview request.", "enter_scout_message": "Enter your scout message", "recruitment_requirements_to_present": "Recruitment requirements to be presented", "offer": "Offer", "job_detail": "Job detail", "contract": "Contact", "signature": "Signature", "day_meeting": "Day meeting: ", "copy_existing": "Copy existing", "upload_image_max_5mb": "Upload Image\n(max 5MB)", "catchphrase": "Catchphrase", "enter_catchphrase": "Enter your Catchphrase", "enter_job_description": "Enter job description", "available_positions": "Available Positions", "select_position": "Select a position", "work_locations": "Work locations", "salary_range": "Salary range", "recruit_title": "Recruit title", "enter_recruit_title": "Enter recruit title", "publication_period": "Publication Period", "start_date": "Start date", "end_date": "End date", "personal_characteristics_conditions": "Personal characteristics conditions", "select_characteristics": "Select a characteristic", "job_description_max_10000": "Job description (Maximum 10000 characters)", "last_academic_background": "Last academic background", "license": "License", "initial_cost": "Initial cost", "outsourcing_cost": "Outsourcing cost", "licenses": "Licenses", "confirmation_notice": "Confirmation notice", "are_you_sure_delete_recruit": "Are you sure you want to delete this recruit?", "start_date_less_than_end_date": "Start date must be less than end date", "end_date_greater_than_start_date": "End date must be greater than start date", "title_of_recruitment_guideline": "Title of recruitment guideline", "show_expired_recruits": "Show expired recruits", "category": "Category", "period": "Period", "create_recruit": "Create Recruit", "warning": "Warning", "update_language": "Update language", "newText": "New", "currency_required": "Currency is required", "no_companies_found": "No companies found", "write_something": "Write something", "contact": "Contact", "members": "members", "undecided": "Undecided", "read": "Read", "unread_message_from_here": "Unread message from here", "stage": "Stage", "interview_date_completed": "Interview date completed", "info": "Info", "compare": "Compare", "self_promotion": "Self-promotion", "comment_from_referral_agency": "Comment from the recruitment agency", "read_more": "Read more", "work_experiences": "Work Experiences", "matching_compare": "Matching Compare", "match_score": "Match Score", "match_profile": "Match profile", "no_match": "No match", "reject_applicant_confirmation": "Are you sure you want to reject this applicant?", "reject_applicant_warning": "Once you reject someone, you cannot undo it.", "interview_request_confirmation": "An interview request will be sent to this applicant. Is this OK?", "interview_request_detail": "If the applicant accepts the interview request, you will be able to send chat messages and register the interview date and time.", "set_schedule_interview": "Set schedule interview", "calendar": "Calendar", "recruitment_condition": "Recruitment Condition", "position": "Position", "scheduled_entry_date": "Scheduled date of entry into the company", "select_date": "Select a date", "data_not_found": "Data not found", "schedule_interview": "Schedule Interview", "matched": "Matched", "position_is_required": "Position is required", "country_is_required": "Country is required", "state_is_required": "State is required", "payroll_price_is_required": "Payroll price is required", "payroll_code_is_required": "Payroll code is required", "date_is_required": "Date is required", "work_location_is_required": "Work location is required", "please_enter_value": "Please enter value", "salary_is_required": "Salary is required", "adoption": "Adoption", "reject": "Reject", "un_match": "UnMatch", "pending_signature": "Pending signature", "download_employment_contract": "Download employment contract", "engineer_full_name": "(Engineer Full Name)", "sign_the_contract": "Sign the contract", "engineer": "Engineer", "select_file_10_MB": "File size limit exceeded. Please upload an image file no larger than 10MB", "select_file_format_not_supported": "Select File Format Not Supported", "employer": "Employer", "find_dream_job": "Find Your Dream Job\nEasily with AsianTech.Link", "enter_job_name": "Enter job name", "list_best_jobs": "List best jobs", "see_all": "See all", "list_best_companies": "List best companies", "top_featured_jobs": "Top Featured Jobs", "connecting_talent": "Connecting Talent\nwith AsianTech.Link", "list_featured_engineers": "List Featured Engineers", "jobs": "Jobs", "please_login_to_continue": "Please login to continue", "search_skill_jobs": "Search skill jobs", "settings": "Settings", "please_select_job_code_first": "Please select job code first", "please_select_currency": "Please select currency", "sending": "Sending", "please_login_again": "\nPlease login again", "please_enter_the_verification_code": "Please enter the verification code", "please_enter_the_6_digit_verification_code": "Please enter the 6-digit verification code.", "image_less_than_5mb": "Image must be less than 5MB", "image_less_than_x_mb": "Image must be less than {x}MB", "pageNotFound": "404 - Page Not Found", "internalServerError": "500 - Internal Server Error", "goToHome": "Go to Home", "age_requirement": "Age must be greater than 16", "field_cannot_be_empty": "This field cannot be empty", "address_kicotrans": "Tokyo, Ho Chi Minh", "items": "items", "no_items_found": "No items found", "the_list_is_currently_empty": "The list is currently empty.", "something_went_wrong": "Something went wrong", "try_again": "Try again", "translate_to_value": "Translate to: {value}", "language_english": "English", "language_vietnamese": "Vietnamese", "language_japanese": "Japanese", "show_original": "Show original", "translation_limit_reached": "You have reached the limit of translatable messages. Please try again later.", "facebook_get_email_permission": "Email empty please allow Facebook get email permission", "continue_with_facebook": "Continue with Facebook", "continue_with_linkedin": "Continue with LinkedIn", "continue_with_zalo": "Continue with <PERSON><PERSON>", "continue_with_whatsapp": "Continue with WhatsApp", "facebook_login_blocked": "Facebook Login Blocked", "it_seems_that_a_browser_extension_is_blocking": "It seems that a browser extension is blocking the connection to Facebook, preventing the login process. Please disable the blocking extension (such as an ad blocker) and try logging in again.", "location_x": "Location {x}", "company_location": "Company location", "age_range_invalid": "Age range invalid", "min_value_less_than_max": "Minimum value must be less than the maximum value", "other_skills": "Other Skills", "horenso_skills": "Ho-Ren-So Skills", "search_engineers": "Search Engineers", "city_and_country": "City and Country", "add": "Add", "address_x": "Address {x}", "add_condition": "Add condition", "please_enter_your_email_address": "Please enter your email address to complete your login.", "or": "or", "whatsAppVerification": "WhatsApp Verification", "pleaseSelectPhoneNumberToVerifyWithWhatsApp": "Please select phone number to verify with WhatsApp:", "addOtherPhoneNumber": "Add other phone number", "confirm": "Confirm", "pleaseEnterYourPhoneNumberInWhatsApp": "Please enter your phone number in WhatsApp:", "pleaseEnterTheVerificationCode": "Please enter the verification code", "enterTheVerificationCodeSentToYourWhatsApp": "Enter the verification code sent to your WhatsApp.", "whatsAppPhoneNumber": "WhatsApp Phone number", "confirmationCode": "Confirmation code", "years_of_experience": "Years of Experience", "job_description": "Job Description", "self_evaluation": "Self-Evaluation", "not_entered": "Not Entered", "partially_entered": "Partially Entered", "entered": "Entered", "self_evaluation_project_management_skills": "Self-Evaluation of Project Management Skills", "edit_self_assessment": "Edit Self-Assessment", "work_mode": "Work Preference", "facebook_url": "Facebook URL", "linkedin_url": "LinkedIn URL", "whatsapp_url": "WhatsApp URL", "self_assessment": "Self-Assessment", "remote_work_skills": "Remote Work Skills", "years_of_experience_remote_work": "Years of Experience in Remote Work", "remote_work_responsibilities": "Remote Work Responsibilities", "edit_self_assessment_remote_work_skills": "Edit Self-Assessment of Remote Work Skills", "communication_skills": "Communication Skills", "social_style": "Social Style", "driving": "Driving", "expressive": "Expressive", "amiable": "Amiable", "analytical": "Analytical", "self_evaluation_communication_skills": "Self-Evaluation of Communication Skills", "interview": "Interview", "project_management_skills": "Project Management Skills", "reporting_communication_skills": "Reporting & Communication Skills", "global_skills": "Global Skills", "overseas_work_experience": "Overseas Work Experience", "not_experienced": "Not Experienced", "experienced": "Experienced", "remote_work_skills_evaluation": "Remote Work Skills Evaluation", "global_responsiveness_skills_evaluation": "Global Responsiveness Skills Evaluation", "communication_skills_evaluation": "Communication Skills Evaluation", "reporting_communication_skills_evaluation": "Reporting & Communication Skills Evaluation", "project_management_skills_evaluation": "Project Management Skills Evaluation", "please_select_option": "Please select the one option out of five that best describes what you typically do in your daily actions for each question from 1 to 3.", "based_on_your_selections": "Based on your selections, your {value} level will be assessed, and your visibility to companies will increase.", "social_style_description": "Global responsiveness skills involve social styles, categorizing human behavior into four communication patterns:", "driving_description": "Driving: Assert opinions while controlling emotions", "expressive_description": "Expressive: Express emotions vividly through voice and behavior", "amiable_description": "Amiable: Listen and prioritize overall harmony", "analytical_description": "Analytical: Value time, data, facts, and theories", "reportingCommunicationSkills": "Reporting & communication skills", "you_have_not_saved_your_changes": "Your data has not been saved. Are you sure you want to leave the page?", "select_recruitment_guideline": "Please select the recruitment guideline from which you are copying.", "needs_improvement": "Needs improvement", "standard": "Standard", "good": "Good", "years_experience_remote_work": "Years of Experience in Remote Work", "under_x_years": "Under X years", "work_experience_overseas": "Work experience overseas", "none": "None", "yes": "Yes", "see_more": "See More", "years_x": "{x} years", "insufficient": "Insufficient", "weak": "Weak", "fair": "Fair", "superior": "Superior", "acceptable": "Acceptable", "delete_account": "Delete Account", "are_you_sure_delete_account": "Are you sure you want to delete your account?", "item_with_index": "Item {index}", "question_with_index": "Question {index}", "haveSomethingErrorPleaseTryAgain": "Have something error please try again", "not_set": "Not set", "undetermined": "Undetermined", "place_of_employment": "Place of employment", "area_of_recruitment": "Area of recruitment", "job_position": "Job Position", "closed": "Closed", "double_check_fields": "Please double check the fields: {x}", "check_entered_information": "Please check your entered information", "information_format_error": "Some information is not in the correct format", "location_required": "Location is required", "number_of_employees_required": "Number of Employees is required", "company_overview": "Company Overview (Maximum 5000 characters)", "business_content": "Business Content (Maximum 5000 characters)", "no_data_exist": "No data exists", "expired": "Expired", "other_license": "Other license", "social_style_engineer_description": "Social Style refers to a communication pattern that categorizes people's behaviors into four types:", "driving_engineer_description": "Driving: Assertive in expressing opinions while suppressing emotions.", "expressive_engineer_description": "Expressive: Demonstrates strong emotions through voice and demeanor.", "amiable_engineer_description": "Amiable: Listens to others' opinions and values overall harmony.", "analytical_engineer_description": "Analytical: Prioritizes time, data, facts, and logical reasoning.", "no": "No", "accept_your_job_offer": "Are you sure you want to accept this job offer?", "accept_your_job_offer_sub_title": "Attention:\nAfter accepting this job offer, you will not be permitted to apply to other companies.", "expected_date_of_joining": "Expected date of joining", "agreement": "Agreement", "congratulations_on_your_agreement": "Congratulations on your agreement!\nThe company has sent you the job acceptance and employment contract, please sign it.\nOnce the signature is completed, all procedures will be finalized", "check_the_employment_contract_agreement": "Check the employment contract", "select_year": "Select year", "qualification_point_option": "Qualification point (option)", "apply_job": "Apply", "job_category": "Job Category", "job_category_is_required": "Job category is required", "company_info_management": "Company Information Management", "employment_form": "Form of Employment", "recruitment_conditions": "Recruitment Conditions", "notify_job_offer": "Notify Job Offer", "for_company_click_here": "For Company\nclick here", "for_engineer_click_here": "For Engineer\nclick here", "update": "Update", "check_job_offer_contract_and_complete_onboarding": "Please check the job offer acceptance contract and complete the onboarding procedures.", "interview_request_sent": "Interview request has been sent.", "poor": "Poor", "average": "Average", "very_good": "Very Good", "excellent": "Excellent", "determine_recruitment_conditions": "Please determine the recruitment conditions.", "important_notice": "[Important]", "prefecture_city": "Country, City", "recruitment_conditions_note": "The content decided here will be directly notified and presented to the successful candidate. You can edit the conditions until they are accepted by the candidate, but any edits will be notified to them. Therefore, please ensure to get their consent before making changes to the conditions.", "createAccountUser": "Create a new account", "view_more": "View more", "user_info_registered": "User information has been registered.", "password_updated": "New password has been updated.", "recruitment_deleted": "Recruitment has been deleted.", "recruitment_registered": "Recruitment has been registered.", "basic_info_registered": "Basic information has been registered.", "detailed_info_registered": "Detailed information has been registered.", "home_page": "Home Page", "engineer_list": "Engineer List", "keyword": "Keyword", "engineer_name": "Engineer Name", "highest_education": "Highest Education", "registration_date": "Registration Date", "registrar": "Registrar", "action": "Action", "id": "ID", "engineer_information_list": "Engineer Information List", "select_registrar": "Select Registrar", "row_per_page": "Row per page", "page_info": "{currentPage} of {totalPage}", "selected_countries": "Selected countries/ states", "login_failed": "Login Failed", "are_you_sure_want_to_delete": "Are you sure you want to delete?", "unselect_all": "Unselect All", "share_data_cv_and_information": "Share Data CV and Information", "edit_sent_email": "<PERSON> <PERSON><PERSON>", "email_title_up_to_100_characters": "Email Title (up to 100 characters)", "email_body_up_to_number_characters": "Email Body (up to {number} characters)", "email_type": "Email Type", "personal_information_consent_email": "Personal Information Consent Email", "automatic_send_time": "Automatic Send Time", "day_of_week": "Day of the Week", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "automatic_send_on_off": "Automatic Send ON/OFF", "operation": "Operation", "delete_selected_email": "Delete Selected Email", "new_registration": "New Registration", "personal_information_consent": "Personal Information Consent", "notification": "Notification", "daily": "Daily", "weekly": "Weekly", "type_time_send": "Type Time Send", "noRepeat": "No Repeat", "repeatWeekday": "Repeat Weekday", "repeatByMonth": "Repeat By Month", "repeatByYear": "Repeat By Year", "dayEveryMonth": "{day} of every month", "dayMonthEveryYear": "day {day}/{month} every year", "email_title": "Email Title", "privacy_policy_agreement": "Agreement to Privacy Policy", "privacy_policy_description": "This system appropriately protects and strictly handles engineers' personal information. Based on the following policy, engineers' personal information will be utilized to achieve matching with companies.", "privacy_policy_point_1": "1. Purpose of Use of Personal Information: To provide scouting from companies to engineers and to improve matching services.", "privacy_policy_point_2": "2. Information Protection: Appropriate security measures will be taken to prevent information leaks.", "privacy_policy_point_3": "3. Information Sharing: Engineers' profile information will be provided to companies using this system. However, detailed information such as name and contact details will only be shared with the engineer's permission.", "privacy_policy_details_prompt": "For more details, please check the full Privacy Policy.", "privacy_policy_consent_message": "I agree to the Privacy Policy and consent to receive scouting offers from companies.", "agree_and_continue": "Agree and Continue", "disagree": "Disagree", "consent_thank_you_message": "Thank you for agreeing to the Privacy Policy. You will now start receiving scouting offers from companies.", "disagree_notice": "You cannot use AsianTech.Link services without agreeing to the Privacy Policy.", "details_prompt": "For more details, ", "please_review": "please review ", "the_full_privacy_policy": "full privacy policy", "notify_mail_list": "Notify Mail List", "please_carefully_review_the_email_content": "You are about to send an email to multiple recipients. Please carefully review the email content to ensure there is no sensitive information or errors. Are you sure you want to proceed with sending the email?", "accountRegistration": "Account Registration", "undecidedNegotiable": "Undecided/Negotiable", "initial_support_cost_required": "Initial support cost is required", "support_details_required": "Support details are required", "enter_html": "Enter HTML", "import": "Import", "your_text_here": "Your text here...", "email_test_receive_email": "Email test receive email", "this_test_email_cannot_be_empty": "This test email cannot be empty", "select_company": "Select Company", "test_email_sent_title": "Test Email Sent Successfully!", "test_email_sent_body": "We have sent a test email to the specified email address.", "please_login_engineer_account_to_continue": "Please login engineer account to continue", "please_login_company_account_to_continue": "Please login company account to continue", "please_login_management_account_to_continue": "Please login management account to continue", "for_engineer": "For Engineer", "selection_status": "Selection Status", "total_companies": "Total {x} Companies", "total_applications": "Total Applications {x}", "unread_applications": "Unread Applications {x}", "unprocessed_applications": "Unprocessed Applications {x}", "active_job_listings": "Active Job Listings: {x}", "total_applicants": "Total Applicants: {x}", "new_applicants": "New Applicants: {x}", "unprocessed_tasks": "Unprocessed Tasks: {x}", "last_status_update": "Last Status Update: {x}", "unread_messages_ascending": "Unread Messages Ascending", "unread_messages_descending": "Unread Messages Descending", "support_company_list": "List of Supported Companies", "yes_confirm": "Yes", "cancel_application_confirm": "Are you sure you want to cancel your application?", "cancel_interview_confirm": "Are you sure you want to cancel the interview request?", "cancel_job_offer_confirm": "Are you sure you want to decline the job offer?\nPlease note that once declined, this action cannot be undone.", "cancel_image_confirm": "Are you sure you want to cancel the image?", "cancel_meeting_confirm": "Are you sure you want to cancel the interview?", "more_than_1_year": "More than 1 year", "more_than_2_years": "More than 2 years", "more_than_3_years": "More than 3 years", "read_all": "Read all", "@read_all": {"description": "Text for read all notifications button"}, "notification_unread_messages": "You have unread messages from sender_name.", "notification_document_screening": "Please register the document screening result for sender_name.", "notification_interview_schedule": "Please register the interview date and time with sender_name.", "notification_interview_result": "Please register the interview outcome for sender_name.", "notification_sign_contract": "Please sign the offer acceptance and employment contract with sender_name.", "notification_unread_messages_company": "You have unread messages from sender_name.", "notification_interview_request": "Please respond to the interview request from sender_name.", "notification_job_offer": "Please respond to the job offer from sender_name.", "company_description": "Job description", "for_recruitment_agency": "For Recruitment Agency Companies", "edit_recruitment_agency": "Edit Recruitment Agency", "confirm_delete_recruitment_agency": "Are you sure you want to delete this recruitment agency?", "recruitment_agency_edit_registered": "Recruitment agency has been registered.", "update_currency": "Update C<PERSON><PERSON>cy", "default_currency_not_set": "If the user has not set a default currency, the currency of the country they are accessing from will be displayed.", "nickname": "Nickname", "education_name": "{school_name} {level_of_education}", "selectRecruitmentAgency": "Select Recruitment Agency", "addRecruitmentAgencyByEmail": "Add Recruitment Agency by Email Address", "addRecruitmentAgency": "Add Recruitment Agency", "manageRegisteredRecruitmentAgencies": "Manage Registered Recruitment Agencies", "registered_recruitment_agency_search": "Registered Recruitment Agency Search", "register_recruitment_agency_information": "Registering Recruitment Agency Information", "job_description_placeholder": "After joining ○○○○○ Corporation, I have been engaged in system development in the financial industry for approximately x years, handling design, testing, and maintenance operations based on requirement definitions.\n\nSince around 20xx, I have been involved in upstream processes, including conducting hearings with clients and defining requirements.\n\nAdditionally, as a leader or sub-leader of projects with xx to xx members, I have gained experience in overall progress management and team member supervision.\n\nBy providing appropriate instructions to team members and coordinating with clients, I have been able to ensure system quality while delivering projects on schedule.", "required_fields": "Required <PERSON>", "email_verified_successfully": "Email verification has been completed", "email_verified_successfully_sub_title": "Thank you for your cooperation. Please continue using the service.", "home": "Home", "under_selection": "Under Selection", "recruiting": "Recruiting", "mypage": "My Page", "asia_it_talent_service": "Asia-focused IT Talent Introduction Service", "job_openings": "{x} job openings available", "view_all_jobs": "View all jobs", "job_categories": "Job Categories", "under_selection_status": "Under Selection", "contracted": "Contracted", "public": "Public", "view_details": "View Details", "number_of_people": "{x} people", "under_selection_engineers": "Engineers Under Selection", "new_message": "New Message", "applications_count": "Number of Applications", "under_selection_count": "Number of Ongoing Selections", "last_updated": "Updated", "ai_recommended_engineers": "AI Recommendations", "recently_registered_engineers": "New Engineers", "trending_engineers": "Trending Engineers", "loading": "Loading...", "company_info": "Company Information", "language_currency_settings": "Language & Currency Settings", "selected": "Selected", "job_offer_contract": "Job Offer Contract", "update_password_success": "Password updated successfully, please login again.", "prefecture_states": "Prefecture/States", "recruitmentAgencyFee": "Recruitment Agency Fee", "registrationRecruitmentAgencyFee": "Recruitment Agency Registration Fee", "recruitmentAgencies": "Recruitment Agencies", "recruitmentAgencyInfo": "Recruitment Agency Information", "monthlySupportFee": "Monthly Support Fee", "supportDetails": "Support Details (Maximum 5000 characters)", "monthly_support_fee_required": "Monthly support fee is required", "registered_recruitment_agency_info": "Recruitment Agency Information has been registered.", "select_recruitment_agency": "Select Recruitment Agency", "receiving_company_support_organization": "Recruitment Agency", "supportAgency": "Recruitment Agency", "support_organizations": "Recruitment Agencies", "save_temporarily": "Save Temporarily", "draft": "Draft", "continue_to_request_interview": "Continue to request interview", "view_engineer_information": "View Engineer Information", "working_hours": "Working Hours", "view_less": "View less", "select_work_mode": "Select Work Mode", "cancel_job_offer": "Cancel", "employment_status": "Employment Status", "technical_background": "Technical Background", "sales_memo": "Sales Memo", "approved": "Approved", "not_approved": "Not Approved", "unapproved": "Unapproved", "available": "Available", "not_employed": "Not Employed", "enter_memo": "Enter memo", "denied": "Denied", "resume_export": "Resume Export", "decline": "Decline", "proceed": "Proceed", "filter_conditions": "Filter options", "relevant_engineer": "Applicable engineer", "displayed_range_engineers": "Displaying {start} to {end} engineers", "interview_adjustment_in_progress": "Interview Adjustment in Progress", "residence": "Residence", "employment_status_company": "Employment Status", "recent_occupation": "Recent Occupation", "development_skills": "Development Skills", "last_login": "Last Login", "language_level_tooltip_1": "<PERSON><PERSON><PERSON> (Chat Only)\nCharacteristics: A level where one can communicate via chat (text-based) using simple English phrases and words.\nConversations are at the greeting level.", "language_level_tooltip_2": "Intermediate (General Conversation Level)\nCharacteristics: A level where one can communicate in English for everyday life and travel.\nCan understand a few pages of design documents, but requires translation tools or takes time for anything beyond that.", "language_level_tooltip_3": "Advanced (Work-Ready Level)\nCharacteristics: A level where one can communicate smoothly in English in business settings.\nCan understand design documents, contracts, and technical documents.", "language_level_tooltip_4": "Native (Business Level)\nCharacteristics: Possesses skills equivalent to or close to a native speaker, capable of utilizing English in all scenarios.\nAble to handle meetings and specialized presentations.", "desired_salary_month": "Desired <PERSON> (Month)", "salary_month": "Salary (Month)", "desired_salary_amount": "Desired salary (Month)", "salary_month_2": "Month Salary", "engineer_info_highlight": "{country_name} nationality, {gender}, {age} years old, job change {job_change_count} times", "engineer_desired_job_type": "Desired Job Type", "engineer_desired_location": "Desired Work Location", "engineer_desired_salary_month": "Desired Monthly Salary", "desired_salary_month_value": "Month {value} {currency}", "work_experience": "Work Experience", "auto_translate_native_language_note": "*Documents written in the viewer's native language are automatically translated and displayed according to the viewer's location.", "verify": "Verify", "please_update_email": "Please update your email address and verify it.", "protect_your_account": "Protect your account", "protect_your_account_description": "To ensure you can always access your account, please verify your email address. This email will help you recover your account when you are unable to log in via SNS.", "enter_your_email": "Enter your email", "send_verification_code": "Send Verification Link", "skip_protect_account": "Later (Not Recommended)*", "protect_account_description": "We are committed to protecting your information and will only use your email for account verification purposes.", "email_restore_password": "Enter Recovery Email Address", "email_update_note": "*You can update your email on the basic information edit screen.", "newest_filter": "Newest", "popular_filter": "Most Popular", "age_filter": "By Age", "salary_filter": "By Salary", "developing": "Developing", "upload_cv": "Upload CV", "upload_cv_file": "Upload CV File", "professional_summary": "Professional Summary", "highlight_projects": "Highlight Projects", "experiences": "Experiences", "role_name": "Role Name", "team_size": "Team Size", "responsibilities": "Responsibilities", "technology_stack": "Technology Stack", "preview_cv": "Preview CV", "analyzing": "Analyzing...", "data_extracted_successfully": "Data has been extracted successfully. Please check.", "country_of_residence": "Place of residence (country)", "area_of_residence": "Place of residence (area)", "up_to_age": "To age", "languages_learned": "Languages learned", "project_size": "Project Size", "edit_highlight_project": "Edit highlight project", "professional_experience": "Professional Experience", "edit_professional_experience": "Edit professional experience", "edit_job_summary": "Edit professional summary", "responsibility": "Responsibility", "technology_used": "Technology used", "project_description": "Project Description", "skills_for_cv_display": "Skills for CV Display", "export_cv": "Export CV", "add_selected_language": "Add selected language", "beginner": "<PERSON><PERSON><PERSON>", "advanced": "Advanced", "skill_display_when_export_cv": "Skill content that will be displayed when exporting CV", "major_projects": "Highlight projects", "job_title": "Job title", "add_major_project": "Add highlight project", "project_name": "Project name", "project_overview": "Project overview", "project_scale": "Project size", "role": "Role", "job_responsibility": "Job responsibility", "specification_technology": "Specification technology", "specification_technology_hint_text": "Nodejs, MongoDB, VueJs, Firebase.", "job_responsibility_hint_text": "Develop UI using VueJS.\nSupport back-end team to write some APIs.\nFix bugs for both front-end and back-end side.", "project_overview_hint_text": "This web application like a social network where can create photo contests so everyone can upload their photos or give their vote. The result will be calculated and return after the contest end.", "search_hint_admin_search": "Enter user name, age, skill or school name...", "add_custom_skill": "Add custom skill", "enter_skill_name": "Enter skill name", "add_selected_skill": "Add selected skill", "browse_file": "Browse File", "supported_file_types": "Supported file types: PDF, max size: 5MB", "confirm_update_information": "Confirm update information", "the_cv_data_will_be_overwritten": "The CV data will overwrite the current information.", "recommended_reason": "Recommended reason", "see_ai_recommendation": "See AI recommendation", "ai_analysis": "AI Analysis", "ai_finding_the_best_matches": "AI is finding the best match for you...", "ai_is_analyzing": "AI is analyzing...", "please_wait_while_we_find_the_best_matches": "Please wait a moment while we determine the most suitable opportunities.", "ai_recommendations_for": "AI recommendations for: {value}", "change": "Change", "select_recruit_for_ai_analysis": "Select a recruit for AI analysis", "no_recruits_available": "No jobs available", "choose_recruit_for_ai_analysis": "Select a recruit for AI analysis", "key_continue": "Continue", "show_more": "Show more", "show_less": "Show less", "time_out_message_upload_cv": "The server is taking longer than expected to process your CV. This usually happens with large files or during high server load. Please try again in a few minutes.", "error_message_connection": "Please check your connection and try again", "error_message_timeout": "Request timed out. Please try again", "error_message_server": "Server error. Please try again later", "error_message_auth": "Authentication failed. Please login again", "error_message_forbidden": "Access denied. You don't have permission", "error_message_client": "Bad request. Please check your input", "error_message_cancelled": "Request was cancelled", "error_message_unknown": "Something went wrong. Please try again later", "file_size_error_message_x_mb": "File size must be less than {x}MB", "engineer_under_selection": "Engineers under selection"}