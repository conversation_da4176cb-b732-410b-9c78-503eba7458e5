import 'dart:async';

import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/my_recruits/index.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/edit_recruit_screen.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/common.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/primary_button.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/transparent_button.dart';
import 'package:asiantech_link/layers/presentation/widgets/cached_image.dart';
import 'package:asiantech_link/layers/presentation/widgets/empty_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/loading_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/pagination_footer.dart';
import 'package:asiantech_link/layers/presentation/widgets/scaffold/app_secondary_scaffold.dart';
import 'package:asiantech_link/routes/host_companies_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:openapi/openapi.dart';

import '../../../../widgets/error_app_widget.dart';
import '../../../../widgets/platform_text.dart';

class MyRecruitScreen extends StatefulWidget {
  static const String routeName = 'my-recruit';
  const MyRecruitScreen({super.key});

  @override
  State<MyRecruitScreen> createState() => _MyRecruitScreenState();
}

class _MyRecruitScreenState extends State<MyRecruitScreen> {
  final MyRecruitsBloc _myRecruitsBloc =
      MyRecruitsBloc(const UnMyRecruitsState());

  int _currentPage = 1;

  @override
  void initState() {
    _getData(false);
    super.initState();
  }

  _getData(bool isLoadMore) {
    final Completer<void> completer = Completer();

    _myRecruitsBloc.add(LoadMyRecruitsEvent(
        isLoadMore: isLoadMore,
        onDone: () {
          completer.complete();
        }));
    return completer.future;
  }

  /// Handle page change
  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    _getData(false);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MyRecruitsBloc, MyRecruitsState>(
        bloc: _myRecruitsBloc,
        builder: (context, state) {
          bool showOldPost = state.showOldPost ?? false;
          bool showNewest = state.showNewest ?? false;
          return AppSecondaryScaffold(
              showBackIcon: false,
              showBottomNavigationBar: false,
              userType: UserTypeConstants.hostCompanyStaff,
              actions: defaultActionBarCompany(context: context),
              child: Column(
                children: [
                  // Header with Create Button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                    child: Row(
                      children: [
                        Expanded(
                          child: PrimaryButton(
                            onPressed: () {
                              context.push(
                                  "${HostCompaniesRouter.routeName}/${MyRecruitScreen.routeName}/${EditRecruitScreen.routeName}");
                            },
                            buttonText: AppText.value(context).create_recruit,
                            icon: const Icon(
                              Icons.add,
                              color: AppColors.white,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Header with count and filters
                  if (state is InMyRecruitsState)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        children: [
                          // Total count and expired checkbox
                          Row(
                            children: [
                              RichText(
                                text: TextSpan(
                                  style: AppTextStyles.paragraph02v1(
                                      color: AppColors.iconColor,
                                      fontWeight: FontWeight.w500),
                                  children: [
                                    TextSpan(text: '${AppText.value(context).recruitment_requirements}: '),
                                    TextSpan(
                                        text: (state.listRecruitModel.totalCount ?? 0).toString(),
                                        style: AppTextStyles.heading04(
                                            color: AppColors.primary,
                                            fontWeight: FontWeight.w700)),
                                  ],
                                ),
                              ),
                              const Spacer(),
                              // Show expired checkbox
                              GestureDetector(
                                onTap: () {
                                  _myRecruitsBloc.add(ApplyFilter(
                                    showOldPost: !showOldPost,
                                  ));
                                },
                                child: Row(
                                  children: [
                                    Container(
                                      margin: const EdgeInsets.only(right: 4),
                                      width: 20,
                                      height: 20,
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: AppColors.hostCompanyPrimary,
                                      ),
                                      child: showOldPost
                                          ? const Icon(
                                              Icons.check,
                                              size: 16,
                                              color: AppColors.white,
                                            )
                                          : null,
                                    ),
                                    Text(
                                      AppText.value(context).show_expired_recruits,
                                      style: AppTextStyles.paragraph02v1(),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          // Sort button
                          Row(
                            children: [
                              const Spacer(),
                              TransparentButton(
                                borderRadius: 5,
                                color: AppColors.hostCompanyPrimary,
                                buttonText: null,
                                onPressed: () async {
                                  _myRecruitsBloc.add(ApplyFilter(
                                    sortNewest: !showNewest,
                                  ));
                                },
                                child: Row(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(right: 6),
                                      child: Text(
                                        "${AppText.value(context).sort_by} ${showNewest ? AppText.value(context).descending : AppText.value(context).ascending}",
                                        style: AppTextStyles.paragraph02v1(),
                                      ),
                                    ),
                                    Icon(
                                      showNewest
                                          ? Icons.keyboard_arrow_down
                                          : Icons.keyboard_arrow_up,
                                      color: AppColors.hostCompanyPrimary,
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: Builder(builder: (context) {
                      if (state is LoadingMyRecruitsState) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }
                      if (state is ErrorMyRecruitsState) {
                        return ErrorAppWidget(
                          onPressed: () {
                            _getData(false);
                          },
                        );
                      }
                      if (state is InMyRecruitsState) {
                        var list = state.listRecruitModel.results ?? [];

                        if (list.isEmpty) {
                          return const Center(child: EmptyWidget());
                        }
                        // Calculate pagination info
                        final totalItems = state.listRecruitModel.totalCount ?? 0;
                        const itemsPerPage = 20;
                        final totalPages = totalItems == 0 ? 1 : (totalItems / itemsPerPage).ceil();

                        return Column(
                          children: [
                            // List view
                            Expanded(
                              child: ListView.separated(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 20, horizontal: 20),
                            itemBuilder: (_, index) {
                              var model =
                                  list[index] as RecruitUploadedSerializers;
                              return GestureDetectorCustom(
                                onTap: () {
                                  var path =
                                      "${HostCompaniesRouter.routeName}/${MyRecruitScreen.routeName}/${EditRecruitScreen.routeName}?id=${model.recruitId}";
                                  context.push(path);
                                },
                                child: Stack(
                                  children: [
                                    Container(
                                        decoration: BoxDecoration(
                                          color: AppColors.white,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 18, vertical: 18),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  bottom: 5),
                                              child: Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Expanded(
                                                    child: PlatformText(
                                                      model.title ?? "",
                                                      style: AppTextStyles
                                                          .heading05(),
                                                    ),
                                                  ),
                                                  Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      SvgPicture.asset(
                                                        Assets
                                                            .svg.apply2IconSVG,
                                                        height: 14,
                                                      ),
                                                      PlatformText(
                                                        "${model.totalApply}",
                                                        style: AppTextStyles
                                                            .paragraph02v1(
                                                          color: AppColors
                                                              .cyanBlue,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Row(
                                              children: [
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 15),
                                                  child:
                                                      CacheImageNetworkWidget(
                                                    imageUrl:
                                                        "${model.recruitImagePath}",
                                                    width: 82,
                                                    height: 82,
                                                    borderRadius: 10,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                bottom: 10),
                                                        child: Row(
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right: 8),
                                                              child: SvgPicture
                                                                  .asset(Assets
                                                                      .svg
                                                                      .folderIconSVG),
                                                            ),
                                                            Expanded(
                                                              child: Text(
                                                                "${AppText.value(context).category}: ${model.jobCode != null ? Helper.getJobCodeName(context, model.jobCode ?? "") : ""}",
                                                                style: AppTextStyles
                                                                    .paragraph02v1(
                                                                  color: AppColors
                                                                      .cyanBlue,
                                                                ),
                                                              ),
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                bottom: 10),
                                                        child: Row(
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right: 8),
                                                              child: SvgPicture
                                                                  .asset(Assets
                                                                      .svg
                                                                      .salaryIconSVG),
                                                            ),
                                                            if (model.payrollPriceFrom !=
                                                                    null &&
                                                                model.payrollPriceTo !=
                                                                    null)
                                                              Expanded(
                                                                child: Text(
                                                                  "${AppText.value(context).salary_month_2}: ${Helper.formatMoney(model.payrollPriceFrom ?? "")} ${Helper.getCurrencyName(context: context, currencyCode: model.payrollCode ?? "")} - ${Helper.formatMoney(model.payrollPriceTo ?? "")} ${Helper.getCurrencyName(context: context, currencyCode: model.payrollCode ?? "")}",
                                                                  style: AppTextStyles
                                                                      .paragraph02v1(
                                                                    color: AppColors
                                                                        .cyanBlue,
                                                                  ),
                                                                ),
                                                              )
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                bottom: 10),
                                                        child: Row(
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right: 8),
                                                              child: SvgPicture
                                                                  .asset(Assets
                                                                      .svg
                                                                      .calendarIconSVG),
                                                            ),
                                                            Expanded(
                                                              child: Text(
                                                                "${AppText.value(context).period}: ${Common.formatDate(model.startDate?.toLocal())} -  ${Common.formatDate(model.endDate)}",
                                                                style: AppTextStyles
                                                                    .paragraph02v1(
                                                                  color: AppColors
                                                                      .cyanBlue,
                                                                ),
                                                              ),
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        )),
                                    if (model.displayFlag == 0 &&
                                        model.isExpired == false)
                                      Positioned(
                                        top: 0,
                                        right: 0,
                                        child: Container(
                                          decoration: const BoxDecoration(
                                            color: AppColors.grey,
                                            borderRadius: BorderRadius.only(
                                              bottomLeft: Radius.circular(10),
                                              topRight: Radius.circular(10),
                                            ),
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 3),
                                          child: Text(
                                            AppText.value(context).draft,
                                            style: AppTextStyles.bodySmall(
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    if (model.isExpired == true)
                                      Positioned(
                                        top: 0,
                                        right: 0,
                                        child: Container(
                                          decoration: const BoxDecoration(
                                            color: AppColors.red,
                                            borderRadius: BorderRadius.only(
                                              bottomLeft: Radius.circular(10),
                                              topRight: Radius.circular(10),
                                            ),
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 3),
                                          child: Text(
                                            AppText.value(context).expired,
                                            style: AppTextStyles.bodySmall(
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      )
                                  ],
                                ),
                              );
                            },
                                separatorBuilder: (_, index) {
                                  return const SizedBox(
                                    height: 20,
                                  );
                                },
                                itemCount: list.length,
                              ),
                            ),
                            // Pagination footer
                            if (totalPages > 1)
                              PaginationFooter(
                                currentPage: _currentPage,
                                totalPages: totalPages,
                                onPageChanged: (int page) {
                                  _onPageChanged(page);
                                },
                              ),
                          ],
                        );
                      }
                      return Container();
                    }),
                  ),
                ],
              ));
        });
  }
}
