import 'dart:async';

import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/my_recruits/index.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/edit_recruit_screen.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/common.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/primary_button.dart';

import 'package:asiantech_link/layers/presentation/widgets/cached_image.dart';
import 'package:asiantech_link/layers/presentation/widgets/empty_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';

import 'package:asiantech_link/layers/presentation/widgets/pagination_footer.dart';
import 'package:asiantech_link/layers/presentation/widgets/scaffold/app_secondary_scaffold.dart';
import 'package:asiantech_link/routes/host_companies_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:openapi/openapi.dart';

import '../../../../widgets/error_app_widget.dart';
import '../../../../widgets/platform_text.dart';

class MyRecruitScreen extends StatefulWidget {
  static const String routeName = 'my-recruit';
  const MyRecruitScreen({super.key});

  @override
  State<MyRecruitScreen> createState() => _MyRecruitScreenState();
}

class _MyRecruitScreenState extends State<MyRecruitScreen> {
  final MyRecruitsBloc _myRecruitsBloc =
      MyRecruitsBloc(const UnMyRecruitsState());

  int _currentPage = 1;

  @override
  void initState() {
    _getData(false);
    super.initState();
  }

  _getData(bool isLoadMore) {
    final Completer<void> completer = Completer();

    _myRecruitsBloc.add(LoadMyRecruitsEvent(
        isLoadMore: isLoadMore,
        onDone: () {
          completer.complete();
        }));
    return completer.future;
  }

  /// Handle page change
  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    _getData(false);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MyRecruitsBloc, MyRecruitsState>(
        bloc: _myRecruitsBloc,
        builder: (context, state) {
          bool showOldPost = state.showOldPost ?? false;
          return AppSecondaryScaffold(
              showBackIcon: false,
              showBottomNavigationBar: false,
              userType: UserTypeConstants.hostCompanyStaff,
              actions: defaultActionBarCompany(context: context),
              child: Column(
                children: [
                  // Header with Create Button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                    child: Row(
                      children: [
                        Expanded(
                          child: PrimaryButton(
                            onPressed: () {
                              context.push(
                                  "${HostCompaniesRouter.routeName}/${MyRecruitScreen.routeName}/${EditRecruitScreen.routeName}");
                            },
                            buttonText: AppText.value(context).create_recruit,
                            icon: const Icon(
                              Icons.add,
                              color: AppColors.white,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Header section
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          AppText.value(context).recruitment_requirements,
                          style: AppTextStyles.heading03v1(
                            fontWeight: FontWeight.w700,
                            color: AppColors.black,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Filter row
                        if (state is InMyRecruitsState)
                          Row(
                            children: [
                              // Count display with range
                              Builder(builder: (context) {
                                final totalItems = state.listRecruitModel.totalCount ?? 0;
                                const itemsPerPage = 20;
                                final startIndex = (_currentPage - 1) * itemsPerPage + 1;
                                final endIndex = (_currentPage * itemsPerPage > totalItems)
                                    ? totalItems
                                    : _currentPage * itemsPerPage;

                                return Text(
                                  totalItems > 0
                                      ? '$totalItems 件中 $startIndex～$endIndex件を表示'
                                      : '0 件中 0～0件を表示',
                                  style: AppTextStyles.paragraph02v1(
                                    color: AppColors.iconColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                );
                              }),
                              const SizedBox(width: 16),
                              // Checkbox for past recruitments
                              GestureDetector(
                                onTap: () {
                                  _myRecruitsBloc.add(ApplyFilter(
                                    showOldPost: !showOldPost,
                                  ));
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: showOldPost
                                              ? AppColors.hostCompanyPrimary
                                              : AppColors.grey,
                                          width: 1,
                                        ),
                                        color: showOldPost
                                            ? AppColors.hostCompanyPrimary
                                            : Colors.transparent,
                                      ),
                                      child: showOldPost
                                          ? const Icon(
                                              Icons.check,
                                              size: 12,
                                              color: AppColors.white,
                                            )
                                          : null,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      AppText.value(context).show_expired_recruits,
                                      style: AppTextStyles.paragraph02v1(
                                        color: AppColors.iconColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Spacer(),
                              // Job Type dropdown
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                  border: Border.all(color: AppColors.grey),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      '職種: すべて',
                                      style: AppTextStyles.paragraph02v1(
                                        color: AppColors.iconColor,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    const Icon(
                                      Icons.keyboard_arrow_down,
                                      size: 16,
                                      color: AppColors.iconColor,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 8),
                              // Status dropdown
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                  border: Border.all(color: AppColors.grey),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      'ステータス: すべて',
                                      style: AppTextStyles.paragraph02v1(
                                        color: AppColors.iconColor,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    const Icon(
                                      Icons.keyboard_arrow_down,
                                      size: 16,
                                      color: AppColors.iconColor,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: Builder(builder: (context) {
                      if (state is LoadingMyRecruitsState) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }
                      if (state is ErrorMyRecruitsState) {
                        return ErrorAppWidget(
                          onPressed: () {
                            _getData(false);
                          },
                        );
                      }
                      if (state is InMyRecruitsState) {
                        var list = state.listRecruitModel.results ?? [];

                        if (list.isEmpty) {
                          return const Center(child: EmptyWidget());
                        }
                        // Calculate pagination info
                        final totalItems = state.listRecruitModel.totalCount ?? 0;
                        const itemsPerPage = 20;
                        final totalPages = totalItems == 0 ? 1 : (totalItems / itemsPerPage).ceil();

                        return Column(
                          children: [
                            // List view
                            Expanded(
                              child: ListView.separated(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 20, horizontal: 20),
                            itemBuilder: (_, index) {
                              var model =
                                  list[index] as RecruitUploadedSerializers;
                              return GestureDetectorCustom(
                                onTap: () {
                                  var path =
                                      "${HostCompaniesRouter.routeName}/${MyRecruitScreen.routeName}/${EditRecruitScreen.routeName}?id=${model.recruitId}";
                                  context.push(path);
                                },
                                child: Stack(
                                  children: [
                                    Container(
                                        decoration: BoxDecoration(
                                          color: AppColors.white,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 18, vertical: 18),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  bottom: 5),
                                              child: Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Expanded(
                                                    child: PlatformText(
                                                      model.title ?? "",
                                                      style: AppTextStyles
                                                          .heading05(),
                                                    ),
                                                  ),
                                                  Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      SvgPicture.asset(
                                                        Assets
                                                            .svg.apply2IconSVG,
                                                        height: 14,
                                                      ),
                                                      PlatformText(
                                                        "${model.totalApply}",
                                                        style: AppTextStyles
                                                            .paragraph02v1(
                                                          color: AppColors
                                                              .cyanBlue,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Row(
                                              children: [
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 15),
                                                  child:
                                                      CacheImageNetworkWidget(
                                                    imageUrl:
                                                        "${model.recruitImagePath}",
                                                    width: 82,
                                                    height: 82,
                                                    borderRadius: 10,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                bottom: 10),
                                                        child: Row(
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right: 8),
                                                              child: SvgPicture
                                                                  .asset(Assets
                                                                      .svg
                                                                      .folderIconSVG),
                                                            ),
                                                            Expanded(
                                                              child: Text(
                                                                "${AppText.value(context).category}: ${model.jobCode != null ? Helper.getJobCodeName(context, model.jobCode ?? "") : ""}",
                                                                style: AppTextStyles
                                                                    .paragraph02v1(
                                                                  color: AppColors
                                                                      .cyanBlue,
                                                                ),
                                                              ),
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                bottom: 10),
                                                        child: Row(
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right: 8),
                                                              child: SvgPicture
                                                                  .asset(Assets
                                                                      .svg
                                                                      .salaryIconSVG),
                                                            ),
                                                            if (model.payrollPriceFrom !=
                                                                    null &&
                                                                model.payrollPriceTo !=
                                                                    null)
                                                              Expanded(
                                                                child: Text(
                                                                  "${AppText.value(context).salary_month_2}: ${Helper.formatMoney(model.payrollPriceFrom ?? "")} ${Helper.getCurrencyName(context: context, currencyCode: model.payrollCode ?? "")} - ${Helper.formatMoney(model.payrollPriceTo ?? "")} ${Helper.getCurrencyName(context: context, currencyCode: model.payrollCode ?? "")}",
                                                                  style: AppTextStyles
                                                                      .paragraph02v1(
                                                                    color: AppColors
                                                                        .cyanBlue,
                                                                  ),
                                                                ),
                                                              )
                                                          ],
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                bottom: 10),
                                                        child: Row(
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right: 8),
                                                              child: SvgPicture
                                                                  .asset(Assets
                                                                      .svg
                                                                      .calendarIconSVG),
                                                            ),
                                                            Expanded(
                                                              child: Text(
                                                                "${AppText.value(context).period}: ${Common.formatDate(model.startDate?.toLocal())} -  ${Common.formatDate(model.endDate)}",
                                                                style: AppTextStyles
                                                                    .paragraph02v1(
                                                                  color: AppColors
                                                                      .cyanBlue,
                                                                ),
                                                              ),
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        )),
                                    if (model.displayFlag == 0 &&
                                        model.isExpired == false)
                                      Positioned(
                                        top: 0,
                                        right: 0,
                                        child: Container(
                                          decoration: const BoxDecoration(
                                            color: AppColors.grey,
                                            borderRadius: BorderRadius.only(
                                              bottomLeft: Radius.circular(10),
                                              topRight: Radius.circular(10),
                                            ),
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 3),
                                          child: Text(
                                            AppText.value(context).draft,
                                            style: AppTextStyles.bodySmall(
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    if (model.isExpired == true)
                                      Positioned(
                                        top: 0,
                                        right: 0,
                                        child: Container(
                                          decoration: const BoxDecoration(
                                            color: AppColors.red,
                                            borderRadius: BorderRadius.only(
                                              bottomLeft: Radius.circular(10),
                                              topRight: Radius.circular(10),
                                            ),
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 3),
                                          child: Text(
                                            AppText.value(context).expired,
                                            style: AppTextStyles.bodySmall(
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      )
                                  ],
                                ),
                              );
                            },
                                separatorBuilder: (_, index) {
                                  return const SizedBox(
                                    height: 20,
                                  );
                                },
                                itemCount: list.length,
                              ),
                            ),
                            // Pagination footer
                            if (totalPages > 1)
                              PaginationFooter(
                                currentPage: _currentPage,
                                totalPages: totalPages,
                                onPageChanged: (int page) {
                                  _onPageChanged(page);
                                },
                              ),
                          ],
                        );
                      }
                      return Container();
                    }),
                  ),
                ],
              ));
        });
  }
}
