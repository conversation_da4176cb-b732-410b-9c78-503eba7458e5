import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/create_recruit/index.dart';

import 'package:asiantech_link/layers/presentation/resources/config_export.dart';

import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/app_validator.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:asiantech_link/layers/presentation/widgets/cached_image.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/svg_icon.dart';
import 'package:asiantech_link/layers/presentation/widgets/title_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class RecruitContent extends StatelessWidget {
  const RecruitContent({
    super.key,
    required EditRecruitBloc editRecruitBloc,
  }) : _editRecruitBloc = editRecruitBloc;

  final EditRecruitBloc _editRecruitBloc;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: GestureDetectorCustom(
            onTap: () async {
              final result = await Helper.pickImage(context: context);
              if (result != null) {
                final bytes = await result.readAsBytes();
                _editRecruitBloc.coverImageBytes = bytes;
                _editRecruitBloc.add(ReloadEditRecruit());
              }
            },
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.hostCompanyPrimary),
                borderRadius: BorderRadius.circular(6),
                color: AppColors.white,
              ),
              width: 150,
              height: 150,
              child: Center(
                child: Builder(builder: (context) {
                  var bytes = _editRecruitBloc.coverImageBytes;
                  if (bytes != null) {
                    return Image.memory(
                      bytes,
                      height: 140,
                      width: 140,
                      fit: BoxFit.cover,
                    );
                  }
                  if (_editRecruitBloc.recruitCoverImage != null) {
                    return CacheImageNetworkWidget(
                      imageUrl: "${_editRecruitBloc.recruitCoverImage}",
                      height: 140,
                      width: 140,
                      boxFit: BoxFit.cover,
                    );
                  }
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: SvgIcon(
                          path: Assets.svg.uploadImageIconSVG,
                          width: 46,
                          color: AppColors.hostCompanyPrimary,
                        ),
                      ),
                      Text(
                        AppText.value(context).upload_image_max_5mb,
                        textAlign: TextAlign.center,
                        style: AppTextStyles.paragraph03(
                          color: AppColors.cyanBlue,
                        ),
                      )
                    ],
                  );
                }),
              ),
            ),
          ),
        ),
        
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16).copyWith(bottom: 0),
          child: TitleTextField(
            minLines: 5,
            maxLines: 5,
            textStyle: AppTextStyles.paragraph02v1(),
            titleTextStyle: AppTextStyles.paragraph02v3(),
            hintStyle: AppTextStyles.paragraph02v1(
              color: AppColors.hostCompanySecondary,
            ),
            inputFormatters: [
              LengthLimitingTextInputFormatter(10000),
            ],
            hintText: AppText.value(context).enter_job_description,
            title: AppText.value(context).job_description_max_10000,
            isRequired: true,
            errorText: _editRecruitBloc.isSubmitted
                ? AppValidator.checkEmpty(
                    _editRecruitBloc.contentController.text, context)
                : null,
            controller: _editRecruitBloc.contentController,
            onChanged: (value) {
              _editRecruitBloc.add(ReloadEditRecruit());
            },
            fillColor: AppColors.backgroundColor,
          ),
        ),
      ],
    );
  }
}
