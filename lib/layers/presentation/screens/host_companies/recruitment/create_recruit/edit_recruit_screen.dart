import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/data/dto/company_model_dto.dart';
import 'package:asiantech_link/layers/domain/entity/academic_level_model.dart';
import 'package:asiantech_link/layers/domain/entity/country_model.dart';
import 'package:asiantech_link/layers/domain/entity/currency_model.dart';
import 'package:asiantech_link/layers/domain/entity/employ_code_model.dart';
import 'package:asiantech_link/layers/domain/entity/job_code_model.dart';
import 'package:asiantech_link/layers/domain/entity/language_level_model.dart';
import 'package:asiantech_link/layers/domain/entity/language_model.dart';
import 'package:asiantech_link/layers/domain/entity/state_model.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/my_company/index.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/create_recruit/index.dart';

import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_academic.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_age.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_content.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_job_code.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_licence.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_list_address.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_list_place_code.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_list_skill.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_personal_conditions.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_remote_code.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_salary.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/recruit_support_company_item.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/show_support_agency_dialog.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/preview_recruit/preview_recruit.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/my_recruit/my_recruit_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/recruitment/create_recruit/local_widget/copy_existing_recruit_dialog.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/common_dialog.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/transparent_button.dart';
import 'package:asiantech_link/layers/presentation/widgets/button_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/scaffold/app_secondary_scaffold.dart';
import 'package:asiantech_link/layers/presentation/widgets/svg_icon.dart';
import 'package:asiantech_link/layers/presentation/widgets/title_required_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/title_text_field.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:openapi/openapi.dart';

import '../../../../../../routes/host_companies_router.dart';
import '../../../../../domain/entity/skill_code_model.dart';
import '../../../../blocs/general/app/index.dart';
import '../../../../resources/config_export.dart';
import '../../../../utils/app_validator.dart';
import '../../../general/authentication/edit_account_screen/local_widget/edit_text_field.dart';
import 'local_widget/recruit_date_time.dart';
import 'local_widget/recruit_employee_type.dart';
import 'local_widget/recruit_career_and_experience.dart';
import 'local_widget/recruit_list_language.dart';
import 'local_widget/recruit_nationality.dart';
import 'local_widget/recruit_sex.dart';

enum PersonalConditionType {
  nationality,
  sex,
  age,
  address,
  lastAcademicBackground,
  language,
  career,
  skill,
  license,
}

class EditRecruitScreen extends StatefulWidget {
  static const String routeName = 'edit-recruit';
  const EditRecruitScreen({super.key});

  @override
  State<EditRecruitScreen> createState() => _CreateRecruitScreenState();
}

class _CreateRecruitScreenState extends State<EditRecruitScreen> {
  final EditRecruitBloc _editRecruitBloc = EditRecruitBloc();

  bool _isUpdate = false;
  String? _recruitId;

  List<JobCode> listJobCode = [];
  List<EmployCode> listEmployCode = [];
  List<Country> listCountry = [];
  List<CountryState> listCountryState = [];
  List<Currency> listCurrency = [];
  List<AcademicLevel> listAcademic = [];
  List<LanguageCode> listLanguage = [];
  List<LanguageLevel> listLanguageLevel = [];
  List<SkillCode> listSkillCode = [];

  final List<PersonalConditionType> _listPersonalConditionSelected = [];
  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    await Future.delayed(const Duration(milliseconds: 200));

    if (mounted) {
      // get params url
      var params = GoRouterState.of(context).uri.queryParameters;
      _recruitId = params['id'];
      if (_recruitId != null) {
        _isUpdate = true;
        _editRecruitBloc.isSubmitted = true;
        _editRecruitBloc.add(GetMyRecruitDetails(
            recruitId: _recruitId ?? "",
            onSuccess: (model) {
              _copyRecruit(model);
            },
            onError: (error) async {
              await CommonDialog.showErrorDialog(
                  context: context, message: error.message, onConfirm: () {});
              if (mounted) {
                context.go(
                    "${HostCompaniesRouter.routeName}/${MyRecruitScreen.routeName}");
              }
            }));
        setState(() {});
      }
    }
  }

  _copyRecruit(CompanyRecruitDetail recruitModel) {
    _listPersonalConditionSelected.clear();
    if (recruitModel.countryCode != null) {
      _listPersonalConditionSelected.add(PersonalConditionType.nationality);
    }
    if (recruitModel.sexType != null) {
      _listPersonalConditionSelected.add(PersonalConditionType.sex);
    }
    if (recruitModel.ageFrom != null || recruitModel.ageTo != null) {
      _listPersonalConditionSelected.add(PersonalConditionType.age);
    }
    if (recruitModel.prefCode1 != null ||
        recruitModel.prefCode2 != null ||
        recruitModel.prefCode3 != null) {
      _listPersonalConditionSelected.add(PersonalConditionType.address);
    }
    if (recruitModel.lastAcademicCode != null) {
      _listPersonalConditionSelected
          .add(PersonalConditionType.lastAcademicBackground);
    }
    if (recruitModel.experiencedJobCode != null) {
      _listPersonalConditionSelected.add(PersonalConditionType.career);
    }
    if (recruitModel.skillCode1 != null ||
        recruitModel.skillCode2 != null ||
        recruitModel.skillCode3 != null) {
      _listPersonalConditionSelected.add(PersonalConditionType.skill);
    }
    if (recruitModel.licenceCode1 != null ||
        recruitModel.licenceCode2 != null ||
        recruitModel.licenceCode3 != null) {
      _listPersonalConditionSelected.add(PersonalConditionType.license);
    }
    if (recruitModel.languageCode1 != null ||
        recruitModel.languageCode2 != null) {
      _listPersonalConditionSelected.add(PersonalConditionType.language);
    }
    _editRecruitBloc.recruitCoverImage = recruitModel.recruitImagePath;
    _editRecruitBloc.add(CopyRecruit(
      recruitModel: recruitModel,
    ));
  }

  void saveRecruit(BuildContext context, {bool isTemporary = false}) {
    _editRecruitBloc.add(EditRecruit(
      context: context,
      personalConditionTypes: _listPersonalConditionSelected,
      isUpdate: _isUpdate,
      id: _recruitId,
      isSaveTemporarily: isTemporary,
      onSuccess: () {
        CommonDialog.showInfoDialog(
            message: AppText.value(context).recruitment_registered,
            onConfirm: () {
              context.pushReplacement(
                  "${HostCompaniesRouter.routeName}/${MyRecruitScreen.routeName}");
            });
      },
      onError: (error) {
        CommonDialog.showErrorDialog(error: error, context: context);
      },
    ));
    if (isTemporary == false) {
      setState(() {});
    }
  }

  void getJsonValue(BuildContext context) {
    listJobCode =
        BlocProvider.of<AppBloc>(context).jobCodeModel?.jobCode?.toList() ?? [];
    listEmployCode =
        BlocProvider.of<AppBloc>(context).employCode?.employCode.toList() ?? [];
    listCountry =
        BlocProvider.of<AppBloc>(context).countryModel?.country.toList() ?? [];
    listCountryState =
        BlocProvider.of<AppBloc>(context).stateModel?.state.toList() ?? [];
    listCurrency =
        BlocProvider.of<AppBloc>(context).currencyModel?.currency?.toList() ??
            [];
    listAcademic = BlocProvider.of<AppBloc>(context)
            .academicLevel
            ?.academicLevel
            .toList() ??
        [];
    listLanguage = BlocProvider.of<AppBloc>(context)
            .languageModel
            ?.languageCode
            .toList() ??
        [];
    listLanguageLevel = BlocProvider.of<AppBloc>(context)
            .languageLevelModel
            ?.languageLevel
            .toList() ??
        [];
    listSkillCode =
        BlocProvider.of<AppBloc>(context).skillCode?.skillCode.toList() ?? [];
  }

  _deleteRecruit() {
    CommonDialog.showInfoDialog(
        title: AppText.value(context).confirmation_notice,
        message: AppText.value(context).are_you_sure_delete_recruit,
        onConfirm: () {
          _editRecruitBloc.add(DeleteRecruit(
              recruitId: _recruitId!,
              onSuccess: () {
                CommonDialog.showInfoDialog(
                    message: AppText.value(context).recruitment_deleted,
                    onConfirm: () {
                      context.pushReplacement(
                          "${HostCompaniesRouter.routeName}/${MyRecruitScreen.routeName}");
                    });
              }));
        },
        labelConfirmTextStyle: AppColors.hostCompanyPrimary,
        labelConfirm: AppText.value(context).delete,
        labelCancel: AppText.value(context).cancel,
        onCancel: () {
          context.pop();
        });
  }

  _preview() {
    CompanyRecruitDetail recruitDetail = CompanyRecruitDetail((b) {
      b.ageFrom = int.tryParse(_editRecruitBloc.minAgeController.text);
      b.ageTo = int.tryParse(_editRecruitBloc.maxAgeController.text);
      b.catchCopy = _editRecruitBloc.catchCopyController.text;
      b.content = _editRecruitBloc.contentController.text;
      b.countryCode = _editRecruitBloc.selectNationality;
      b.employCode = _editRecruitBloc.selectEmployCode;
      b.jobCode = _editRecruitBloc.selectJobCode;
      b.languageCode1 = _editRecruitBloc.languageCode1;
      b.languageCode2 = _editRecruitBloc.languageCode2;
      b.languageLevelType1 = _editRecruitBloc.languageLevel1;
      b.languageLevelType2 = _editRecruitBloc.languageLevel2;
      b.lastAcademicCode = _editRecruitBloc.selectAcademicLevel?.toString();
      b.licenceCode1 = _editRecruitBloc.licenceCode1;
      b.licenceCode2 = _editRecruitBloc.licenceCode2;
      b.licenceCode3 = _editRecruitBloc.licenceCode3;
      b.licenceName1 = _editRecruitBloc.licenceName1;
      b.licenceName2 = _editRecruitBloc.licenceName2;
      b.licenceName3 = _editRecruitBloc.licenceName3;
      b.licencePoint1 =
          int.tryParse(_editRecruitBloc.licencePoint1Controller.text);
      b.licencePoint2 =
          int.tryParse(_editRecruitBloc.licencePoint2Controller.text);
      b.licencePoint3 =
          int.tryParse(_editRecruitBloc.licencePoint3Controller.text);
      b.payrollCode = _editRecruitBloc.selectCurrency;
      b.payrollPriceFrom = _editRecruitBloc.minSalaryController.text;
      b.payrollPriceTo = _editRecruitBloc.maxAgeController.text;
      b.placeCode1 = _editRecruitBloc.placeCode1;
      b.placeCode2 = _editRecruitBloc.placeCode2;
      b.placeCode3 = _editRecruitBloc.placeCode3;

      b.prefCode1 = _editRecruitBloc.prefCode1;
      b.prefCode2 = _editRecruitBloc.prefCode2;
      b.prefCode3 = _editRecruitBloc.prefCode3;
      b.recruitImagePath = _editRecruitBloc.recruitCoverImage;
      b.sexType = _editRecruitBloc.selectSexType?.index;
      b.skillCode1 = _editRecruitBloc.skillCode1;
      b.skillCode2 = _editRecruitBloc.skillCode2;
      b.skillCode3 = _editRecruitBloc.skillCode3;
      b.skillLevelType1 = _editRecruitBloc.skillLevel1;
      b.skillLevelType2 = _editRecruitBloc.skillLevel2;
      b.skillLevelType3 = _editRecruitBloc.skillLevel3;
      b.startDate = _editRecruitBloc.startTime;
      b.endDate = _editRecruitBloc.endTime;
      if (_editRecruitBloc.supportCompany != null) {
        b.supportCompany.name = _editRecruitBloc.supportCompany?.name;
        b.supportCompany.logoImagePath =
            _editRecruitBloc.supportCompany?.logoImagePath;
        b.supportCompany.acceptingFee =
            _editRecruitBloc.supportCompany?.acceptingFee;
        b.supportCompany.acceptingFeeCurrCode =
            _editRecruitBloc.supportCompany?.acceptingFeeCurrCode;
        b.supportCompany.introductionPr =
            _editRecruitBloc.supportCompany?.introductionPr;
        b.supportCompany.supportOutsourcingFee =
            _editRecruitBloc.supportCompany?.supportOutsourcingFee;
        b.supportCompany.supportOutsourcingFeeCurrCode =
            _editRecruitBloc.supportCompany?.supportOutsourcingFeeCurrCode;
      }
      b.recruitImagePath = _editRecruitBloc.recruitCoverImage;

      b.yearsOfExperience = _editRecruitBloc.yearsOfExperience;
    });
    showDialog(
        context: context,
        builder: (_) {
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
            child: PreviewRecruit(
              recruitDetail: recruitDetail,
              coverImageBytes: _editRecruitBloc.coverImageBytes,
            ),
          );
        });
  }

  _onPersonalConditionChanged(PersonalConditionType type, value) {
    if (value) {
      _listPersonalConditionSelected.add(type);
    } else {
      _listPersonalConditionSelected.remove(type);
      switch (type) {
        case PersonalConditionType.nationality:
          _editRecruitBloc.selectNationality = null;
        case PersonalConditionType.sex:
          _editRecruitBloc.selectSexType = null;
        case PersonalConditionType.age:
          _editRecruitBloc.minAgeController.clear();
          _editRecruitBloc.maxAgeController.clear();
        case PersonalConditionType.address:
          _editRecruitBloc.prefCode1 = null;
          _editRecruitBloc.prefCode2 = null;
          _editRecruitBloc.prefCode3 = null;

        case PersonalConditionType.lastAcademicBackground:
          _editRecruitBloc.selectAcademicLevel = null;
        case PersonalConditionType.language:
          _editRecruitBloc.languageCode1 = null;
          _editRecruitBloc.languageCode2 = null;
          _editRecruitBloc.languageLevel1 = null;
          _editRecruitBloc.languageLevel2 = null;
        case PersonalConditionType.career:
          _editRecruitBloc.experiencedJobCode = null;
          _editRecruitBloc.personalEmployExperience = null;
        case PersonalConditionType.skill:
          _editRecruitBloc.skillCode1 = null;
          _editRecruitBloc.skillCode2 = null;
          _editRecruitBloc.skillCode3 = null;
          _editRecruitBloc.skillLevel1 = null;
          _editRecruitBloc.skillLevel2 = null;
          _editRecruitBloc.skillLevel3 = null;
        case PersonalConditionType.license:
          _editRecruitBloc.licenceCode1 = null;
          _editRecruitBloc.licenceCode2 = null;
          _editRecruitBloc.licenceCode3 = null;
          _editRecruitBloc.licenceName1 = null;
          _editRecruitBloc.licenceName2 = null;
          _editRecruitBloc.licenceName3 = null;
          _editRecruitBloc.licencePoint1Controller.clear();
          _editRecruitBloc.licencePoint2Controller.clear();
          _editRecruitBloc.licencePoint3Controller.clear();
      }
    }
    setState(() {});
  }

  _changeSupportCompany() async {
    final model = await showDialog(
        context: context,
        builder: (_) {
          return const ShowSupportAgencyDialog();
        });
    if (model != null) {
      var companyModel = model as CompanyModelDto;
      var recruitCompany = RecruitCompany((b) {
        b.acceptingFee = companyModel.acceptingFee;
        b.companyId = companyModel.companyId;
        b.acceptingFeeCurrCode = companyModel.acceptingFeeCurrCode;
        b.supportOutsourcingFee = companyModel.supportOutsourcingFee;
        b.supportOutsourcingFeeCurrCode =
            companyModel.supportOutsourcingFeeCurrCode;
        b.name = companyModel.name;
        b.logoImagePath = companyModel.logoImagePath;
        b.introductionPr = companyModel.introductionPR;
      });
      _editRecruitBloc.supportCompany = recruitCompany;
      _editRecruitBloc.add(ReloadEditRecruit());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MyCompanyBloc, MyCompanyState>(
        builder: (context, companyState) {
      return AppSecondaryScaffold(
        showBottomNavigationBar: true,
        bottomNavigationBar: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                spacing: 20,
                children: [
                  Row(
                    spacing: 20,
                    children: [
                      Expanded(
                        child: TransparentButton(
                          color: AppColors.hostCompanyPrimary,
                          buttonText: "",
                          onPressed: () {
                            if (_recruitId != null) {
                              _deleteRecruit();
                            } else {
                              _preview();
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8.5),
                            alignment: Alignment.center,
                            child: Text(
                              _recruitId == null
                                  ? AppText.value(context).preview
                                  : AppText.value(context).delete,
                              style: AppTextStyles.caption01(
                                color: AppColors.hostCompanyPrimary,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: TransparentButton(
                          color: AppColors.hostCompanyPrimary,
                          buttonText: "",
                          onPressed: () {
                            saveRecruit(context, isTemporary: true);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8.5),
                            alignment: Alignment.center,
                            child: Text(
                              AppText.value(context).save_temporarily,
                              style: AppTextStyles.caption01(
                                color: AppColors.hostCompanyPrimary,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  ButtonCustom(
                    backgroundColor: AppColors.hostCompanyPrimary,
                    title: AppText.value(context).save,
                    onPressed: () {
                      saveRecruit(context);
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: defaultActionBarCompany(context: context),
        userType: UserTypeConstants.hostCompanyStaff,
        child: BlocProvider(
          create: (_) => _editRecruitBloc,
          child: BlocBuilder<AppBloc, AppState>(
            builder: (context, stateApp) {
              if (stateApp is InAppState) {
                getJsonValue(context);
                return BlocBuilder<EditRecruitBloc, EditRecruitState>(
                    builder: (context, state) {
                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (!_isUpdate)
                          GestureDetectorCustom(
                            onTap: () async {
                              final result = await showDialog(
                                  context: context,
                                  builder: (context) {
                                    return const CopyExistingRecruitDialog();
                                  });

                              if (result is RecruitUploadedSerializers) {
                                RecruitUploadedSerializers recruit = result;
                                var id = recruit.recruitId;
                                if (id != null) {
                                  _editRecruitBloc.add(GetCopyRecruitDetails(
                                      recruitId: id.toString(),
                                      onSuccess: (model) {
                                        _copyRecruit(model);
                                      }));
                                }
                              }
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 7, horizontal: 16),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: AppColors.hostCompanyPrimary,
                                  ),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(right: 8),
                                      child: Text(
                                        AppText.value(context).copy_existing,
                                        style: AppTextStyles.paragraph02v1(),
                                      ),
                                    ),
                                    SvgIcon(
                                      path: Assets.svg.copyIconSVG,
                                      color: AppColors.hostCompanyPrimary,
                                      width: 20,
                                      height: 20,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        _buildCommonCard(
                            title: AppText.value(context).recruitment_overview,
                            child: Column(
                              children: [
                                _buildCommonTitle(
                                  isRequired: true,
                                  title: AppText.value(context).recruit_title,
                                  child: TitleTextField(
                                    minLines: 1,
                                    maxLines: 1,
                                    titleTextStyle:
                                        AppTextStyles.paragraph02v3(),
                                    hintStyle: AppTextStyles.paragraph02v1(
                                      color: AppColors.hostCompanySecondary,
                                    ),
                                    inputFormatters: [
                                      LengthLimitingTextInputFormatter(100),
                                    ],
                                    textStyle: AppTextStyles.paragraph02v1(),
                                    hintText: AppText.value(context)
                                        .enter_catchphrase,
                                    errorText: _editRecruitBloc.isSubmitted
                                        ? AppValidator.checkEmpty(
                                            _editRecruitBloc
                                                .catchCopyController.text,
                                            context)
                                        : null,
                                    controller:
                                        _editRecruitBloc.catchCopyController,
                                    onChanged: (value) {
                                      _editRecruitBloc
                                          .add(ReloadEditRecruit());
                                    },
                                    fillColor: AppColors.backgroundColor,
                                  ),
                                ),
                              ],
                            )),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 20),
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              RecruitContent(editRecruitBloc: _editRecruitBloc),
                              const SizedBox(height: 16),
                              RecruitJobCode(
                                editRecruitBloc: _editRecruitBloc,
                                jobCodes: listJobCode,
                              ),
                              const SizedBox(height: 16),
                              RecruitEmployeeType(
                                editRecruitBloc: _editRecruitBloc,
                                list: listEmployCode,
                              ),
                              const SizedBox(height: 16),
                              RecruitRemoteCode(
                                remoteCode: _editRecruitBloc.selectRemoteCode,
                                onChanged: (value) {
                                  _editRecruitBloc.selectRemoteCode = value;
                                  _editRecruitBloc.add(ReloadEditRecruit());
                                },
                              ),
                              const SizedBox(height: 16),
                              RecruitListPlaceCode(
                                editRecruitBloc: _editRecruitBloc,
                                listState: listCountryState,
                                listCountry: listCountry,
                              ),
                              const SizedBox(height: 16),
                              RecruitSalary(
                                  editRecruitBloc: _editRecruitBloc,
                                  list: listCurrency),
                              const SizedBox(height: 16),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TitleRequiredWidget(
                                    title: AppText.value(context)
                                        .title_of_recruitment_guideline,
                                  ),
                                  const SizedBox(height: 7),
                                  EditTextField(
                                    inputFormatters: [
                                      LengthLimitingTextInputFormatter(100),
                                    ],
                                    textStyle: AppTextStyles.paragraph02v1(),
                                    hintStyle: AppTextStyles.paragraph02v1(
                                      color: AppColors.hostCompanySecondary,
                                    ),
                                    fillColor: AppColors.backgroundColor,
                                    controller:
                                        _editRecruitBloc.titleController,
                                    validator: (value) {
                                      return _editRecruitBloc.isSubmitted
                                          ? AppValidator.checkEmpty(
                                              value, context)
                                          : null;
                                    },
                                    onChanged: (value) {
                                      _editRecruitBloc.add(ReloadEditRecruit());
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              RecruitDateTime(
                                  editRecruitBloc: _editRecruitBloc),
                              const SizedBox(height: 16),
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: AppColors.primary,
                                  ),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                padding: const EdgeInsets.all(10),
                                child: Column(
                                  children: [
                                    if (companyState is InMyCompanyState)
                                      RecruitPersonConditions(
                                        companyModel: companyState.companyModel,
                                        listPersonalConditionSelected:
                                            _listPersonalConditionSelected,
                                        onChanged: _onPersonalConditionChanged,
                                      ),
                                    if (_listPersonalConditionSelected
                                        .isNotEmpty)
                                      const SizedBox(height: 16),
                                    if (_listPersonalConditionSelected.contains(
                                        PersonalConditionType.nationality))
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16),
                                        child: RecruitNationality(
                                          editRecruitBloc: _editRecruitBloc,
                                          listCountry: listCountry,
                                          onDelete: () {
                                            _onPersonalConditionChanged(
                                                PersonalConditionType
                                                    .nationality,
                                                false);
                                          },
                                        ),
                                      ),
                                    if (_listPersonalConditionSelected
                                        .contains(PersonalConditionType.sex))
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16),
                                        child: RecruitSex(
                                            onDelete: () {
                                              _onPersonalConditionChanged(
                                                  PersonalConditionType.sex,
                                                  false);
                                            },
                                            editRecruitBloc: _editRecruitBloc),
                                      ),
                                    if (_listPersonalConditionSelected
                                        .contains(PersonalConditionType.age))
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16),
                                        child: RecruitAge(
                                            onDelete: () {
                                              _onPersonalConditionChanged(
                                                  PersonalConditionType.age,
                                                  false);
                                            },
                                            editRecruitBloc: _editRecruitBloc),
                                      ),
                                    if (_listPersonalConditionSelected.contains(
                                        PersonalConditionType
                                            .lastAcademicBackground))
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16),
                                        child: RecruitAcademic(
                                            onDelete: () {
                                              _onPersonalConditionChanged(
                                                  PersonalConditionType
                                                      .lastAcademicBackground,
                                                  false);
                                            },
                                            editRecruitBloc: _editRecruitBloc,
                                            list: listAcademic),
                                      ),
                                    if (_listPersonalConditionSelected.contains(
                                        PersonalConditionType.address))
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16),
                                        child: RecruitListAddress(
                                          onDeleteAddress: () {
                                            _onPersonalConditionChanged(
                                                PersonalConditionType.address,
                                                false);
                                          },
                                          editRecruitBloc: _editRecruitBloc,
                                          listState: listCountryState,
                                          listCountry: listCountry,
                                        ),
                                      ),
                                    if (_listPersonalConditionSelected.contains(
                                        PersonalConditionType.language))
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16),
                                        child: RecruitListLanguage(
                                          onDelete: () {
                                            _onPersonalConditionChanged(
                                                PersonalConditionType.language,
                                                false);
                                          },
                                          editRecruitBloc: _editRecruitBloc,
                                          listLanguage: listLanguage,
                                          listLanguageLevel: listLanguageLevel,
                                        ),
                                      ),
                                    if (_listPersonalConditionSelected
                                        .contains(PersonalConditionType.career))
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16),
                                        child: RecruitCareerAndExperience(
                                            jobCodes: listJobCode,
                                            onDelete: () {
                                              _onPersonalConditionChanged(
                                                  PersonalConditionType.career,
                                                  false);
                                            },
                                            editRecruitBloc: _editRecruitBloc),
                                      ),
                                    if (_listPersonalConditionSelected
                                        .contains(PersonalConditionType.skill))
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16),
                                        child: RecruitListSkill(
                                          editRecruitBloc: _editRecruitBloc,
                                          list: listSkillCode,
                                          onDelete: () {
                                            _onPersonalConditionChanged(
                                                PersonalConditionType.skill,
                                                false);
                                          },
                                        ),
                                      ),
                                    if (_listPersonalConditionSelected.contains(
                                        PersonalConditionType.license))
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16),
                                        child: RecruitLicence(
                                          editRecruitBloc: _editRecruitBloc,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: 16,
                              ),
                              Container(
                                margin: const EdgeInsets.only(bottom: 8),
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: AppColors.primary,
                                  ),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(right: 8),
                                          child: Text(
                                            AppText.value(context)
                                                .receiving_company_support_organization,
                                            style:
                                                AppTextStyles.paragraph02v3(),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 10),
                                      child: Builder(builder: (context) {
                                        final supportCompany =
                                            _editRecruitBloc.supportCompany;
                                        if (supportCompany != null) {
                                          return RecruitSupportCompanyItem(
                                            showEdit: true,
                                            onEdit: _changeSupportCompany,
                                            logo: supportCompany.logoImagePath,
                                            introductionPR:
                                                supportCompany.introductionPr,
                                            name: supportCompany.name,
                                            acceptingFee:
                                                supportCompany.acceptingFee,
                                            acceptingFeeCurrCode: supportCompany
                                                .acceptingFeeCurrCode,
                                            supportOutsourcingFee:
                                                supportCompany
                                                    .supportOutsourcingFee,
                                            supportOutsourcingFeeCurrCode:
                                                supportCompany
                                                    .supportOutsourcingFeeCurrCode,
                                          );
                                        }
                                        return Column(
                                          children: [
                                            GestureDetectorCustom(
                                              onTap: _changeSupportCompany,
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 16,
                                                        vertical: 5),
                                                decoration: BoxDecoration(
                                                  border: Border.all(
                                                      color: AppColors.primary),
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  color: AppColors.primary
                                                      .withValues(alpha: 0.05),
                                                ),
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              right: 8),
                                                      child: Icon(
                                                        Icons.add_circle,
                                                        color:
                                                            AppColors.primary,
                                                      ),
                                                    ),
                                                    Text(AppText.value(context)
                                                        .add),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      }),
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                });
              }
              return Container();
            },
          ),
        ),
      );
    });
  }

  Widget _buildCommonCard({required String title, required Widget child}) {
    return Container(
      decoration: BoxDecoration(
          color: AppColors.white, borderRadius: BorderRadius.circular(16)),
      padding: const EdgeInsets.all(40),
      child: Column(
        spacing: 32,
        children: [
          Text(
            title,
            style: AppTextStyles.textStyle24(context,
                    fontWeight: FontWeight.w700,
                    color: AppColors.hostCompanyPrimary)
                .copyWith(height: 1.35),
          ),
          child
        ],
      ),
    );
  }

  Widget _buildCommonTitle({
    required bool isRequired,
    required String title,
    required Widget child,
    String? description,
  }) {
    return Column(
      spacing: 16,
      children: [
        Row(
          spacing: 8,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
              decoration: BoxDecoration(
                color: isRequired ? AppColors.errorColor : AppColors.iconColor,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                isRequired
                    ? AppText.value(context).required
                    : AppText.value(context).optional,
                style: AppTextStyles.textStyle12(context,
                        fontWeight: FontWeight.w700, color: AppColors.white)
                    .copyWith(height: 1.4),
              ),
            ),
            Text(
              title,
              style: AppTextStyles.textStyle16(context,
                      fontWeight: FontWeight.w500,
                      color: AppColors.hostCompanyPrimary)
                  .copyWith(height: 1.4),
            ),
            if (description != null)
              Text(
                description,
                style: AppTextStyles.textStyle14(context,
                        fontWeight: FontWeight.w500, color: AppColors.iconColor)
                    .copyWith(height: 1.4),
              ),
          ],
        ),
        child
      ],
    );
  }
}
