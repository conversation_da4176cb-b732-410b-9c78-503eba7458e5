import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/my_recruits/my_recruits_bloc.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/my_recruits/my_recruits_event.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/my_recruits/my_recruits_state.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/user/short_list_engineer/short_list_engineer/short_list_engineer_bloc.dart';
import 'package:asiantech_link/layers/presentation/blocs/general/chat/list_group_chat/list_group_chat_cubit.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/company_home_screen/v2/ai_recommend_engineers/ai_recommend_engineers_section.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/company_home_screen/v2/engineer_in_message/engineer_in_message_section.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/company_home_screen/v2/engineer_in_selection/engineer_in_selection_section.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/company_home_screen/v2/widgets/host_company_appbar.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/company_home_screen/v2/recent_registered_engineers/recent_registered_engineers_section.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/company_home_screen/v2/recruit_opening/recruit_opening_section.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/company_home_screen/v2/trending_engineers/trending_engineers_section.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/manage_apply/manage_apply_cubit.dart';

class HostCompanyHomeScreen extends StatefulWidget {
  final GlobalKey<ScaffoldState>? scaffoldKey;
  static const String routeName = 'home';
  const HostCompanyHomeScreen({super.key, required this.scaffoldKey});

  @override
  State<HostCompanyHomeScreen> createState() => _HostCompanyHomeScreenState();
}

class _HostCompanyHomeScreenState extends State<HostCompanyHomeScreen>
    with AutomaticKeepAliveClientMixin {
  final MyRecruitsBloc _myRecruitsBloc =
      MyRecruitsBloc(const UnMyRecruitsState());
  final ManageApplyCubit _manageApplyCubit = ManageApplyCubit();
  final ListGroupChatCubit _listGroupChatCubit = ListGroupChatCubit();
  final ShortListEngineerBloc _shortListEngineerBloc = ShortListEngineerBloc();
  @override
  void initState() {
    _getData(false);
    super.initState();
  }

  _getData(bool isLoadMore) {
    _myRecruitsBloc.add(LoadMyRecruitsEvent(
        isLoadMore: isLoadMore, onDone: () {}, showOverlayLoading: false));
    _manageApplyCubit.updateOrdering("-created");
    _manageApplyCubit.loadManageApply();
    _listGroupChatCubit.getListGroupChat(ordering: "-updated");
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => _myRecruitsBloc),
        BlocProvider(create: (context) => _manageApplyCubit),
        BlocProvider(create: (context) => _listGroupChatCubit),
        BlocProvider(create: (context) => _shortListEngineerBloc),
      ],
      child: Scaffold(
        appBar: const HostCompanyAppBar(
          showBackIcon: false,
        ),
        backgroundColor: AppColors.hostCompanyBackgroundColor,
        body: Column(
          spacing: 40,
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                        vertical: 0, horizontal: context.isMobile ? 16 : 24)
                    .copyWith(bottom: context.isMobile ? 16 : 24),
                child: Column(
                  spacing: 40,
                  children: [
                    const RecruitOpeningSection(),
                    Builder(builder: (context) {
                      if (context.isSmallScreen) {
                        return Column(
                          spacing: 40,
                          children: [
                            const EngineerInSelectionSection(),
                            EngineerInMessageSection(
                              onRefresh: () {
                                _getData(false);
                              },
                            ),
                          ],
                        );
                      }
                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 40,
                        children: [
                          const Expanded(child: EngineerInSelectionSection()),
                          Expanded(
                            child: EngineerInMessageSection(
                              onRefresh: () {
                                _getData(false);
                              },
                            ),
                          ),
                        ],
                      );
                    }),
                    const AIRecommendEngineersSection(),
                    const RecentRegisteredEngineersSection(),
                    const TrendingEngineersSection(),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _manageApplyCubit.close();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
