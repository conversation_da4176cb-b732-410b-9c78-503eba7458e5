import 'package:asiantech_link/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/company_home_screen/v2/engineer_in_selection/widgets/engineer_selected_item.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/apply_details/apply_details_screen.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/manage_apply/manage_apply_cubit.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/manage_apply/manage_apply_cubit_state.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/manage_apply_screen.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:asiantech_link/layers/presentation/widgets/empty_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/loading/engineer_item_loading.dart';
import 'package:asiantech_link/layers/presentation/widgets/svg_icon.dart';

class EngineerInSelectionSection extends StatelessWidget {
  const EngineerInSelectionSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ManageApplyCubit, ManageApplyCubitState>(
      builder: (context, state) {
        return Column(
          spacing: 8,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  spacing: 8,
                  children: [
                    SvgIcon(
                      path: Assets.svg.v2.peopleSVG,
                      width: context.isMobile ? 30 : 40,
                      height: context.isMobile ? 30 : 40,
                      color: AppColors.hostCompanyPrimary,
                    ),
                    Text(
                      AppText.value(context).engineers_in_selection,
                      style: AppTextStyles.textStyle24(
                        context,
                        fontWeight: FontWeight.w700,
                        color: AppColors.hostCompanyPrimary,
                      ),
                    ),
                  ],
                ),
                GestureDetectorCustom(
                  onTap: () {
                    context.pushSubRoute(
                      ManageApplyScreen.routeName,
                    );
                  },
                  child: Text(
                    AppText.value(context).view_more,
                    style: AppTextStyles.textStyle14(
                      context,
                      fontWeight: FontWeight.w700,
                      color: AppColors.hostCompanyPrimary,
                    ).copyWith(decoration: TextDecoration.underline),
                  ),
                ),
              ],
            ),
            Builder(builder: (context) {
              if (state.isLoading) {
                return Column(
                  spacing: 16,
                  children: List.generate(3, (i) {
                    return const EngineerItemLoading();
                  }),
                );
              }
              if (state.hasData) {
                var list = state.listAppliedEngineer!.results.take(3).toList();
                if (list.isEmpty) {
                  return const SizedBox(height: 250, child: Center(child: EmptyWidget()),);
                }
                return Column(
                  spacing: 16,
                  children: List.generate(list.length, (i) {
                    var item = list[i];
                    return EngineerSelectedItem(
                      onTap: () {
                        context.pushSubRoute(
                          "${ManageApplyScreen.routeName}/${ApplyDetailsScreen.routeName}?id=${item.applyId}",
                        );
                      },
                      item: item,
                    );
                  }),
                );
              }
              return const SizedBox.shrink();
            })
          ],
        );
      },
    );
  }
}
