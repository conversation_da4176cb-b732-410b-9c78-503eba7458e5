import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/radio_title_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/divider_custom.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/my_recruits/index.dart';
import 'package:openapi/openapi.dart';

class RecruitDropdown extends StatefulWidget {
  final String label;
  final RecruitUploadedSerializers? selectedRecruit;
  final ValueChanged<RecruitUploadedSerializers?> onChanged;
  final bool isExpanded;

  const RecruitDropdown({
    Key? key,
    required this.label,
    this.selectedRecruit,
    required this.onChanged,
    this.isExpanded = false,
  }) : super(key: key);

  @override
  State<RecruitDropdown> createState() => _RecruitDropdownState();
}

class _RecruitDropdownState extends State<RecruitDropdown> {
  final MyRecruitsBloc _myRecruitsBloc = MyRecruitsBloc(const UnMyRecruitsState());
  List<RecruitUploadedSerializers> _recruitList = [];
  bool _expanded = false;
  bool _isLoading = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _expanded = widget.isExpanded;
    _loadRecruits();
  }

  @override
  void dispose() {
    _myRecruitsBloc.close();
    super.dispose();
  }

  void _loadRecruits() {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    _myRecruitsBloc.add(LoadMyRecruitsEvent(
      isLoadMore: false,
      displayFlag: RecruitDisplayFlag.public,
      onDone: () {
        // Handle completion in the bloc listener
      },
    ));
  }

  void _selectRecruit(RecruitUploadedSerializers recruit) {
    widget.onChanged(recruit);
    setState(() {
      _expanded = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final hasSelection = widget.selectedRecruit != null;

    return BlocListener<MyRecruitsBloc, MyRecruitsState>(
      bloc: _myRecruitsBloc,
      listener: (context, state) {
        if (state is InMyRecruitsState) {
          setState(() {
            _recruitList = (state.listRecruitModel.results ?? [])
                .cast<RecruitUploadedSerializers>();
            _isLoading = false;
            _hasError = false;
          });
        } else if (state is ErrorMyRecruitsState) {
          setState(() {
            _isLoading = false;
            _hasError = true;
          });
        } else if (state is LoadingMyRecruitsState) {
          setState(() {
            _isLoading = true;
            _hasError = false;
          });
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetectorCustom(
            onTap: () => setState(() => _expanded = !_expanded),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 20),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.lineColor),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.check_rounded,
                        color: hasSelection
                            ? AppColors.hoverPrimaryColor
                            : Colors.transparent,
                        size: 11,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.label,
                        style: AppTextStyles.paragraph02v3(
                          color: AppColors.hoverPrimaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    _expanded ? Icons.remove_rounded : Icons.add_rounded,
                    color: AppColors.hostCompanyPrimary,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (child, anim) =>
                SizeTransition(sizeFactor: anim, child: child),
            child: _expanded ? _buildExpandedContent() : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedContent() {
    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_hasError) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Text(
              'Error loading recruits',
              style: AppTextStyles.paragraph02v1(color: AppColors.red),
            ),
            const SizedBox(height: 10),
            TextButton(
              onPressed: _loadRecruits,
              child: Text(
                'Retry',
                style: AppTextStyles.paragraph02v1(color: AppColors.primary),
              ),
            ),
          ],
        ),
      );
    }

    if (_recruitList.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Text(
          AppText.value(context).no_recruits_available,
          style: AppTextStyles.paragraph02v1(color: AppColors.grey1),
        ),
      );
    }

    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      child: ListView.separated(
        shrinkWrap: true,
        padding: const EdgeInsets.all(16),
        itemCount: _recruitList.length,
        itemBuilder: (context, index) {
          final recruit = _recruitList[index];

          return RadioTitleCustom(
            mainAxisSize: MainAxisSize.min,
            groupValue: widget.selectedRecruit,
            selectedColor: AppColors.primary,
            unSelectedColor: AppColors.disabledButton,
            title: recruit.title ?? 'Untitled',
            value: recruit,
            onChanged: (value) => _selectRecruit(value!),
          );
        },
        separatorBuilder: (context, index) {
          return const DividerCustom();
        },
      ),
    );
  }
}
