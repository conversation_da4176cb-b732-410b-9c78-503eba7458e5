import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';

import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/my_recruits/index.dart';
import 'package:openapi/openapi.dart';

class RecruitDropdown extends StatefulWidget {
  final String label;
  final RecruitUploadedSerializers? selectedRecruit;
  final ValueChanged<RecruitUploadedSerializers?> onChanged;
  final String? hint;

  const RecruitDropdown({
    Key? key,
    required this.label,
    this.selectedRecruit,
    required this.onChanged,
    this.hint,
  }) : super(key: key);

  @override
  State<RecruitDropdown> createState() => _RecruitDropdownState();
}

class _RecruitDropdownState extends State<RecruitDropdown> {
  final MyRecruitsBloc _myRecruitsBloc = MyRecruitsBloc(const UnMyRecruitsState());
  List<RecruitUploadedSerializers> _recruitList = [];
  bool _isLoading = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadRecruits();
  }

  @override
  void dispose() {
    _myRecruitsBloc.close();
    super.dispose();
  }

  void _loadRecruits() {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    _myRecruitsBloc.add(LoadMyRecruitsEvent(
      isLoadMore: false,
      displayFlag: RecruitDisplayFlag.public,
      onDone: () {
        // Handle completion in the bloc listener
      },
    ));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<MyRecruitsBloc, MyRecruitsState>(
      bloc: _myRecruitsBloc,
      listener: (context, state) {
        if (state is InMyRecruitsState) {
          setState(() {
            _recruitList = (state.listRecruitModel.results ?? [])
                .cast<RecruitUploadedSerializers>();
            _isLoading = false;
            _hasError = false;
          });
        } else if (state is ErrorMyRecruitsState) {
          setState(() {
            _isLoading = false;
            _hasError = true;
          });
        } else if (state is LoadingMyRecruitsState) {
          setState(() {
            _isLoading = true;
            _hasError = false;
          });
        }
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 15.0),
        child: Row(
          spacing: 15,
          children: [
            Text(
              widget.label,
              style: AppTextStyles.paragraph02v1(
                color: AppColors.iconColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            Expanded(
              child: _buildDropdown(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdown() {
    if (_isLoading) {
      return Container(
        height: 48,
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.lineColor),
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: const Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    if (_hasError) {
      return Container(
        height: 48,
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.red),
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Row(
          children: [
            const Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  'Error loading recruits',
                  style: TextStyle(color: AppColors.red),
                ),
              ),
            ),
            IconButton(
              onPressed: _loadRecruits,
              icon: const Icon(Icons.refresh, color: AppColors.red),
            ),
          ],
        ),
      );
    }

    if (_recruitList.isEmpty) {
      return Container(
        height: 48,
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.lineColor),
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              AppText.value(context).no_recruits_available,
              style: AppTextStyles.paragraph02v1(color: AppColors.grey1),
            ),
          ),
        ),
      );
    }

    return DropdownMenu<RecruitUploadedSerializers>(
      initialSelection: widget.selectedRecruit,
      onSelected: (RecruitUploadedSerializers? value) {
        widget.onChanged(value);
      },
      requestFocusOnTap: false,
      enableFilter: false,
      enableSearch: false,
      menuHeight: context.height * 0.4,
      expandedInsets: EdgeInsets.zero,
      hintText: widget.hint ?? AppText.value(context).select_recruit,
      trailingIcon: const Icon(
        Icons.arrow_drop_down_rounded,
        color: AppColors.iconColor,
      ),
      selectedTrailingIcon: Icon(
        Icons.arrow_drop_up_rounded,
        color: AppColors.primary,
      ),
      dropdownMenuEntries: _recruitList.map((recruit) {
        return DropdownMenuEntry<RecruitUploadedSerializers>(
          value: recruit,
          label: recruit.title ?? 'Untitled',
          trailingIcon: recruit == widget.selectedRecruit
              ? Icon(
                  Icons.check,
                  size: 22,
                  color: AppColors.primary,
                )
              : null,
        );
      }).toList(),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.lineColor),
          borderRadius: BorderRadius.circular(8),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.lineColor),
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primary),
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      alignmentOffset: const Offset(0, 5),
    );
  }
}
