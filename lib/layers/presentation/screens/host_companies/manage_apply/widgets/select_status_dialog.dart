import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:asiantech_link/layers/presentation/widgets/button_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/check_box_title_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/divider_custom.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class SelectStatusDialog extends StatefulWidget {
  final List<int> listStatus;
  const SelectStatusDialog({super.key, required this.listStatus});

  @override
  State<SelectStatusDialog> createState() => _SelectStatusDialogState();
}

class _SelectStatusDialogState extends State<SelectStatusDialog> {
  List<int> _listStatusSelected = [];
  @override
  void initState() {
    _listStatusSelected = widget.listStatus.toList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var list = [
      RecruitProgressCodeType.requestingAnAgent,
      RecruitProgressCodeType.application,
      RecruitProgressCodeType.interviewRequest,
      RecruitProgressCodeType.interviewScheduling,
      RecruitProgressCodeType.interviewDateConfirmed,
      RecruitProgressCodeType.interviewCompleted,
      RecruitProgressCodeType.jobOffer,
      RecruitProgressCodeType.offerAccepted,
      RecruitProgressCodeType.employed,
      RecruitProgressCodeType.notPassed,
      RecruitProgressCodeType.applicationWithdrawn,
      RecruitProgressCodeType.interviewWithdrawn,
      RecruitProgressCodeType.offerDeclined,
      RecruitProgressCodeType.otherCompanyOffer,
    ];
    return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 20, top: 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppText.value(context).select_status,
                      style: AppTextStyles.heading05(),
                    ),
                    IconButton(
                      onPressed: () {
                        context.pop();
                      },
                      icon: const Icon(
                        Icons.close,
                        color: AppColors.black,
                      ),
                    )
                  ],
                ),
              ),
              Expanded(
                  child: ListView.separated(
                      itemBuilder: (_, index) {
                        var name = Helper.getRecruitProgressCodeName(
                            context: context, code: list[index]);
                        bool selected =
                            _listStatusSelected.contains(list[index]);
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: CheckBoxTitleCustom(
                              fillColor: WidgetStateProperty.resolveWith<Color>(
                                  (Set<WidgetState> states) {
                                return (selected)
                                    ? AppColors.primary
                                    : AppColors.white;
                              }),
                              value: selected,
                              onChanged: (value) {
                                if (value == true) {
                                  _listStatusSelected.add(list[index]);
                                } else {
                                  _listStatusSelected.remove(list[index]);
                                }
                                setState(() {});
                              },
                              title: name),
                        );
                      },
                      separatorBuilder: (_, index) {
                        return const DividerCustom();
                      },
                      itemCount: list.length)),
              const SizedBox(
                height: 20,
              ),
              ButtonCustom(
                  textStyle: AppTextStyles.caption01(
                    color: Colors.white,
                  ),
                  backgroundColor: AppColors.primary,
                  title: AppText.value(context).apply,
                  onPressed: () {
                    context.pop(_listStatusSelected);
                  }),
            ],
          ),
        ));
  }
}
