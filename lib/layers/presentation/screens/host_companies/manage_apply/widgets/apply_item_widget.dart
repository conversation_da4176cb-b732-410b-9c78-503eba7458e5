import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/primary_button.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:asiantech_link/layers/presentation/widgets/cached_image.dart';
import 'package:asiantech_link/layers/presentation/widgets/skills_display_widget.dart';
import 'package:openapi/openapi.dart';

/// Widget for displaying individual apply item in the manage apply screen
class ApplyItem extends StatelessWidget {
  final GeneralCompanyAppliedEngineers appliedEngineers;
  final VoidCallback onViewDetail;
  final VoidCallback onFavoriteToggle;

  const ApplyItem({
    super.key,
    required this.appliedEngineers,
    required this.onViewDetail,
    required this.onFavoriteToggle,
  });

  /// Get the appropriate note text based on the recruitment progress code
  String getApplyNote(BuildContext context) {
    switch (appliedEngineers.recruitProgressCode) {
      case RecruitProgressCodeType.application:
        return AppText.value(context).register_selection_results_update_status;
      case RecruitProgressCodeType.interviewScheduling:
        return AppText.value(context)
            .adjust_interview_schedule_chat_register_time;
      case RecruitProgressCodeType.interviewDateConfirmed:
        var dateTime = appliedEngineers.interviewDatetime;
        var dateTimeStr =
            Helper.dateTimeToString(dateTime, format: "MMM dd, yyyy hh:mm a");
        return AppText.value(context).interview_time_x(dateTimeStr);
      case RecruitProgressCodeType.interviewCompleted:
        return AppText.value(context).register_interview_results_update_status;
      case RecruitProgressCodeType.jobOffer:
        return AppText.value(context).sign_contract_accept_offer;
      case RecruitProgressCodeType.offerAccepted:
        return AppText.value(context)
            .check_job_offer_contract_and_complete_onboarding;
      default:
        return "";
    }
  }

  @override
  Widget build(BuildContext context) {
    final model = appliedEngineers.engineer;
    final titlesInfo = [
      AppText.value(context).residence,
      AppText.value(context).employment_status_company,
      AppText.value(context).recent_occupation,
      AppText.value(context).highestEducation,
      AppText.value(context).language,
      AppText.value(context).qualification,
      AppText.value(context).work_history,
    ];
    final titleSkills = [
      AppText.value(context).job_position,
      AppText.value(context).employmentType,
      AppText.value(context).annual_salary,
      AppText.value(context).work_place,
    ];
    final titleWidthInfo = _calculateMaxTitleWidth(context, titlesInfo);
    final titleWidthSkill = _calculateMaxTitleWidth(context, titleSkills);

    final String employmentStatusCompany = Helper.getRecruitProgressCodeName(
        context: context, code: model.currentRecruitProgressCodeWithMyCompany);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Column(
        children: [
          Container(
            height: 50,
            decoration: const BoxDecoration(
              color: AppColors.iconColor,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(5), topRight: Radius.circular(5)),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                RichText(
                  text: TextSpan(
                      style: AppTextStyles.paragraph02v1(
                          color: AppColors.white, fontWeight: FontWeight.w500),
                      children: [
                        TextSpan(
                          style: AppTextStyles.paragraph01(
                              color: AppColors.white,
                              fontWeight: FontWeight.w500),
                          text:
                              '${getFullNameAndNickName(context: context, firstName: model.firstName, lastName: model.lastName, nickName: model.nickname)}\t',
                        ),
                        TextSpan(
                          text: Helper.getEngineerHightLight(
                              context: context,
                              countryCode: model.countryCode ?? '',
                              sexType: model.sexType,
                              age: model.age,
                              jobChangeCount: model.totalCompanyChangedCount),
                        ),
                      ]),
                ),
                if (appliedEngineers.recruitProgressCode != null)
                  Container(
                    alignment: Alignment.center,
                    padding:
                        const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                        color: AppColors.red2,
                        borderRadius: BorderRadius.circular(24)),
                    child: Text(
                      Helper.getRecruitProgressCodeName(
                          context: context,
                          code: appliedEngineers.recruitProgressCode),
                      textAlign: TextAlign.center,
                      style: AppTextStyles.textStyle14(context,
                              color: AppColors.whiteText,
                              fontWeight: FontWeight.bold)
                          .copyWith(height: 1.5),
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 40),
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 16,
                children: [
                  Container(
                    width: 150,
                    margin: const EdgeInsets.only(right: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        CacheImageNetworkWidget(
                          imageUrl: "${model.profileImagePath}",
                          width: 150,
                          height: 150,
                          boxFit: BoxFit.cover,
                          borderRadius: 8,
                        ),
                        _buildDateRow(AppText.value(context).registration_date,
                            appliedEngineers.created),
                        _buildDateRow(AppText.value(context).updated_at,
                            appliedEngineers.updated),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 4,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            AppText.value(context).profile,
                            style: AppTextStyles.textStyle16(
                              context,
                              color: AppColors.hostCompanyPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        if (model.addressCode != null)
                          _buildInformationRow(
                              AppText.value(context).residence,
                              Helper.getStateAndCountryName(
                                  context: context, code: model.addressCode),
                              titleWidthInfo),
                        _buildInformationRow(
                            AppText.value(context).employment_status_company,
                            employmentStatusCompany.isEmpty
                                ? AppText.value(context).not_set
                                : employmentStatusCompany,
                            titleWidthInfo),
                        _buildInformationRow(
                            AppText.value(context).recent_occupation,
                            Helper.getJobCodeName(context,
                                model.jobCodeHasWorkedRecently.toString()),
                            titleWidthInfo),
                        _buildInformationRow(
                            AppText.value(context).highestEducation,
                            Helper.getAcademicLevelName(
                                  context: context,
                                  id: int.tryParse(
                                      model.lastAcademicCode ?? '')),
                            titleWidthInfo),
                        _buildInformationRow(AppText.value(context).language,
                            Helper.getLanguageNameByList(
                                context: context,
                                languageCodes:
                                    Helper.getLanguageNameByListModel(
                                        model.languages)), titleWidthInfo),
                        _buildInformationRow(
                            AppText.value(context).qualification,
                            Helper.parseQualificationsToString(
                                context: context,
                                qualifications: model.qualifications),
                            titleWidthInfo),
                        if (Helper.buildWorkHistoryRange(
                                context, model.workHistories?.toList() ?? [])
                            .isNotEmpty)
                          _buildInformationRow(
                              AppText.value(context).work_history,
                              Helper.buildWorkHistoryRange(
                                  context, model.workHistories?.toList() ?? []),
                              titleWidthInfo),
                        Padding(
                          padding: const EdgeInsets.only(top: 12.0),
                          child: Row(
                            spacing: 16,
                            children: [
                              _buildInformationBox(
                                AppText.value(context).last_login,
                                AppText.value(context).not_set,
                              ),
                              _buildInformationBox(
                                AppText.value(context).under_selection_count,
                                AppText.value(context).not_set,
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  if ((model.skills != null && model.skills!.isNotEmpty) ||
                      model.requirements != null)
                    const VerticalDivider(
                      width: 10,
                      thickness: 1,
                      color: AppColors.lineColor,
                    ),
                  Expanded(
                      child: Column(
                    spacing: 16,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (model.skills != null && model.skills!.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: Text(
                                AppText.value(context).development_skills,
                                style: AppTextStyles.textStyle16(
                                  context,
                                  color: AppColors.hostCompanyPrimary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            SkillsDisplayWidget(
                              skills: model.skills!.toList(),
                            ),
                          ],
                        ),
                      if (model.requirements != null)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 4,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 4.0),
                              child: Text(
                                AppText.value(context).desired_conditions,
                                style: AppTextStyles.textStyle16(
                                  context,
                                  color: AppColors.hostCompanyPrimary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            if (model.requirements?.jobSkills != null)
                              _buildInformationRow(
                                  AppText.value(context).job_position,
                                  Helper.extractJobCodeNames(
                                      context: context,
                                      jobSkills: model.requirements?.jobSkills),
                                  titleWidthSkill),
                            if (model.requirements?.employCode != null)
                              _buildInformationRow(
                                  AppText.value(context).employmentType,
                                  Helper.getEmployCodeName(
                                      context: context,
                                      code: model.requirements?.employCode),
                                  titleWidthSkill),
                            if (model.requirements?.payrollPrice != null)
                              _buildInformationRow(
                                  AppText.value(context).annual_salary,
                                  [
                                    model.requirements?.payrollCode == 'JPN'
                                        ? Helper.formatYenInMan(
                                            model.requirements?.payrollPrice ??
                                                '')
                                        : Helper.formatMoney(
                                            model.requirements?.payrollPrice ??
                                                ''),
                                    Helper.getCurrencyName(
                                        context: context,
                                        currencyCode:
                                            model.requirements?.payrollCode ??
                                                '')
                                  ].join(' '),
                                  titleWidthSkill),
                            _buildInformationRow(
                                AppText.value(context).work_place,
                                Helper.getStatesAndCountries(
                                    context: context,
                                    codes: [
                                      model.requirements?.placeCode1,
                                      model.requirements?.placeCode2,
                                      model.requirements?.placeCode3
                                    ]),
                                titleWidthSkill),
                          ],
                        ),
                    ],
                  ))
                ],
              ),
            ),
          ),
          if (getApplyNote(context).isNotEmpty)
            const SizedBox(
              height: 24,
            ),
          if (getApplyNote(context).isNotEmpty)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 24),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: AppColors.hostCompanyBackgroundColor,
              ),
              child: Row(
                spacing: 24,
                children: [
                  SvgPicture.asset(
                    Assets.svg.speakerIconSVG,
                  ),
                  Container(width: 1, height: 40, color: AppColors.iconColor),
                  Expanded(
                      child: Text(
                    getApplyNote(context),
                    style: AppTextStyles.textStyle14(
                      context,
                      color: AppColors.hoverPrimaryColor,
                      fontWeight: FontWeight.w500,
                    ).copyWith(height: 1.4),
                  )),
                ],
              ),
            ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
            child: SizedBox(
              height: 34,
              child: Row(
                spacing: 15,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Builder(builder: (context) {
                    Color color = AppColors.iconColor;
                    String title = AppText.value(context).add_to_favorites;
                    IconData icon = Icons.favorite_border_rounded; 
                    if (model.interestedFlag == 1) {
                      color = AppColors.red;
                      icon = Icons.favorite_rounded;
                      title = AppText.value(context).remove_from_favorites;
                    }

                    return GestureDetectorCustom(
                      onTap: onFavoriteToggle,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 65),
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(24),
                          border: Border.all(
                              color: AppColors.iconColor,
                              strokeAlign: BorderSide.strokeAlignInside),
                        ),
                        child: Center(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            spacing: 4,
                            children: [
                              Icon(
                                icon,
                                color: color,
                                size: 20,
                              ),
                              Text(
                                title,
                                style: AppTextStyles.textStyle14(context,
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.iconColor),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }),
                  PrimaryButton(
                    height: 34,
                    padding: const EdgeInsets.symmetric(horizontal: 65),
                    onPressed: onViewDetail,
                    buttonText: AppText.value(context).view_details,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateRow(String title, DateTime? des) {
    if (des == null) return const SizedBox();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Container(
              alignment: Alignment.centerLeft,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Text(
                  title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.bodySmall(
                    fontWeight: FontWeight.w700,
                    color: AppColors.iconColor,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          Flexible(
            child: Container(
              alignment: Alignment.centerRight,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerRight,
                child: Text(
                  Helper.dateTimeToString(des, format: 'yyyy/MM/dd'),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.paragraph02v2(
                    color: AppColors.hoverPrimaryColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInformationRow(String title, String? des, double titleWidth) {
    if (des == null) return const SizedBox();
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: titleWidth,
          child: Text(
            title,
            style: AppTextStyles.paragraph02v2(color: AppColors.iconColor),
          ),
        ),
        Container(
          padding: const EdgeInsets.only(left: 7.0),
          width: 17,
          child: Text(
            ':',
            style: AppTextStyles.paragraph02v2(color: AppColors.iconColor),
          ),
        ),
        Expanded(
          flex: 5,
          child: Text(
            des,
            style:
                AppTextStyles.paragraph02v2(color: AppColors.hoverPrimaryColor),
          ),
        ),
      ],
    );
  }

  double _calculateMaxTitleWidth(BuildContext context, List<String> titles) {
    final style = AppTextStyles.paragraph02v2(color: AppColors.iconColor);
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      maxLines: 1,
    );

    double maxWidth = 0;
    for (final title in titles) {
      textPainter.text = TextSpan(text: title, style: style);
      textPainter.layout();
      if (textPainter.width > maxWidth) {
        maxWidth = textPainter.width;
      }
    }
    return maxWidth;
  }

  Widget _buildInformationBox(String tittle, String? des) {
    if (des == null) return const SizedBox();
    return Row(
      spacing: 8,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 7),
          decoration: BoxDecoration(
              color: AppColors.hostCompanyBackgroundColor,
              borderRadius: BorderRadius.circular(5)),
          child: Text(
            tittle,
            style: AppTextStyles.bodySmall(
                color: AppColors.iconColor, fontWeight: FontWeight.w700),
          ),
        ),
        Text(
          des,
          style:
              AppTextStyles.paragraph02v2(color: AppColors.hoverPrimaryColor),
        ),
      ],
    );
  }
}

// Positioned(
//             top: 0,
//             right: 0,
//             child: Builder(builder: (context) {
//               if (appliedEngineers.unreadMessageCount != null &&
//                   (appliedEngineers.unreadMessageCount ?? 0) > 0) {
//                 return Container(
//                   height: 24,
//                   decoration: const BoxDecoration(
//                     color: AppColors.purple,
//                     borderRadius: BorderRadius.only(
//                       bottomLeft: Radius.circular(10),
//                       topRight: Radius.circular(10),
//                     ),
//                   ),
//                   padding:
//                       const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
//                   child: Center(
//                     child: Badge(
//                         largeSize: 10,
//                         label: Text(
//                           "${appliedEngineers.unreadMessageCount}",
//                           style: const TextStyle(fontSize: 5),
//                         ),
//                         child: SvgPicture.asset(Assets.svg.chat2IconSVG)),
//                   ),
//                 );
//               }
//               if (appliedEngineers.isNew == true) {
//                 return Container(
//                   decoration: const BoxDecoration(
//                       color: AppColors.red2,
//                       borderRadius: BorderRadius.only(
//                         bottomLeft: Radius.circular(10),
//                         topRight: Radius.circular(10),
//                       )),
//                   padding:
//                       const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
//                   child: Text(
//                     "New",
//                     style: AppTextStyles.bodySmall(
//                       color: AppColors.white,
//                     ),
//                   ),
//                 );
//               }
//               return Container();
//             }),
//           )
