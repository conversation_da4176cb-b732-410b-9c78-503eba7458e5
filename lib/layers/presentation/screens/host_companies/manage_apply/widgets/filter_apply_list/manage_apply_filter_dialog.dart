import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/widgets/filter_apply_list/status_filter.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/widgets/recruit_dropdown.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/transparent_button.dart';
import 'package:asiantech_link/layers/presentation/widgets/button_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/title_text_field.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:openapi/openapi.dart';

class ManageApplyFilterDialog extends StatefulWidget {
  final RecruitUploadedSerializers? recruitModel;
  final List<int>? progressCodes;
  final String? search;
  const ManageApplyFilterDialog(
      {super.key, this.recruitModel, this.progressCodes, this.search});

  @override
  State<ManageApplyFilterDialog> createState() =>
      _ManageApplyFilterDialogState();
}

class _ManageApplyFilterDialogState extends State<ManageApplyFilterDialog> {
  List<int>? _recruitProgressCodes;
  RecruitUploadedSerializers? _recruitModel;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    _recruitProgressCodes = widget.progressCodes;
    _recruitModel = widget.recruitModel;
    _searchController.text = widget.search ?? "";

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 62,
            decoration: const BoxDecoration(
                color: AppColors.hostCompanyBackgroundColor,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(5), topRight: Radius.circular(5))),
            padding: const EdgeInsets.symmetric(horizontal: 23),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  spacing: 11,
                  children: [
                    SvgPicture.asset(
                      height: 18,
                      width: 20,
                      Assets.svg.filterIconCompanySVG,
                      colorFilter: const ColorFilter.mode(
                          AppColors.hostCompanyPrimary, BlendMode.srcIn),
                    ),
                    Text(
                      AppText.value(context).filter_conditions,
                      style: AppTextStyles.textStyle18(context,
                          color: AppColors.hostCompanyPrimary,
                          fontWeight: FontWeight.w700),
                    ),
                  ],
                ),
                IconButton(
                  onPressed: () {
                    context.pop();
                  },
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    margin: const EdgeInsets.all(40).copyWith(bottom: 0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: AppColors.lineColor),
                    ),
                    child: TitleTextField(
                      fillColor: AppColors.hostCompanyBackgroundColor,
                      controller: _searchController,
                      hintText:
                          AppText.value(context).enter_recruit_title_user_name,
                      autoFocus: true,
                      suffixIcon: const Icon(
                        Icons.search_rounded,
                        color: AppColors.lineColor,
                        size: 27,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(40).copyWith(bottom: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppText.value(context).application_status,
                          style: AppTextStyles.textStyle16(context,
                                  color: AppColors.green3,
                                  fontWeight: FontWeight.w500)
                              .copyWith(height: 1.6),
                        ),
                        // Recruit Selection using RecruitDropdown component
                        RecruitDropdown(
                          label: AppText.value(context).recruitment_requirements,
                          selectedRecruit: _recruitModel,
                          onChanged: (selectedRecruit) {
                            setState(() {
                              _recruitModel = selectedRecruit;
                            });
                          },
                        ),
                        const SizedBox(
                          height: 32,
                        ),
                        // Status Selection using StatusFilter component
                        StatusFilter(
                          label: AppText.value(context).select_status,
                          selectedValues: _recruitProgressCodes ?? [],
                          onChanged: (selectedValues) {
                            setState(() {
                              _recruitProgressCodes = selectedValues;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 20, bottom: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              spacing: 40,
              children: [
                TransparentButton(
                  height: 50,
                  width: 240,
                  textStyle: AppTextStyles.heading05(
                    color: AppColors.primary,
                  ).copyWith(fontSize: 15),
                  borderRadius: 24,
                  color: AppColors.primary,
                  onPressed: () {},
                  buttonText: AppText.value(context).clear,
                ),
                SizedBox(
                  height: 50,
                  width: 240,
                  child: ButtonCustom(
                      textStyle: AppTextStyles.caption01(
                        color: Colors.white,
                      ),
                      backgroundColor: AppColors.primary,
                      title: AppText.value(context).show_x_results("0"),
                      onPressed: () {
                        context.pop({
                          "recruit": _recruitModel,
                          "progress_codes": _recruitProgressCodes,
                          "search": _searchController.text,
                        });
                      }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
