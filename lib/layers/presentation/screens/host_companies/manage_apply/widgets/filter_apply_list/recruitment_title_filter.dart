import 'package:flutter/material.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/radio_title_custom.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';

class RecruitmentTitleFilter<T> extends StatefulWidget {
  final String label;
  final List<T> items;
  final String? selectedValue;
  final String Function(T item) getValue;
  final String Function(T item) getTitle;
  final bool isExpanded;
  final ValueChanged<String> onChanged;

  const RecruitmentTitleFilter({
    Key? key,
    required this.label,
    required this.items,
    this.selectedValue,
    this.isExpanded = false,
    required this.getValue,
    required this.getTitle,
    required this.onChanged,
  }) : super(key: key);

  @override
  FilterChildCommonState<T> createState() => FilterChildCommonState<T>();
}

class FilterChildCommonState<T> extends State<RecruitmentTitleFilter<T>> {
  bool _expanded = false;

  @override
  initState() {
    super.initState();
    _expanded = widget.isExpanded;
  }

  @override
  Widget build(BuildContext context) {
    final hasSelection = widget.selectedValue != null;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetectorCustom(
          onTap: () => setState(() => _expanded = !_expanded),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 20),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppColors.lineColor),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.check_rounded,
                      color: hasSelection
                          ? AppColors.hoverPrimaryColor
                          : Colors.transparent,
                      size: 11,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.label,
                      style: AppTextStyles.paragraph02v3(
                        color: AppColors.hoverPrimaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Icon(
                  _expanded ? Icons.remove_rounded : Icons.add_rounded,
                  color: AppColors.hostCompanyPrimary,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (child, anim) =>
              SizeTransition(sizeFactor: anim, child: child),
          child: _expanded
              ? LayoutBuilder(
                  builder: (context, constraints) {
                    final isMobile = constraints.maxWidth < 600;
                    final columns = isMobile ? 2 : 3;
                    return Column(
                      children: List.generate(
                        (widget.items.length / columns).ceil(),
                        (rowIndex) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Row(
                              children: List.generate(columns, (colIndex) {
                                final itemIndex = rowIndex * columns + colIndex;
                                if (itemIndex >= widget.items.length) {
                                  return const Expanded(child: SizedBox());
                                }

                                final item = widget.items[itemIndex];
                                final value = widget.getValue(item);

                                return Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 5),
                                    child: RadioTitleCustom(
                                      mainAxisSize: MainAxisSize.min,
                                      groupValue: widget.selectedValue,
                                      selectedColor: AppColors.primary,
                                      unSelectedColor: AppColors.disabledButton,
                                      title: widget.getTitle(item),
                                      value: value,
                                      onChanged: (v) => widget.onChanged(v!),
                                    ),
                                  ),
                                );
                              }),
                            ),
                          );
                        },
                      ),
                    );
                  },
                )
              : const SizedBox.shrink(),
        ),
      ],
    );
  }
}