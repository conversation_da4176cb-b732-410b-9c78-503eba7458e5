import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/manage_apply/manage_apply_bloc.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/manage_apply/manage_apply_event.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/manage_apply/manage_apply_state.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/resources/enum.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/apply_details/apply_details_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/widgets/apply_item_widget.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/widgets/filter_button_widget_recruit.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/widgets/filter_apply_list/manage_apply_filter_dialog.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';

import 'package:asiantech_link/layers/presentation/widgets/empty_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/error_app_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/loading_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/scaffold/app_secondary_scaffold.dart';
import 'package:asiantech_link/layers/presentation/widgets/pagination_sort_header.dart';
import 'package:asiantech_link/layers/presentation/widgets/pagination_footer.dart';
import 'package:asiantech_link/routes/router.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import 'package:openapi/openapi.dart';

class ManageApplyScreen extends StatefulWidget {
  static const String routeName = 'manage-apply';
  final int userType;
  const ManageApplyScreen({super.key, required this.userType});

  @override
  State<ManageApplyScreen> createState() => _ManageApplyScreenState();
}

class _ManageApplyScreenState extends State<ManageApplyScreen> {
  final ManageApplyBloc _manageApplyBloc =
      ManageApplyBloc(const UnManageApplyState());

  List<int>? _recruitProgressCodes;
  RecruitUploadedSerializers? _recruitModel;
  int? _hostCompanyId;

  String? _search;
  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    await Future.delayed(const Duration(milliseconds: 100));
    final uri = Uri.base;
    final hostCompanyId = uri.queryParameters['hostCompanyId'];
    if (hostCompanyId != null) {
      _hostCompanyId = int.tryParse(hostCompanyId);
    }
    _getData();
  }

  _getData({int page = 1, String? ordering}) {
    _manageApplyBloc.add(LoadManageApplyEvent(
      ordering: ordering ?? "created",
      recruitId: _recruitModel?.recruitId,
      progressCodes: _recruitProgressCodes,
      search: _search,
      hostCompanyId: _hostCompanyId,
      page: page,
    ));
  }

  /// Check if there are any active filters
  bool _hasActiveFilters() {
    final hasSearch = _search?.isNotEmpty ?? false;
    final hasRecruitModel = _recruitModel != null;
    final hasProgressCodes = _recruitProgressCodes?.isNotEmpty ?? false;

    return hasSearch || hasRecruitModel || hasProgressCodes;
  }

  /// Convert current ordering string to OrderingOption
  OrderingOption _getOrderingOption(String ordering) {
    switch (ordering) {
      case 'created':
        return OrderingOption.newest;
      case '-total_recruit_progress_code_active':
        return OrderingOption.popular;
      case '-engineer__birth_date':
        return OrderingOption.age;
      case 'payroll_price':
        return OrderingOption.salary;
      default:
        return OrderingOption.newest;
    }
  }

  Widget _buildItemCurrentFilter(String filterName, VoidCallback onDeleteItem) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      height: 37,
      padding: const EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppColors.lineColor),
      ),
      child: Row(
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 5),
            child: Text(
              filterName,
              style: AppTextStyles.paragraph02v1(
                  color: AppColors.black, fontWeight: FontWeight.w500),
            ),
          ),
          GestureDetectorCustom(
            onTap: onDeleteItem,
            child: const Icon(
              Icons.close,
              size: 18,
              color: AppColors.black,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ManageApplyBloc, ManageApplyState>(
        bloc: _manageApplyBloc,
        builder: (context, state) {
          return AppSecondaryScaffold(
              userType: widget.userType,
              showBackIcon: false,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(right: 10),
                      child: FilterButtonWidgetRecruit(
                        onPressed: () async {
                          final result = await showDialog(
                              context: context,
                              builder: (_) {
                                return ManageApplyFilterDialog(
                                  recruitModel: _recruitModel,
                                  progressCodes: _recruitProgressCodes,
                                  search: _search,
                                  hostCompanyId: _hostCompanyId,
                                );
                              });
                          if (context.mounted && result != null) {
                            _recruitModel = result['recruit'];
                            _recruitProgressCodes = result['progress_codes'];
                            _search = result['search'];
                            _getData();
                          }
                        },
                      ),
                    ),
                  ),
                  _hasActiveFilters()
                      ? SingleChildScrollView(
                          padding: EdgeInsets.zero,
                          scrollDirection: Axis.horizontal,
                          child: Padding(
                            padding: const EdgeInsets.all(24),
                            child: Row(
                              children: [
                                if ((_search?.isNotEmpty ?? false) ||
                                    (_recruitModel != null) ||
                                    (_recruitProgressCodes?.isNotEmpty ??
                                        false))
                                  Text(
                                    "${AppText.value(context).filter}: ",
                                    style: AppTextStyles.paragraph02v1(
                                      color: AppColors.iconColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                if (_search?.isNotEmpty ?? false)
                                  _buildItemCurrentFilter(
                                    "${AppText.value(context).search}: $_search",
                                    () {
                                      setState(() {
                                        _search = null;
                                      });
                                      _getData();
                                    },
                                  ),
                                if (_recruitModel != null)
                                  _buildItemCurrentFilter(
                                    "${AppText.value(context).recruitment}: ${_recruitModel?.title}",
                                    () {
                                      setState(() {
                                        _recruitModel = null;
                                      });
                                      _getData();
                                    },
                                  ),
                                if (_recruitProgressCodes?.isNotEmpty ?? false)
                                  _buildItemCurrentFilter(
                                    _recruitProgressCodes?.isNotEmpty == true
                                        ? "${AppText.value(context).status}: ${_recruitProgressCodes?.map((e) {
                                            return Helper
                                                .getRecruitProgressCodeName(
                                                    context: context, code: e);
                                          }).reduce((a, b) => "$a, $b")}"
                                        : AppText.value(context).select_status,
                                    () {
                                      setState(() {
                                        _recruitProgressCodes = null;
                                      });
                                      _getData();
                                    },
                                  ),
                              ],
                            ),
                          ),
                        )
                      : const SizedBox(
                          height: 39,
                        ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Row(
                      spacing: 8,
                      children: [
                        SvgPicture.asset(Assets.svg.iconEngineerApplySVG,
                            width: 40, height: 40),
                        Text(
                          AppText.value(context).engineer_under_selection,
                          style: AppTextStyles.heading03v1(
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.hostCompanyPrimary)
                              .copyWith(height: 1.35),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 13,
                  ),
                  PaginationSortHeader.forEngineers(
                    context: context,
                    totalCount: state is InManageApplyState
                        ? state.listAppliedEngineer.count
                        : 0,
                    currentPage: state.currentPage,
                    selectedOrdering: _getOrderingOption(state.ordering),
                    onOrderingChanged: (OrderingOption? newValue) {
                      if (newValue == null) return;
                      _getData(ordering: newValue.valueRequestRecruit);
                    },
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                  ),
                  Expanded(
                    child: Builder(builder: (context) {
                      if (state is ErrorManageApplyState) {
                        return ErrorAppWidget(
                          onPressed: () {
                            _getData();
                          },
                        );
                      }

                      if (state is InManageApplyState) {
                        var list = state.listAppliedEngineer.results.toList();

                        if (list.isEmpty) {
                          return const Center(child: EmptyWidget());
                        }
                        // Calculate pagination info
                        final totalItems = state.listAppliedEngineer.count;
                        const itemsPerPage = 20;
                        final totalPages = PaginationHelper.calculateTotalPages(
                            totalItems, itemsPerPage);

                        return SingleChildScrollView(
                          child: Column(
                            children: [
                              ListView.separated(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 20),
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemBuilder: (_, index) {
                                  var item = list[index];
                                  return ApplyItem(
                                    appliedEngineers: item,
                                    onViewDetail: () async {
                                      String path =
                                          "${getParentRoute(context)}/${ApplyDetailsScreen.routeName}?id=${item.applyId}";
                                      if (_hostCompanyId != null) {
                                        path =
                                            "$path&hostCompanyId=$_hostCompanyId";
                                      }
                                      await context.push(path);
                                      _getData();
                                    },
                                    onFavoriteToggle: () {
                                      _manageApplyBloc.add(
                                        UpdateFavoriteUserEvent(
                                          isFavorite:
                                              item.engineer.interestedFlag == 1
                                                  ? false
                                                  : true,
                                          userId: item.engineer.userId ?? 0,
                                          applyIndex: index,
                                        ),
                                      );
                                    },
                                  );
                                },
                                separatorBuilder: (_, index) {
                                  return const SizedBox(height: 20);
                                },
                                itemCount: list.length,
                              ),
                              PaginationFooter(
                                currentPage: state.currentPage,
                                totalPages: totalPages,
                                onPageChanged: (int page) {
                                  _getData(page: page, ordering: state.ordering);
                                },
                              ),
                            ],
                          ),
                        );
                      }

                      return const LoadingWidget();
                    }),
                  ),
                ],
              ));
        });
  }
}
