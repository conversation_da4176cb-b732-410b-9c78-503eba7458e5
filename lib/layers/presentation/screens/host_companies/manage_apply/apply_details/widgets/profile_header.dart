import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/apply_details/index.dart';
import 'package:asiantech_link/layers/presentation/blocs/general/notify/notify_cubit.dart';
import 'package:asiantech_link/layers/presentation/blocs/general/notify/notify_state.dart';

import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/common_dialog.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:asiantech_link/layers/presentation/utils/log.dart';
import 'package:asiantech_link/layers/presentation/widgets/cached_image.dart';
import 'package:asiantech_link/layers/presentation/widgets/country_flag_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/routes/router.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:openapi/openapi.dart';
import 'package:video_player/video_player.dart';

class ProfileHeader extends StatefulWidget {
  final UserExploreDetailsSerializers user;
  final Function() onMenuTap;
  const ProfileHeader({super.key, required this.user, required this.onMenuTap});

  @override
  State<ProfileHeader> createState() => _ProfileHeaderState();
}

class _ProfileHeaderState extends State<ProfileHeader> {
  late VideoPlayerController _controller;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _init();
  }

  _init() {
    try {
      _controller = VideoPlayerController.networkUrl(
          Uri.parse('${widget.user.selfIntroductionUrl}'))
        ..initialize().then((_) {
          // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
          setState(() {});
        });
      _controller.addListener(_listener);
    } catch (e) {
      log(e.toString());
    }
  }

  _listener() {
    if (_controller.value.isPlaying != _isPlaying) {
      setState(() {
        _isPlaying = _controller.value.isPlaying;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ApplyDetailsBloc, ApplyDetailsState>(
        builder: (context, state) {
      var user = (state as InApplyDetailsState).applyDetails.engineer;
      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                SizedBox(
                  height: 360,
                  child: Stack(
                    children: [
                      GestureDetectorCustom(
                        onTap: () async {
                          if (_controller.value.isPlaying) {
                            await _controller.pause();
                          } else {
                            await _controller.play();
                          }
                        },
                        child: SizedBox(
                          height: 218,
                          child: Stack(
                            children: [
                              Center(
                                child: Builder(builder: (_) {
                                  if (_controller.value.isInitialized) {
                                    return SizedBox(
                                      height: 218,
                                      child: AspectRatio(
                                        aspectRatio:
                                            _controller.value.aspectRatio,
                                        child: VideoPlayer(_controller),
                                      ),
                                    );
                                  }
                                  return Container();
                                }),
                              ),
                              Container(
                                height: 218,
                                width: getWidth(context),
                                color: Colors.black.withValues(alpha: 0.2),
                                child: !_isPlaying
                                    ? Center(
                                        child: SvgPicture.asset(
                                            Assets.svg.playIconSVG))
                                    : null,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Container(
                        height: 72,
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                              onPressed: () {
                                pop(context);
                              },
                              icon: const Icon(
                                Icons.arrow_back,
                                color: Colors.white,
                              ),
                            ),
                            Row(
                              children: [
                                IconButton(
                                  onPressed: () {
                                    CommonDialog.showListNotification(
                                        context: context,
                                        primaryColor: AppColors.primary);
                                  },
                                  icon: Container(
                                    height: 35,
                                    width: 38,
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.white,
                                    ),
                                    child: Stack(
                                      children: [
                                        Center(
                                            child: SvgPicture.asset(
                                                Assets.svg.alertIconSVG)),
                                        BlocBuilder<NotifyCubit, NotifyState>(
                                          builder: (context, state) {
                                            var countUnreadNotify =
                                                state.countUnreadNotify;
                                            if (countUnreadNotify == 0) {
                                              return const SizedBox.shrink();
                                            }
                                            return Align(
                                              alignment: Alignment.topRight,
                                              child: Container(
                                                height: 16,
                                                width: 16,
                                                decoration: const BoxDecoration(
                                                  color: AppColors.red,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    countUnreadNotify
                                                        .toString(),
                                                    style:
                                                        AppTextStyles.bodySmall(
                                                      color: AppColors.white,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                IconButton(
                                  onPressed: widget.onMenuTap,
                                  icon: Container(
                                      width: 32,
                                      height: 32,
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.white,
                                      ),
                                      child: Center(
                                          child: SvgPicture.asset(
                                              Assets.svg.menu2IconSVG))),
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: 140,
                  child: SizedBox(
                    width: getWidth(context),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                width: 155,
                                height: 155,
                                alignment: Alignment.center,
                                decoration: const BoxDecoration(
                                  color: AppColors.white,
                                  shape: BoxShape.circle,
                                ),
                                child: CacheImageNetworkWidget(
                                  imageUrl: "${user?.profileImagePath}",
                                  width: 150,
                                  height: 150,
                                  borderRadius: 1000,
                                ),
                              ),
                              GestureDetectorCustom(
                                onTap: () {
                                  BlocProvider.of<ApplyDetailsBloc>(context)
                                      .add(
                                    UpdateFavoriteUserEvent(
                                      isFavorite: user?.interestedFlag == 1
                                          ? false
                                          : true,
                                      userId: int.parse("${user?.userId}"),
                                    ),
                                  );
                                  setState(() {});
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 10, horizontal: 20),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(100),
                                    color: AppColors.grey2,
                                  ),
                                  child: Row(
                                    children: [
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(right: 8),
                                        child: SvgPicture.asset(
                                          Assets.svg.heartFillSVG,
                                          width: 18,
                                          colorFilter: ColorFilter.mode(
                                              user?.interestedFlag == 1
                                                  ? AppColors.red
                                                  : AppColors.brown,
                                              BlendMode.srcIn),
                                        ),
                                      ),
                                      Text(
                                        user?.interestedFlag == 1
                                            ? AppText.value(context).liked
                                            : AppText.value(context).like,
                                        style: AppTextStyles.paragraph02v1(),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Flexible(
                                    child: Text(
                                      Helper.getFullName(
                                        context: context,
                                        firstName: user?.firstName,
                                        lastName: user?.lastName,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: AppTextStyles.heading03v1(),
                                    ),
                                  ),
                                  if (user?.nickname != null &&
                                      (user?.nickname!.isNotEmpty ?? false))
                                    Padding(
                                      padding: const EdgeInsets.only(left: 8),
                                      child: Text(
                                        "(${user?.nickname})",
                                        style: AppTextStyles.paragraph02v3(),
                                      ),
                                    ),
                                ],
                              ),
                              Row(
                                children: [
                                  if (user?.experiences?.isNotEmpty ?? false)
                                    Padding(
                                      padding: const EdgeInsets.only(right: 8),
                                      child: Builder(builder: (context) {
                                        String value =
                                            AppText.value(context).not_set;
                                        var jobs = user?.experiences!
                                            .map((e) =>
                                                e.careerJobSkills?.toList() ??
                                                [])
                                            .reduce((a, b) => a + b);
                                        if (jobs?.isNotEmpty ?? false) {
                                          value = Helper.getJobCodeNameByList(
                                              context,
                                              jobs
                                                      ?.map((e) =>
                                                          e.jobCode ?? "")
                                                      .toList() ??
                                                  []);
                                        }
                                        return Text(
                                          value,
                                          style: AppTextStyles.paragraph03(
                                            color: AppColors.grey1,
                                          ),
                                        );
                                      }),
                                    ),
                                  if (user?.countryCode != null)
                                    CountryFlagWidget(
                                      countryCode: user?.countryCode ?? "",
                                    )
                                  else
                                    Container(
                                      margin: const EdgeInsets.only(left: 10),
                                      padding: const EdgeInsets.all(2),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                            color: AppColors.grey1, width: 0.5),
                                      ),
                                      child: Text(
                                        AppText.value(context).not_set,
                                        style: AppTextStyles.bodySmall(),
                                      ),
                                    ),
                                ],
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
