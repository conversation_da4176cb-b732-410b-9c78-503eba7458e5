import 'package:asiantech_link/layers/presentation/blocs/companies/user/explore_user/index.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';
import 'package:asiantech_link/layers/presentation/resources/app_constants.dart';
import 'package:asiantech_link/layers/presentation/resources/app_text_styles.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/common_dialog.dart';
import 'package:asiantech_link/layers/presentation/utils/helper.dart';
import 'package:asiantech_link/layers/presentation/widgets/button/function_button/primary_button.dart';
import 'package:asiantech_link/layers/presentation/widgets/cached_image.dart';
import 'package:asiantech_link/layers/presentation/widgets/dialog/ai_recommendation_dialog.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/skills_display_widget.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:openapi/openapi.dart';

class UserItemExplore extends StatelessWidget {
  final GeneralCompanyExploreUser model;
  final int index;
  final Color? primaryColor;
  final int userType;
  final VoidCallback onViewDetails;

  const UserItemExplore(
      {super.key,
      required this.model,
      required this.index,
      required this.onViewDetails,
      this.primaryColor,
      required this.userType});

  /// Check if the status should show a white border (for declined/withdrawn statuses)
  bool _shouldShowWhiteBorder(int? statusCode) {
    return statusCode != null && [
      RecruitProgressCodeType.notPassed,
      RecruitProgressCodeType.applicationWithdrawn,
      RecruitProgressCodeType.interviewWithdrawn,
      RecruitProgressCodeType.offerDeclined,
      RecruitProgressCodeType.otherCompanyOffer,
    ].contains(statusCode);
  }    

  @override
  Widget build(BuildContext context) {
    final titlesInfo = [
      AppText.value(context).residence,
      AppText.value(context).employment_status_company,
      AppText.value(context).recent_occupation,
      AppText.value(context).highestEducation,
      AppText.value(context).language,
      AppText.value(context).qualification,
      AppText.value(context).work_history,
    ];
    final titleSkills = [
      AppText.value(context).job_position,
      AppText.value(context).employmentType,
      AppText.value(context).annual_salary,
      AppText.value(context).work_place,
    ];
    final titleWidthInfo = _calculateMaxTitleWidth(context, titlesInfo);
    final titleWidthSkill = _calculateMaxTitleWidth(context, titleSkills);
    final String employmentStatusCompany = Helper.getRecruitProgressCodeName(
        context: context, code: model.currentRecruitProgressCodeWithMyCompany);
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 50,
            decoration: const BoxDecoration(
              color: AppColors.iconColor,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(5), topRight: Radius.circular(5)),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                RichText(
                  text: TextSpan(
                      style: AppTextStyles.paragraph02v1(
                          color: AppColors.white, fontWeight: FontWeight.w500),
                      children: [
                        TextSpan(
                          style: AppTextStyles.paragraph01(
                              color: AppColors.white,
                              fontWeight: FontWeight.w500),
                          text:
                              '${getFullNameAndNickName(context: context, firstName: model.firstName, lastName: model.lastName, nickName: model.nickname)}\t',
                        ),
                        TextSpan(
                          text: Helper.getEngineerHightLight(
                              context: context,
                              countryCode: model.countryCode ?? '',
                              sexType: model.sexType,
                              age: model.age,
                              jobChangeCount: model.totalCompanyChangedCount),
                        ),
                      ]),
                ),
                if (model.currentRecruitProgressCodeWithMyCompany != null)
                  Container(
                    alignment: Alignment.center,
                    padding:
                        const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: Helper.getRecruitProgressCodeColor(
                        code: model.currentRecruitProgressCodeWithMyCompany,
                      ),
                      borderRadius: BorderRadius.circular(24),
                      border: _shouldShowWhiteBorder(model.currentRecruitProgressCodeWithMyCompany)
                          ? Border.all(color: AppColors.white, width: 1)
                          : null,
                    ),
                    child: Text(
                      Helper.getRecruitProgressCodeName(
                          context: context,
                          code: model.currentRecruitProgressCodeWithMyCompany),
                      textAlign: TextAlign.center,
                      style: AppTextStyles.textStyle14(context,
                              color: AppColors.whiteText,
                              fontWeight: FontWeight.bold)
                          .copyWith(height: 1.5),
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 40),
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 16,
                children: [
                  Container(
                    width: 150,
                    margin: const EdgeInsets.only(right: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        CacheImageNetworkWidget(
                          imageUrl: "${model.profileImagePath}",
                          width: 150,
                          height: 150,
                          boxFit: BoxFit.cover,
                          borderRadius: 8,
                        ),
                        _buildDateRow(AppText.value(context).registration_date,
                            model.created),
                        _buildDateRow(
                            AppText.value(context).updated_at, model.updated),
                        if (model.reasonAiRecommend?.isNotEmpty ?? false)
                          Builder(builder: (context) {
                            var summary = model.reasonAiRecommend;
                            var summaryJa = model.reasonAiRecommendJa;
                            var summaryVi = model.reasonAiRecommendVi;
                            if (AppText.value(context).localeName == "ja") {
                              summary = summaryJa;
                            } else if (AppText.value(context).localeName ==
                                "vi") {
                              summary = summaryVi;
                            }
                            return UserItemExploreAiRecommendation(
                                summary: summary ?? "");
                          }),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 4,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            AppText.value(context).profile,
                            style: AppTextStyles.textStyle16(
                              context,
                              color: AppColors.hostCompanyPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        if (model.addressCode != null)
                          _buildInformationRow(
                              AppText.value(context).residence,
                              Helper.getStateAndCountryName(
                                  context: context, code: model.addressCode),
                              titleWidthInfo),
                        _buildInformationRow(
                            AppText.value(context).employment_status_company,
                            employmentStatusCompany.isEmpty
                                ? AppText.value(context).not_set
                                : employmentStatusCompany,
                            titleWidthInfo),
                        _buildInformationRow(
                            AppText.value(context).recent_occupation,
                            Helper.getJobCodeName(context,
                                model.jobCodeHasWorkedRecently.toString()),
                            titleWidthInfo),
                        if (model.lastAcademicCode != null)
                          _buildInformationRow(
                              AppText.value(context).highestEducation,
                              Helper.getAcademicLevelName(
                                  context: context,
                                  id: int.tryParse(
                                      model.lastAcademicCode ?? '')),
                              titleWidthInfo),
                        _buildInformationRow(
                            AppText.value(context).language,
                            Helper.getLanguageNameByList(
                                context: context,
                                languageCodes:
                                    Helper.getLanguageNameByListModel(
                                        model.languages)),
                            titleWidthInfo),
                        _buildInformationRow(
                            AppText.value(context).qualification,
                            Helper.parseQualificationsToString(
                                context: context,
                                qualifications: model.qualifications),
                            titleWidthInfo),
                        if (Helper.buildWorkHistoryRange(
                                context, model.workHistories?.toList() ?? [])
                            .isNotEmpty)
                          _buildInformationRow(
                              AppText.value(context).work_history,
                              Helper.buildWorkHistoryRange(
                                  context, model.workHistories?.toList() ?? []),
                              titleWidthInfo),
                        Padding(
                          padding: const EdgeInsets.only(top: 12.0),
                          child: Row(
                            spacing: 16,
                            children: [
                              _buildInformationBox(
                                  AppText.value(context).last_login,
                                  Helper.dateTimeToString(model.lastLogin,
                                      format: 'yyyy/MM/dd')),
                              _buildInformationBox(
                                  AppText.value(context).under_selection_count,
                                  model.totalApplyCount?.toString())
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  if ((model.skills != null && model.skills!.isNotEmpty) ||
                      model.requirements != null)
                    const VerticalDivider(
                      width: 10,
                      thickness: 1,
                      color: AppColors.lineColor,
                    ),
                  Expanded(
                      child: Column(
                    spacing: 16,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (model.skills != null && model.skills!.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: Text(
                                AppText.value(context).development_skills,
                                style: AppTextStyles.textStyle16(
                                  context,
                                  color: AppColors.hostCompanyPrimary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            SkillsDisplayWidget(
                              skills: model.skills!.toList(),
                              primaryColor: primaryColor,
                            ),
                          ],
                        ),
                      if (model.requirements != null)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 4,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 4.0),
                              child: Text(
                                AppText.value(context).desired_conditions,
                                style: AppTextStyles.textStyle16(
                                  context,
                                  color: AppColors.hostCompanyPrimary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            if (model.requirements?.jobSkills != null)
                              _buildInformationRow(
                                  AppText.value(context).job_position,
                                  Helper.extractJobCodeNames(
                                      context: context,
                                      jobSkills: model.requirements?.jobSkills),
                                  titleWidthSkill),
                            if (model.requirements?.employCode != null)
                              _buildInformationRow(
                                  AppText.value(context).employmentType,
                                  Helper.getEmployCodeName(
                                      context: context,
                                      code: model.requirements?.employCode),
                                  titleWidthSkill),
                            if (model.requirements?.payrollPrice != null)
                              _buildInformationRow(
                                  AppText.value(context).annual_salary,
                                  [
                                    model.requirements?.payrollCode == 'JPN'
                                        ? Helper.formatYenInMan(
                                            model.requirements?.payrollPrice ??
                                                '')
                                        : Helper.formatMoney(
                                            model.requirements?.payrollPrice ??
                                                ''),
                                    Helper.getCurrencyName(
                                        context: context,
                                        currencyCode:
                                            model.requirements?.payrollCode ??
                                                '')
                                  ].join(' '),
                                  titleWidthSkill),
                            _buildInformationRow(
                                AppText.value(context).work_place,
                                Helper.getStatesAndCountries(
                                    context: context,
                                    codes: [
                                      model.requirements?.placeCode1,
                                      model.requirements?.placeCode2,
                                      model.requirements?.placeCode3
                                    ]),
                                titleWidthSkill),
                          ],
                        ),
                    ],
                  ))
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Row(
              spacing: 15,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Builder(builder: (context) {
                  Color color = AppColors.iconColor;
                  String title = AppText.value(context).add_to_favorites;
                  IconData icon = Icons.favorite_border_rounded;
                  if (model.interestedFlag == 1) {
                    color = AppColors.red;
                    icon = Icons.favorite_rounded;
                    title = AppText.value(context).remove_from_favorites;
                  }

                  return GestureDetectorCustom(
                    onTap: model.interestedFlag != 2
                        ? () {
                            if (isAuthenticated(userType: userType)) {
                              BlocProvider.of<ExploreUserCubit>(context).updateFavoriteUser(
                                      index: index,
                                      isFavorite:
                                          !(model.interestedFlag == 1));
                            } else {
                              CommonDialog.showErrorDialog(
                                  context: context,
                                  title: AppText.value(context).login_failed,
                                  message: AppText.value(context)
                                      .please_login_to_continue);
                            }
                          }
                        : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 65),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                            color: AppColors.iconColor,
                            strokeAlign: BorderSide.strokeAlignInside),
                      ),
                      child: Row(
                        spacing: 4,
                        children: [
                          Icon(
                            icon,
                            color: color,
                          ),
                          Text(
                            title,
                            style: AppTextStyles.paragraph02v3(
                                color: AppColors.iconColor),
                          ),
                        ],
                      ),
                    ),
                  );
                }),
                PrimaryButton(
                  height: 46,
                  padding: const EdgeInsets.symmetric(horizontal: 65),
                  onPressed: onViewDetails,
                  buttonText: AppText.value(context).view_details,
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 24,
          )
        ],
      ),
    );
  }

  Widget _buildDateRow(String title, DateTime? des) {
    if (des == null) return const SizedBox();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Container(
              alignment: Alignment.centerLeft,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Text(
                  title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.bodySmall(
                    fontWeight: FontWeight.w700,
                    color: AppColors.iconColor,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          Flexible(
            child: Container(
              alignment: Alignment.centerRight,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerRight,
                child: Text(
                  Helper.dateTimeToString(des, format: 'yyyy/MM/dd'),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.paragraph02v2(
                    color: AppColors.hoverPrimaryColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInformationRow(String title, String? des, double titleWidth) {
    if (des == null) return const SizedBox();
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: titleWidth,
          child: Text(
            title,
            style: AppTextStyles.paragraph02v2(color: AppColors.iconColor),
          ),
        ),
        Container(
          padding: const EdgeInsets.only(left: 7.0),
          width: 17,
          child: Text(
            ':',
            style: AppTextStyles.paragraph02v2(color: AppColors.iconColor),
          ),
        ),
        Expanded(
          flex: 5,
          child: Text(
            des,
            style:
                AppTextStyles.paragraph02v2(color: AppColors.hoverPrimaryColor),
          ),
        ),
      ],
    );
  }

  Widget _buildInformationBox(String tittle, String? des) {
    if (des == null) return const SizedBox();
    return Row(
      spacing: 8,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 7),
          decoration: BoxDecoration(
              color: AppColors.hostCompanyBackgroundColor,
              borderRadius: BorderRadius.circular(5)),
          child: Text(
            tittle,
            style: AppTextStyles.bodySmall(
                color: AppColors.iconColor, fontWeight: FontWeight.w700),
          ),
        ),
        Text(
          des,
          style:
              AppTextStyles.paragraph02v2(color: AppColors.hoverPrimaryColor),
        ),
      ],
    );
  }

  double _calculateMaxTitleWidth(BuildContext context, List<String> titles) {
    final style = AppTextStyles.paragraph02v2(color: AppColors.iconColor);
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      maxLines: 1,
    );

    double maxWidth = 0;
    for (final title in titles) {
      textPainter.text = TextSpan(text: title, style: style);
      textPainter.layout();
      if (textPainter.width > maxWidth) {
        maxWidth = textPainter.width;
      }
    }
    return maxWidth;
  }
}


class UserItemExploreAiRecommendation extends StatelessWidget {
  final String summary;
  const UserItemExploreAiRecommendation({super.key, required this.summary});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 150,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5),
        child: GestureDetectorCustom(
          onTap: () {
            AiRecommendationDialog.show(
              context,
              summary: summary,
              title: AppText.value(context).see_ai_recommendation,
            );
          },
          child: Row(
            spacing: 8,
            children: [
              Icon(
                Icons.auto_awesome,
                color: AppColors.primary,
                size: 20,
              ),
              Expanded(
                child: Text(
                  AppText.value(context).see_ai_recommendation,
                  style: AppTextStyles.textStyle14(context,
                      fontWeight: FontWeight.w700, color: AppColors.primary),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
