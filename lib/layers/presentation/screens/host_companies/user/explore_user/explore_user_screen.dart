import 'dart:async';

import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/domain/entity/skill_code_model.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/create_recruit/create_recruit_bloc.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/recruit/create_recruit/create_recruit_event.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/status_engineer/index.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/user/explore_user/index.dart';
import 'package:asiantech_link/layers/presentation/resources/config_export.dart';
import 'package:asiantech_link/layers/presentation/resources/enum.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/apply_details/apply_details_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/manage_apply/manage_apply_screen.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/user/explore_user/widgets/filter_button_widget.dart';
import 'package:asiantech_link/layers/presentation/screens/host_companies/user/explore_user/widgets/filter_dialog.dart';
import 'package:asiantech_link/layers/presentation/screens/general/user_details/user_details_screen.dart';

import 'package:asiantech_link/layers/presentation/screens/host_companies/user/explore_user/widgets/user_item_explore.dart';
import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:asiantech_link/layers/presentation/widgets/dialog/ai_recommendation_dialog.dart';
import 'package:asiantech_link/layers/presentation/widgets/dialog/recruit_selection_dialog.dart';
import 'package:asiantech_link/layers/presentation/widgets/empty_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/gesture_detector_custom.dart';
import 'package:asiantech_link/layers/presentation/widgets/loading/ai_loading_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/loading_widget.dart';
import 'package:asiantech_link/layers/presentation/widgets/scaffold/app_secondary_scaffold.dart';
import 'package:asiantech_link/layers/presentation/widgets/svg_icon.dart';
import 'package:asiantech_link/layers/presentation/widgets/pagination_sort_header.dart';
import 'package:asiantech_link/layers/presentation/widgets/pagination_footer.dart';

import 'package:asiantech_link/routes/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import 'package:openapi/openapi.dart';

import '../../../../widgets/error_app_widget.dart';

class ExploreUserScreen extends StatefulWidget {
  final int userType;
  final bool showBackIcon;
  static const String routeName = 'explore-user';
  final SkillCode? skill;
  final String? ordering;
  final String? searchType;
  final String? recruitId;
  final Map<String, dynamic> queryParams;
  const ExploreUserScreen(
      {super.key,
      this.skill,
      required this.userType,
      this.queryParams = const {},
      this.ordering = '',
      this.showBackIcon = false,
      this.searchType,
      this.recruitId});

  @override
  State<ExploreUserScreen> createState() => _ExploreUserScreenState();
}

class _ExploreUserScreenState extends State<ExploreUserScreen> {
  final ExploreUserCubit _exploreUserCubit = ExploreUserCubit();
  final EditRecruitBloc _editRecruitBloc = EditRecruitBloc();
  bool showFavorite = false;
  bool _isInitialized = false;
  RecruitUploadedSerializers? selectedRecruitForAI;

  @override
  void initState() {
    showFavorite = widget.queryParams["showFavorite"] == "true";
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      _init();
      _isInitialized = true;
    }
  }

  _init() async {
    if (widget.skill != null) {
      try {
        var skill = widget.skill;
        var jobCode = skill?.id?.substring(0, 3);
        var dataFilter = {
          "experiencedJobCode1": jobCode,
          "skillCode1_1": skill?.id,
        };
        _exploreUserCubit.updateFilters(
          filters: dataFilter,
          onDone: () {
            _getData();
          },
        );
      } catch (_) {
        _getData();
      }
    } else if (widget.ordering?.isNotEmpty == true) {
      _exploreUserCubit.updateFilter(
        key: 'ordering',
        value: widget.ordering,
        onDone: () {
          _getData();
        },
      );
    } else {
      _exploreUserCubit.getFilterFromLocalStorage(
        onDone: (filters) async {
          if (filters != null) {
            filters['showFavorite'] = showFavorite.toString();
            filters['selectedSearchMode'] = SearchType.all.name;
            if (widget.searchType ==
                SearchType.aiRecommended.valueRequestType) {
              filters['selectedSearchMode'] = widget.searchType;
              if (widget.recruitId == null || widget.recruitId == "") {
                await Future.delayed(const Duration(milliseconds: 300));
                if (mounted) {
                  _updateSearchType(context,
                      searchType: SearchType.aiRecommended);
                }
              } else {
                _exploreUserCubit.updateSearchType(
                  searchType: SearchType.aiRecommended,
                  onDone: () {},
                );
                _editRecruitBloc.add(GetMyRecruitDetails(
                  recruitId: widget.recruitId ?? "",
                  onSuccess: (recruit) {
                    selectedRecruitForAI = RecruitUploadedSerializers((b) {
                      b.title = recruit.title;
                      b.catchCopy = recruit.catchCopy;
                      b.recruitId = recruit.recruitId;
                      b.hostCompany = 0;
                      b.hostAgent = 0;
                      // b.companyUserId = 0;
                      // b.totalApply = 0;
                      // b.jobName = "";
                      // b.applied = false;
                      // b.recruitImagePath = "";
                      // b.isExpired = false;
                    });
                    setState(() {});
                    _getData();
                  },
                  onError: (error) {},
                ));
              }
            } else {
              _exploreUserCubit.updateFilters(
                filters: filters,
                onDone: () {
                  _getData();
                },
              );
            }
          } else {
            _exploreUserCubit.updateFilters(
              filters: {
                'showFavorite': showFavorite.toString(),
              },
              onDone: () {
                _getData();
              },
            );
          }
        },
      );
    }
  }

  void _updateSearchType(
    BuildContext context, {
    SearchType searchType = SearchType.all,
  }) {
    if (searchType == SearchType.aiRecommended) {
      _showRecruitSelectionDialog(context);
      return;
    }
    _exploreUserCubit.updateSearchType(
      searchType: searchType,
      onDone: () {
        _getData();
      },
    );
  }

  Future _getData({bool isResetPage = false}) {
    final Completer<void> completer = Completer();
    _exploreUserCubit.loadExploreUser(
        context: context,
        isResetPage: isResetPage,
        recruitId: selectedRecruitForAI?.recruitId.toString(),
        onDone: () {
          completer.complete();
          _exploreUserCubit.updateFilterToLocalStorage();
        });
    return completer.future;
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _showRecruitSelectionDialog(BuildContext context) async {
    // Show dialog - dialog tự quản lý việc load data
    final selectedRecruit = await showDialog(
        context: context,
        builder: (_) {
          return const RecruitSelectionDialog();
        });

    if (selectedRecruit != null) {
      // Save selected recruit for display
      setState(() {
        selectedRecruitForAI = selectedRecruit;
      });

      // // User selected a recruit, now update search type and call API with recruitId
      _exploreUserCubit.updateSearchType(
        searchType: SearchType.aiRecommended,
        onDone: () {
          _getData();
        },
      );
    }
  }

  Widget _buildSelectedRecruitInfo() {
    if (selectedRecruitForAI == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.psychology,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "AI Recommendations for:",
                      style: AppTextStyles.paragraph02v1().copyWith(
                        color: AppColors.iconColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      selectedRecruitForAI?.title ??
                          AppText.value(context).not_set,
                      style: AppTextStyles.paragraph01().copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              GestureDetectorCustom(
                onTap: () {
                  _showRecruitSelectionDialog(context);
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    AppText.value(context).change,
                    style: AppTextStyles.paragraph02v1().copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
          if (selectedRecruitForAI?.catchCopy?.isNotEmpty == true) ...[
            const SizedBox(height: 8),
            Text(
              selectedRecruitForAI?.catchCopy ?? "",
              style: AppTextStyles.paragraph02v1().copyWith(
                color: AppColors.iconColor,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<StatusEngineerBloc, StatusEngineerState>(
          listenWhen: (previous, current) {
            return previous.dateTime != current.dateTime;
          },
          listener: (context, state) {
            _exploreUserCubit.updateFavoriteUserState(
                isFavorite: state.liked ?? false,
                userId: int.parse("${state.userId}"));
          },
        ),
      ],
      child: BlocProvider(
        create: (_) => _exploreUserCubit,
        child: BlocBuilder<ExploreUserCubit, ExploreUserState>(
            builder: (context, state) {
          return AppSecondaryScaffold(
            showBackIcon: widget.showBackIcon,
            userType: widget.userType,
            child: Column(
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(right: 10),
                        child: FilterButtonWidget(
                          onPressed: () async {
                            var oldState = state;
                            final result = await showDialog(
                                context: context,
                                builder: (_) {
                                  return BlocProvider.value(
                                    value: _exploreUserCubit,
                                    child: const FilterUserDialog(),
                                  );
                                });
                            if (result == true) {
                              _getData(isResetPage: true);
                            } else {
                              if (context.mounted &&
                                  state.exploreUser.results != null) {
                                BlocProvider.of<ExploreUserCubit>(context)
                                    .updateState(newState: oldState);
                              }
                            }
                          },
                        ),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: SearchType.values
                                    .where((mode) =>
                                        mode != state.selectedSearchType)
                                    .map((mode) {
                                  return Row(
                                    children: [
                                      _buildSelectableText(context, mode,
                                          state.selectedSearchType, () {
                                        // Change search mode
                                        _updateSearchType(context,
                                            searchType: mode);
                                      }),
                                      if (mode !=
                                          SearchType.values
                                              .where((m) =>
                                                  m != state.selectedSearchType)
                                              .last)
                                        const SizedBox(width: 15),
                                    ],
                                  );
                                }).toList(),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Builder(builder: (context) {
                  var currentFilters = state.toMap();

                  // Define filter categories and their associated filters
                  final filterCategories = {
                    0: {
                      'name': AppText.value(context).profile,
                      'filters': [
                        "ageFrom",
                        "ageTo",
                        "sexType",
                        "countryCode",
                        "addressCode",
                        "addressCode1",
                        "addressCode2",
                        "lastAcademicCode",
                      ]
                    },
                    1: {
                      'name': AppText.value(context).career_skills,
                      'filters': [
                        "experiencedJobCode1",
                        "yearsOfExperience1",
                        "experiencedJobCode2",
                        "yearsOfExperience2",
                        "experiencedJobCode3",
                        "yearsOfExperience3",
                        "languageCode1",
                        "languageLevelType1",
                        "languageCode2",
                        "languageLevelType2",
                        "licenceCode1",
                        "licencePoint1",
                        "licenceCode2",
                        "licencePoint2",
                        "licenceCode3",
                        "licencePoint3",
                      ]
                    },
                    2: {
                      'name': AppText.value(context).other_skills,
                      'filters': [
                        "remoteWorkSkillPointType",
                        "globalSkillPointType",
                        "communicationSkillPointType",
                        "horensoSkillPointType",
                        "projectManagementSkillPointType",
                        "globalWorkExperience"
                      ]
                    },
                    3: {
                      'name': AppText.value(context).work_conditions,
                      'filters': [
                        "recruitingJobCode",
                        "recruitingEmployCode",
                        "workPlaceCode1",
                        "workPlaceCode2",
                        "workPlaceCode3",
                        "payrollCode",
                        "payrollPriceFrom",
                        "payrollPriceTo",
                      ]
                    },
                    4: {
                      'name': AppText.value(context).transactionConditions,
                      'filters': ["agentFee", "agentFeeCurrCode"]
                    },
                    5: {
                      'name': state.searchQuery,
                      'filters': ["searchQuery"]
                    }
                  };

                  // Function to check if filters exist
                  bool filtersExist(List<String> filters) {
                    return filters.any((filter) =>
                        currentFilters[filter] != null &&
                        currentFilters[filter]?.isNotEmpty == true);
                  }

                  // Check if any filter category has active filters
                  bool anyFiltersActive = filterCategories.values.any(
                      (category) =>
                          filtersExist(category['filters'] as List<String>));

                  // Only show the filter section if there are active filters or _showFavorite is true
                  bool showFav =
                      bool.tryParse(state.showFavorite ?? "false") ?? false;
                  if (!anyFiltersActive && !showFav) {
                    return const SizedBox();
                  }

                  // Function to clear filters for a given category
                  void clearFilters(int categoryIndex) {
                    var filters = filterCategories[categoryIndex]!['filters']
                        as List<String>;
                    Map<String, dynamic> dataFilter = {
                      for (var filter in filters) filter: null
                    };

                    BlocProvider.of<ExploreUserCubit>(context).updateFilters(
                      filters: dataFilter,
                      onDone: () {
                        _getData();
                      },
                    );
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 10),
                    child: Row(
                      children: [
                        Text('${AppText.value(context).filter}: ',
                            style: AppTextStyles.paragraph02v1(
                                color: AppColors.iconColor,
                                fontWeight: FontWeight.w500)),
                        Expanded(
                          child: SizedBox(
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: [
                                  if (bool.tryParse(
                                          state.showFavorite ?? "false") ??
                                      false)
                                    Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 5),
                                      height: 37,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 5),
                                      decoration: BoxDecoration(
                                        color: AppColors.white,
                                        borderRadius: BorderRadius.circular(5),
                                        border: Border.all(
                                            color: AppColors.lineColor),
                                      ),
                                      child: Row(
                                        children: [
                                          Padding(
                                            padding:
                                                const EdgeInsets.only(right: 5),
                                            child: Text(
                                              AppText.value(context)
                                                  .under_consideration,
                                              style:
                                                  AppTextStyles.paragraph02v1(
                                                      color: AppColors.black,
                                                      fontWeight:
                                                          FontWeight.w500),
                                            ),
                                          ),
                                          GestureDetectorCustom(
                                            onTap: () {
                                              context.push(
                                                  "${getParentRoute(context)}/${ExploreUserScreen.routeName}");
                                            },
                                            child: const Icon(
                                              Icons.close,
                                              size: 18,
                                              color: AppColors.black,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  Row(
                                    children: filterCategories.keys
                                        .map((categoryIndex) {
                                      var category =
                                          filterCategories[categoryIndex];
                                      var categoryFilters =
                                          category!['filters'] as List<String>;

                                      if (!filtersExist(categoryFilters)) {
                                        return Container();
                                      }

                                      final categoryName =
                                          category['name'] as String;

                                      return categoryName.isNotEmpty
                                          ? Container(
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 5),
                                              height: 37,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 5),
                                              decoration: BoxDecoration(
                                                color: AppColors.white,
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                border: Border.all(
                                                    color: AppColors.lineColor),
                                              ),
                                              child: Row(
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 5),
                                                    child: Text(
                                                      categoryName,
                                                      style: AppTextStyles
                                                          .paragraph02v1(
                                                              color: AppColors
                                                                  .black,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500),
                                                    ),
                                                  ),
                                                  GestureDetectorCustom(
                                                    onTap: () => clearFilters(
                                                        categoryIndex),
                                                    child: const Icon(
                                                      Icons.close,
                                                      size: 18,
                                                      color: AppColors.black,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            )
                                          : const SizedBox.shrink();
                                    }).toList(),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
                if (state.selectedSearchType == SearchType.aiRecommended &&
                    selectedRecruitForAI != null)
                  _buildSelectedRecruitInfo(),
                _buildSelectableText(context, state.selectedSearchType,
                    state.selectedSearchType, () {}),
                PaginationSortHeader.forEngineers(
                  context: context,
                  totalCount: state.totalCountItemsPage,
                  currentPage: state.page,
                  selectedOrdering: OrderingOption.values.firstWhere(
                    (option) =>
                        option.valueRequestType == state.toMap()['ordering'],
                    orElse: () => OrderingOption.newest,
                  ),
                  onOrderingChanged: (OrderingOption? newValue) {
                    if (newValue == null) return;
                    BlocProvider.of<ExploreUserCubit>(context).updateFilter(
                      key: 'ordering',
                      value: newValue.valueRequestType,
                      onDone: () => _getData(),
                    );
                  },
                ),
                Expanded(
                  child: Builder(builder: (context) {
                    if (state.isLoading) {
                      // Show AI loading animation for AI recommended search type
                      if (state.selectedSearchType ==
                          SearchType.aiRecommended) {
                        return AiLoadingWidget(
                          message: AppText.value(context)
                              .ai_finding_the_best_matches,
                        );
                      }

                      return const LoadingWidget();
                    }
                    if (state.errorMessage != null) {
                      return ErrorAppWidget(
                        onPressed: () {
                          _init();
                        },
                      );
                    }
                    if (state.exploreUser.results != null) {
                      var list = state.exploreUser.results ?? [];
                      if (list.isEmpty) {
                        return const Center(child: EmptyWidget());
                      }

                      // Calculate pagination data
                      const int itemsPerPage = 20;
                      final int totalCount = state.totalCountItemsPage;
                      final int totalPages = (totalCount / itemsPerPage).ceil();
                      final bool isLastPage = state.page == totalPages;

                      final int itemCount = isLastPage
                          ? totalCount - (totalPages - 1) * itemsPerPage
                          : itemsPerPage;
                      return SingleChildScrollView(
                        child: Column(
                          children: [
                            ListView.separated(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 20),
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (_, index) {
                                var model =
                                    list[index] as GeneralCompanyExploreUser;
                                return UserItemExplore(
                                  userType: widget.userType,
                                  index: index,
                                  model: model,
                                  onViewDetails: () async {
                                    if (model.existApplyId != null) {
                                      await context.push(
                                          "${getParentRoute(context)}/${ManageApplyScreen.routeName}/${ApplyDetailsScreen.routeName}?id=${model.existApplyId}");
                                    } else {
                                      await context.push(
                                          "${getParentRoute(context)}/${ExploreUserScreen.routeName}/${UserDetailsScreen.routeName}?id=${model.userId}");
                                    }
                                  },
                                );
                              },
                              separatorBuilder: (context, index) {
                                return const SizedBox(height: 10);
                              },
                              itemCount: itemCount,
                            ),
                            PaginationFooter(
                              currentPage: state.page,
                              totalPages: totalPages,
                              onPageChanged: (int page) {
                                BlocProvider.of<ExploreUserCubit>(context)
                                    .updateFilter(
                                  key: "page",
                                  value: page,
                                  onDone: () {
                                    _getData();
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox();
                  }),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildSelectableText(BuildContext context, SearchType mode,
      SearchType selectedMode, VoidCallback onPressed) {
    if (mode == SearchType.all) {
      return const SizedBox();
    }
    bool isSelected = mode == selectedMode;
    String text = mode.displayName(context);
    String assetIcon = mode.iconPath();
    return GestureDetectorCustom(
      onTap: onPressed,
      child: Container(
          padding: EdgeInsets.symmetric(
              horizontal: isSelected ? 20 : 10, vertical: isSelected ? 10 : 5),
          child: Row(
            spacing: isSelected ? 7 : 3,
            children: [
              SvgPicture.asset(
                assetIcon,
                width: isSelected ? 32 : 24,
              ),
              Container(
                decoration: !isSelected
                    ? BoxDecoration(
                        border: Border(
                          bottom:
                              BorderSide(color: AppColors.primary, width: 0.8),
                        ),
                      )
                    : null,
                child: Text(text,
                    style: AppTextStyles.paragraph02v1().copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: isSelected ? 24 : 14,
                      color: AppColors.primary,
                      decorationColor: AppColors.primary,
                    )),
              ),
              if (mode == SearchType.aiRecommended && isSelected)
                GestureDetectorCustom(
                  onTap: () {
                    _showAiRecommendationDialog(context);
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: Row(
                      spacing: 4,
                      children: [
                        SvgIcon(
                          path: Assets.svg.v2.infoSVG,
                          width: 16,
                          height: 16,
                          color: AppColors.primary,
                        ),
                        Text(
                          AppText.value(context).recommended_reason,
                          style: AppTextStyles.textStyle14(context).copyWith(
                            color: AppColors.primary,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          )),
    );
  }



  void _showAiRecommendationDialog(BuildContext context) {
    var state = _exploreUserCubit.state;
    var summary = (state.exploreUser).metaData?['ai_summary'];
    var summaryJa = (state.exploreUser).metaData?['ai_summary_ja'];
    var summaryVi = (state.exploreUser).metaData?['ai_summary_vi'];
    if (AppText.value(context).localeName == "ja") {
      summary = summaryJa;
    } else if (AppText.value(context).localeName == "vi") {
      summary = summaryVi;
    }
    AiRecommendationDialog.show(
      context,
      summary: summary,
      title: AppText.value(context).recommended_reason,
    );
  }
}
