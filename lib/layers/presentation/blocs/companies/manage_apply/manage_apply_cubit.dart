import 'dart:async';
import 'dart:convert';

import 'package:asiantech_link/layers/data/repository_impl/general_company_repository_impl.dart';
import 'package:asiantech_link/layers/data/source/local/local_storage_service.dart';
import 'package:asiantech_link/layers/domain/repository/general_company_repository.dart';
import 'package:asiantech_link/layers/domain/usecase/general_company_usecase/get_manage_apply_usecase.dart';
import 'package:asiantech_link/layers/domain/usecase/general_company_usecase/add_favorite_engineer_usecase.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:built_collection/built_collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:openapi/openapi.dart';

import 'manage_apply_cubit_state.dart';

class ManageApplyCubit extends Cubit<ManageApplyCubitState> {
  // Dependencies
  final GeneralCompanyRepository _generalCompanyRepository = GeneralCompanyRepositoryImpl();
  late final GetManageApplyUsecase _getManageApplyUsecase = GetManageApplyUsecase(
    generalCompanyRepository: _generalCompanyRepository,
  );
  late final AddFavoriteEngineerUsecase _addFavoriteEngineerUsecase = AddFavoriteEngineerUsecase(
    generalCompanyRepository: _generalCompanyRepository,
  );
  final LocalStorageService localStorageService = LocalStorageService();

  ManageApplyCubit() : super(ManageApplyCubitState.empty());

  // Reset to uninitialized state
  Future<void> resetToUninitialized() async {
    emit(ManageApplyCubitState.empty());
  }

  // Load manage apply data
  Future<void> loadManageApply({
    bool isResetPage = false,
    BuildContext? context,
    Function()? onDone,
  }) async {
    try {
      if (isResetPage) {
        emit(state.copyWith(page: 1));
      }

      emit(state.copyWith(isLoading: true, errorMessage: null));

      final result = await _getManageApplyUsecase.call(
        ordering: state.ordering,
        search: state.search?.isEmpty == true ? null : state.search,
        recruitIds: state.recruitModel?.recruitId != null
            ? BuiltList([state.recruitModel!.recruitId])
            : null,
        applyStatusFilterCodes: state.progressCodes != null
            ? BuiltList(state.progressCodes!)
            : null,
        hostCompanyId: state.hostCompanyId,
        page: state.page,
      );

      emit(state.copyWith(
        listAppliedEngineer: result,
        isLoading: false,
        errorMessage: null,
      ));

      onDone?.call();
    } catch (e) {
      onDone?.call();
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.parseErrorMessage(),
      ));
    }
  }

  // Update single filter
  Future<void> updateFilter({
    required String key,
    required dynamic value,
    Function()? onDone,
  }) async {
    final data = state.toMap();
    data[key] = value;
    emit(state.updateFilter(data));
    onDone?.call();
  }

  // Update multiple filters
  Future<void> updateFilters({
    required Function()? onDone,
    required Map<String, dynamic> filters,
  }) async {
    final data = state.toMap();
    for (var x in filters.keys) {
      data[x] = filters[x]?.toString();
    }

    emit(state.updateFilter(data));
    onDone?.call();
  }

  // Update recruit model and reload data
  Future<void> updateRecruitModel(RecruitUploadedSerializers? recruitModel, {bool reload = false}) async {
    if (reload) {
      // Show loading immediately
      emit(state.copyWith(
        recruitModel: recruitModel,
        page: 1,
        clearRecruitModel: recruitModel == null,
        isLoading: true,
        errorMessage: null,
      ));
      await loadManageApply();
    } else {
      emit(state.copyWith(
        recruitModel: recruitModel,
        page: 1,
        clearRecruitModel: recruitModel == null,
      ));
    }
  }

  // Update progress codes and reload data
  Future<void> updateProgressCodes(List<int>? progressCodes, {bool reload = false}) async {
    if (reload) {
      // Show loading immediately
      emit(state.copyWith(
        progressCodes: progressCodes,
        page: 1,
        clearProgressCodes: progressCodes == null,
        isLoading: true,
        errorMessage: null,
      ));
      await loadManageApply();
    } else {
      emit(state.copyWith(
        progressCodes: progressCodes,
        page: 1,
        clearProgressCodes: progressCodes == null,
      ));
    }
  }

  // Update search and reload data
  Future<void> updateSearch(String? search, {bool reload = false}) async {
    if (reload) {
      // Show loading immediately
      emit(state.copyWith(
        search: search,
        page: 1,
        clearSearch: search == null,
        isLoading: true,
        errorMessage: null,
      ));
      await loadManageApply();
    } else {
      emit(state.copyWith(
        search: search,
        page: 1,
        clearSearch: search == null,
      ));
    }
  }

  // Update ordering and reload data
  Future<void> updateOrdering(String ordering, {bool reload = false}) async {
    if (reload) {
      // Show loading immediately
      emit(state.copyWith(
        ordering: ordering,
        page: 1,
        isLoading: true,
        errorMessage: null,
      ));
      await loadManageApply();
    } else {
      emit(state.copyWith(ordering: ordering, page: 1));
    }
  }

  // Update page and reload data
  Future<void> updatePage(int page, {bool reload = false}) async {
    if (reload) {
      // Show loading immediately
      emit(state.copyWith(
        page: page,
        isLoading: true,
        errorMessage: null,
      ));
      await loadManageApply();
    } else {
      emit(state.copyWith(page: page));
    }
  }

  // Update host company ID
  Future<void> updateHostCompanyId(int? hostCompanyId) async {
    emit(state.copyWith(hostCompanyId: hostCompanyId));
  }

  // Update favorite user state
  Future<void> updateFavoriteUserState({
    required int userId,
    required bool isFavorite,
  }) async {
    if (state.listAppliedEngineer?.results == null) return;

    var list = state.listAppliedEngineer!.results.toList();
    var index = list.indexWhere((element) => element.engineer.userId == userId);
    
    if (index != -1) {
      var updatedItem = list[index].rebuild((b) => b
        ..engineer.replace(list[index].engineer.rebuild((engineerBuilder) => engineerBuilder
          ..interestedFlag = isFavorite ? 1 : 0)));
      list[index] = updatedItem;

      emit(state.copyWith(
        listAppliedEngineer: state.listAppliedEngineer!.rebuild((b) => b
          ..results.replace(BuiltList(list))),
      ));

      // Call API to update favorite status
      try {
        await _addFavoriteEngineerUsecase.call(
          data: GeneralCompanyUpdateFavoriteUser((update) {
            update.isFavorite = isFavorite;
            update.userId = userId;
          }),
        );
      } catch (e) {
        // Revert on error
        var revertedItem = list[index].rebuild((b) => b
          ..engineer.replace(list[index].engineer.rebuild((engineerBuilder) => engineerBuilder
            ..interestedFlag = !isFavorite ? 1 : 0)));
        list[index] = revertedItem;

        emit(state.copyWith(
          listAppliedEngineer: state.listAppliedEngineer!.rebuild((b) => b
            ..results.replace(BuiltList(list))),
        ));
      }
    }
  }

  // Clear all filters
  Future<void> clearFilters({bool reload = false}) async {
    if (reload) {
      // Show loading immediately
      emit(state.copyWith(
        page: 1,
        clearRecruitModel: true,
        clearProgressCodes: true,
        clearSearch: true,
        isLoading: true,
        errorMessage: null,
      ));
      await loadManageApply();
    } else {
      emit(state.copyWith(
        page: 1,
        clearRecruitModel: true,
        clearProgressCodes: true,
        clearSearch: true,
      ));
    }
  }

  // Update state directly
  Future<void> updateState({required ManageApplyCubitState newState}) async {
    emit(newState);
  }

  // Helper method to check if loading state is properly managed
  bool get isCurrentlyLoading => state.isLoading;

  // Get filter from local storage
  Future<void> getFilterFromLocalStorage({
    Function(Map<String, dynamic>?)? onDone,
  }) async {
    try {
      final filterString = localStorageService.getString('manage_apply_filter');
      if (filterString != null) {
        final filters = json.decode(filterString) as Map<String, dynamic>;
        onDone?.call(filters);
      } else {
        onDone?.call(null);
      }
    } catch (e) {
      onDone?.call(null);
    }
  }

  // Update filter to local storage
  Future<void> updateFilterToLocalStorage() async {
    try {
      final filterData = state.toMap();
      await localStorageService.setString(
        'manage_apply_filter',
        json.encode(filterData),
      );
    } catch (e) {
      // Handle error silently
    }
  }
}
