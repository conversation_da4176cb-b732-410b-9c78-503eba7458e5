import 'package:equatable/equatable.dart';
import 'package:openapi/openapi.dart';

class ManageApplyCubitState extends Equatable {
  final PagingGeneralCompanyAppliedEngineersResponseModel? listAppliedEngineer;
  final String ordering;
  final int page;
  final String? search;
  final RecruitUploadedSerializers? recruitModel;
  final List<int>? progressCodes;
  final int? hostCompanyId;
  final bool isLoading;
  final String? errorMessage;

  const ManageApplyCubitState({
    this.listAppliedEngineer,
    this.ordering = "created",
    this.page = 1,
    this.search,
    this.recruitModel,
    this.progressCodes,
    this.hostCompanyId,
    this.isLoading = false,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
        listAppliedEngineer,
        ordering,
        page,
        search,
        recruitModel,
        progressCodes,
        hostCompanyId,
        isLoading,
        errorMessage,
      ];

  // Factory constructor for empty state
  factory ManageApplyCubitState.empty() {
    return const ManageApplyCubitState();
  }

  // Factory constructor for loading state
  factory ManageApplyCubitState.loading() {
    return const ManageApplyCubitState(isLoading: true);
  }

  // Factory constructor for error state
  factory ManageApplyCubitState.error(String errorMessage) {
    return ManageApplyCubitState(errorMessage: errorMessage);
  }

  // Copy with method
  ManageApplyCubitState copyWith({
    PagingGeneralCompanyAppliedEngineersResponseModel? listAppliedEngineer,
    String? ordering,
    int? page,
    String? search,
    RecruitUploadedSerializers? recruitModel,
    List<int>? progressCodes,
    int? hostCompanyId,
    bool? isLoading,
    String? errorMessage,
  }) {
    return ManageApplyCubitState(
      listAppliedEngineer: listAppliedEngineer ?? this.listAppliedEngineer,
      ordering: ordering ?? this.ordering,
      page: page ?? this.page,
      search: search ?? this.search,
      recruitModel: recruitModel ?? this.recruitModel,
      progressCodes: progressCodes ?? this.progressCodes,
      hostCompanyId: hostCompanyId ?? this.hostCompanyId,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  // Convert to map for local storage
  Map<String, dynamic> toMap() {
    return {
      'ordering': ordering,
      'page': page,
      'search': search,
      'progressCodes': progressCodes,
      'hostCompanyId': hostCompanyId,
    };
  }

  // Update filter from map
  ManageApplyCubitState updateFilter(Map<String, dynamic> data) {
    return copyWith(
      ordering: data['ordering'] ?? ordering,
      page: int.tryParse(data['page']?.toString() ?? '1') ?? page,
      search: data['search'],
      progressCodes: data['progressCodes'] != null
          ? List<int>.from(data['progressCodes'])
          : progressCodes,
      hostCompanyId: data['hostCompanyId'] != null
          ? int.tryParse(data['hostCompanyId'].toString())
          : hostCompanyId,
    );
  }

  // Check if has data
  bool get hasData => listAppliedEngineer != null;

  // Check if has error
  bool get hasError => errorMessage != null;

  // Check if is empty
  bool get isEmpty => !hasData && !isLoading && !hasError;

  // Get total count
  int get totalCount => listAppliedEngineer?.count ?? 0;

  // Get current page
  int get currentPage => page;

  // Check if has next page
  bool get hasNextPage {
    if (listAppliedEngineer == null) return false;
    final totalPages = (totalCount / 10).ceil();
    return currentPage < totalPages;
  }

  // Check if has previous page
  bool get hasPreviousPage => currentPage > 1;

  // Get total pages
  int get totalPages {
    if (totalCount == 0) return 1;
    return (totalCount / 10).ceil();
  }

  @override
  String toString() {
    return 'ManageApplyCubitState(isLoading: $isLoading, hasData: $hasData, hasError: $hasError, page: $page, ordering: $ordering)';
  }
}
