import 'dart:async';
import 'dart:developer' as developer;

import 'package:asiantech_link/layers/data/repository_impl/general_company_repository_impl.dart';
import 'package:asiantech_link/layers/domain/repository/general_company_repository.dart';
import 'package:asiantech_link/layers/domain/usecase/general_company_usecase/get_manage_apply_usecase.dart';
import 'package:asiantech_link/layers/domain/usecase/general_company_usecase/add_favorite_engineer_usecase.dart';
import 'package:asiantech_link/layers/presentation/blocs/companies/manage_apply/index.dart';
import 'package:built_collection/built_collection.dart';
import 'package:meta/meta.dart';
import 'package:openapi/openapi.dart';

@immutable
abstract class ManageApplyEvent {
  final GeneralCompanyRepository _generalCompanyRepository =
      GeneralCompanyRepositoryImpl();
  late final GetManageApplyUsecase _getManageApplyUsecase =
      GetManageApplyUsecase(
          generalCompanyRepository: _generalCompanyRepository);
  late final AddFavoriteEngineerUsecase _addFavoriteEngineerUsecase =
      AddFavoriteEngineerUsecase(
          generalCompanyRepository: _generalCompanyRepository);
  Stream<ManageApplyState> applyAsync(
      {ManageApplyState currentState, ManageApplyBloc bloc});
}

class LoadManageApplyEvent extends ManageApplyEvent {
  final String? ordering;
  final int? recruitId;
  final List<int>? progressCodes;
  final String? search;
  final int? hostCompanyId;
  final int page;

  LoadManageApplyEvent(
      {required this.ordering,
      this.recruitId,
      this.progressCodes,
      this.search,
      this.hostCompanyId,
      this.page = 1});
  @override
  Stream<ManageApplyState> applyAsync(
      {ManageApplyState? currentState, ManageApplyBloc? bloc}) async* {
    try {
      yield LoadingManageApplyState(
        ordering: ordering ?? "created",
        currentPage: page,
      );
      final model = await _getManageApplyUsecase.call(
        ordering: ordering,
        search: search,
        recruitIds: recruitId != null
            ? BuiltList(
                [recruitId],
              )
            : null,
        applyStatusFilterCodes:
            progressCodes != null ? BuiltList(progressCodes ?? []) : null,
        hostCompanyId: hostCompanyId,
        page: page,
      );
      yield InManageApplyState(
        listAppliedEngineer: model,
        currentPage: page,
        ordering: ordering ?? "created",
      );
    } catch (error, stackTrace) {
      developer.log('$error',
          name: 'LoadManageApplyEvent', error: error, stackTrace: stackTrace);
      yield ErrorManageApplyState(
        error.toString(),
        ordering: ordering ?? "created",
        currentPage: page,
      );
    }
  }
}

class UpdateFavoriteUserEvent extends ManageApplyEvent {
  final bool isFavorite;
  final int userId;
  final int applyIndex;

  UpdateFavoriteUserEvent({
    required this.userId,
    required this.isFavorite,
    required this.applyIndex,
  });

  @override
  Stream<ManageApplyState> applyAsync(
      {ManageApplyState? currentState, ManageApplyBloc? bloc}) async* {
    if (currentState is InManageApplyState) {
      try {
        var list = currentState.listAppliedEngineer.results.toList();
        if (applyIndex < list.length) {
          var item = list[applyIndex];
          var currentFavoriteStatus = item.engineer.interestedFlag == 1;

          if (currentFavoriteStatus != isFavorite) {
            var updatedItem = item.rebuild((b) => b
              ..engineer.replace(item.engineer.rebuild((engineerBuilder) => engineerBuilder
                ..interestedFlag = isFavorite ? 1 : 0)));
            list[applyIndex] = updatedItem;

            yield InManageApplyState(
              listAppliedEngineer: currentState.listAppliedEngineer.rebuild((b) => b
                ..results.replace(BuiltList(list))),
              currentPage: currentState.currentPage,
              ordering: currentState.ordering,
            );

            await _addFavoriteEngineerUsecase.call(
              data: GeneralCompanyUpdateFavoriteUser((update) {
                update.isFavorite = isFavorite;
                update.userId = userId;
              }),
            );
          }
        }
      } catch (error, stackTrace) {
        developer.log('$error',
            name: 'UpdateFavoriteUserEvent', error: error, stackTrace: stackTrace);
      }
    }
  }
}
