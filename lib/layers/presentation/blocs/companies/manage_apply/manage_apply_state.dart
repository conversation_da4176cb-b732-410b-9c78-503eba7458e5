import 'package:equatable/equatable.dart';
import 'package:openapi/openapi.dart';

abstract class ManageApplyState extends Equatable {
  final String ordering;
  final int currentPage;

  const ManageApplyState({
    this.ordering = "created",
    this.currentPage = 1,
  });

  @override
  List<Object> get props => [ordering, currentPage];
}

/// UnInitialized
class UnManageApplyState extends ManageApplyState {
  const UnManageApplyState({
    super.ordering = "created",
    super.currentPage = 1,
  });

  @override
  String toString() => 'UnManageApplyState';
}

class LoadingManageApplyState extends ManageApplyState {
  const LoadingManageApplyState({
    super.ordering = "created",
    super.currentPage = 1,
  });
}

/// Initialized
class InManageApplyState extends ManageApplyState {
  final PagingGeneralCompanyAppliedEngineersResponseModel listAppliedEngineer;

  const InManageApplyState({
    required this.listAppliedEngineer,
    super.currentPage = 1,
    super.ordering = "created",
  });

  @override
  List<Object> get props => [listAppliedEngineer.results.hashCode, currentPage, ordering];

  /// Create a copy of this state with updated values
  InManageApplyState copyWith({
    PagingGeneralCompanyAppliedEngineersResponseModel? listAppliedEngineer,
    int? currentPage,
    String? ordering,
  }) {
    return InManageApplyState(
      listAppliedEngineer: listAppliedEngineer ?? this.listAppliedEngineer,
      currentPage: currentPage ?? this.currentPage,
      ordering: ordering ?? this.ordering,
    );
  }
}

class ErrorManageApplyState extends ManageApplyState {
  const ErrorManageApplyState(
    this.errorMessage, {
    super.ordering = "created",
    super.currentPage = 1,
  });

  final String errorMessage;

  @override
  String toString() => 'ErrorManageApplyState';

  @override
  List<Object> get props => [errorMessage, ordering, currentPage];
}
