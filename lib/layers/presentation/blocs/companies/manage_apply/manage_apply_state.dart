import 'package:equatable/equatable.dart';
import 'package:openapi/openapi.dart';

abstract class ManageApplyState extends Equatable {
  const ManageApplyState();

  @override
  List<Object> get props => [];
}

/// UnInitialized
class UnManageApplyState extends ManageApplyState {
  const UnManageApplyState();

  @override
  String toString() => 'UnManageApplyState';
}

class LoadingManageApplyState extends ManageApplyState {}

/// Initialized
class InManageApplyState extends ManageApplyState {
  final PagingGeneralCompanyAppliedEngineersResponseModel listAppliedEngineer;
  final int currentPage;
  final String ordering;

  const InManageApplyState({
    required this.listAppliedEngineer,
    this.currentPage = 1,
    this.ordering = "created",
  });

  @override
  List<Object> get props => [listAppliedEngineer.results.hashCode, currentPage, ordering];

  /// Create a copy of this state with updated values
  InManageApplyState copyWith({
    PagingGeneralCompanyAppliedEngineersResponseModel? listAppliedEngineer,
    int? currentPage,
    String? ordering,
  }) {
    return InManageApplyState(
      listAppliedEngineer: listAppliedEngineer ?? this.listAppliedEngineer,
      currentPage: currentPage ?? this.currentPage,
      ordering: ordering ?? this.ordering,
    );
  }
}

class ErrorManageApplyState extends ManageApplyState {
  const ErrorManageApplyState(this.errorMessage);

  final String errorMessage;

  @override
  String toString() => 'ErrorManageApplyState';

  @override
  List<Object> get props => [errorMessage];
}
