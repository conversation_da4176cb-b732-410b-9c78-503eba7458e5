import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:asiantech_link/app.dart';
import 'package:asiantech_link/layers/data/source/local/local_storage/jwt_local_storage.dart';
import 'package:asiantech_link/layers/data/source/local/local_storage_service.dart';
import 'package:asiantech_link/layers/data/source/local/session_storage_service.dart';
import 'package:asiantech_link/layers/domain/entity/academic_level_model.dart';
import 'package:asiantech_link/layers/domain/entity/country_model.dart';
import 'package:asiantech_link/layers/domain/entity/currency_model.dart';
import 'package:asiantech_link/layers/domain/entity/language_level_model.dart';
import 'package:asiantech_link/layers/domain/entity/language_model.dart';
import 'package:asiantech_link/layers/domain/entity/state_model.dart';
import 'package:asiantech_link/layers/presentation/blocs/general/theme/index.dart';
import 'package:asiantech_link/layers/presentation/blocs/general/top_management/top_management_bloc.dart';
import 'package:asiantech_link/layers/presentation/blocs/general/top_management/top_management_event.dart';
import 'package:asiantech_link/layers/presentation/resources/config_export.dart';
import 'package:asiantech_link/layers/domain/entity/country_code_model.dart';

import 'package:asiantech_link/layers/domain/entity/job_code_model.dart';
import 'package:asiantech_link/layers/domain/entity/skill_code_model.dart';
import 'package:asiantech_link/layers/presentation/resources/constants/sort_constants.dart';
import 'package:asiantech_link/layers/presentation/resources/enum.dart';
import 'package:asiantech_link/layers/presentation/services/profile_services.dart';

import 'package:asiantech_link/layers/presentation/utils/app_locale.dart';
import 'package:asiantech_link/layers/presentation/utils/app_validator.dart';
import 'package:asiantech_link/layers/presentation/utils/extensions.dart';
import 'package:asiantech_link/layers/presentation/utils/localizations/academic_localizations.dart';
import 'package:asiantech_link/layers/presentation/utils/localizations/country_localizations.dart';
import 'package:asiantech_link/layers/presentation/utils/localizations/currency_localizations.dart';
import 'package:asiantech_link/layers/presentation/utils/localizations/employee_localizations.dart';
import 'package:asiantech_link/layers/presentation/utils/localizations/job_localizations.dart';
import 'package:asiantech_link/layers/presentation/utils/localizations/job_roles_localizations.dart';
import 'package:asiantech_link/layers/presentation/utils/localizations/language_localizations.dart';
import 'package:asiantech_link/layers/presentation/utils/localizations/remote_localizations.dart';
import 'package:asiantech_link/layers/presentation/utils/localizations/state_country_localizations.dart';
import 'package:built_collection/built_collection.dart';
import 'package:crypto/crypto.dart';
import 'package:decimal/decimal.dart';
import 'package:decimal/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:openapi/openapi.dart';
import 'package:stream_transform/stream_transform.dart';

import 'package:universal_html/html.dart' as html;
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../google_cloud_logging_service.dart';
import '../../../routes/router.dart';
import '../../domain/entity/employ_code_model.dart';
import '../../domain/entity/qualification_model.dart';
import '../blocs/general/app/index.dart';

import 'package:image/image.dart' as img;

import '../services/app_services.dart';
import 'common_dialog.dart';

class Helper {
  static EventTransformer<E> debounce<E>(Duration duration) {
    return (events, mapper) {
      return events.debounce(duration).asyncExpand(mapper);
    };
  }

  static bytesToUrl(Uint8List bytes) {
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrl(blob);
    return url;
  }

  static String? checkEmailValid(
      {required String email, required BuildContext context}) {
    // Check if the email is empty
    if (email.isEmpty) {
      return AppText.value(context).please_enter_email;
    }
    // check regex email
    final allowedCharsRegex = RegExp(r'^[\w\-\.\+]+@([\w\-]+\.)+[\w\-]{2,}$');

    // Check overall length
    if (email.length >= 201) {
      return AppText.value(context).email_too_long;
    }

    // Split email into local and domain parts
    List<String> parts = email.split('@');
    if (parts.length != 2) {
      return AppText.value(context).email_single_at_symbol;
    }

    String localPart = parts[0];
    String domainPart = parts[1];

    // Check local part length
    if (localPart.length >= 64) {
      return AppText.value(context).email_local_part_too_long;
    }

    // Check allowed characters
    if (!allowedCharsRegex.hasMatch(email)) {
      return AppText.value(context).email_invalid_format;
    }

    // Check for consecutive periods
    if (email.contains('..')) {
      return AppText.value(context).email_consecutive_periods;
    }

    // Check for periods at the start or end of local part or before @
    if (localPart.startsWith('.') ||
        localPart.endsWith('.') ||
        email.endsWith('@')) {
      return AppText.value(context).email_periods_invalid_positions;
    }

    // Check domain part is not an IP address
    final ipRegex = RegExp(r'^\d{1,3}(\.\d{1,3}){3}$');
    if (ipRegex.hasMatch(domainPart)) {
      return AppText.value(context).email_domain_no_ip;
    }

    return null;
  }

  static String? checkUpdatePasswordValid(
      {required String oldPassword,
      required String newPassword,
      required String confirmPassword,
      required BuildContext context}) {
    if (oldPassword.isEmpty) {
      return AppText.value(context).please_enter_old_password;
    }
    if (newPassword.isEmpty) {
      return AppText.value(context).please_enter_new_password;
    }
    if (confirmPassword.isEmpty) {
      return AppText.value(context).please_enter_confirm_password;
    }
    var error = AppValidator.checkPasswordValid(newPassword, context);
    if (error != null) {
      return error;
    }

    return null;
  }

  static String? checkUrlValid(
      {required String url, required BuildContext context}) {
    if (url.isEmpty) {
      return AppText.value(context).pleaseEnterUrl;
    }
    final urlRegex = RegExp(
        r'^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$');
    if (!urlRegex.hasMatch(url)) {
      return AppText.value(context).urlIsInvalid;
    }
    return null;
  }

  static String getCompanySizeName(
      {required BuildContext context, required EmployeeType type}) {
    switch (type) {
      case EmployeeType.from1To9:
        return AppText.value(context).fromTo("1", "9");
      case EmployeeType.from10To29:
        return AppText.value(context).fromTo("10", "29");
      case EmployeeType.from30To49:
        return AppText.value(context).fromTo("30", "49");
      case EmployeeType.from50To99:
        return AppText.value(context).fromTo("50", "99");
      case EmployeeType.from100To299:
        return AppText.value(context).fromTo("100", "299");
      case EmployeeType.from300To499:
        return AppText.value(context).fromTo("300", "499");
      case EmployeeType.from500To999:
        return AppText.value(context).fromTo("500", "999");
      case EmployeeType.over1000:
        return AppText.value(context).over1000;
    }
  }

  static int sexTypeToInt(SexType sexType) {
    if (sexType == SexType.unknown) {
      return 0;
    } else if (sexType == SexType.male) {
      return 1;
    } else if (sexType == SexType.female) {
      return 2;
    } else if (sexType == SexType.other) {
      return 3;
    }
    return 0;
  }

  static SexType intToSexType(int? value) {
    if (value == 0) {
      return SexType.unknown;
    } else if (value == 1) {
      return SexType.male;
    } else if (value == 2) {
      return SexType.female;
    } else if (value == 3) {
      return SexType.other;
    }
    return SexType.unknown;
  }

  static String getSexName(SexType type, BuildContext context) {
    switch (type) {
      case SexType.male:
        return AppText.value(context).male;
      case SexType.female:
        return AppText.value(context).female;
      case SexType.other:
        return AppText.value(context).others;
      case SexType.unknown:
        return AppText.value(context).unanswered;
    }
  }

  static Future<Uint8List?> pickImageWeb() async {
    try {
      // Create an input element
      html.FileUploadInputElement uploadInput = html.FileUploadInputElement()
        ..accept = '.jpg,.png,.jpeg,.gif';

      // Completer to return the bytes (or null on cancel)
      final completer = Completer<Uint8List?>();

      // Listen for a real file-selection
      uploadInput.onChange.listen((event) {
        final files = uploadInput.files;
        if (files != null && files.isNotEmpty) {
          final reader = html.FileReader();
          reader.readAsArrayBuffer(files.first);
          reader.onLoadEnd.listen((_) {
            final bytes = reader.result as Uint8List;
            logToConsole("Picked file: ${files.first.name}");
            logToConsole("File size: ${bytes.length}");
            if (!completer.isCompleted) completer.complete(bytes);
          });
        }
      });
      // Listen for the user canceling the dialog
      uploadInput.on['cancel'].listen((_) {
        logToConsole("PICK_IMAGE: user cancelled");
        if (!completer.isCompleted) completer.complete(null);
      });

      // Fire the picker
      uploadInput.click();

      // Return whatever the user did
      return completer.future;
    } catch (e) {
      logToConsole("PICK_IMAGE: pickImageWeb error: $e");
      return null;
    }
  }

  static Future<XFile?> pickImage(
      {ImageSource source = ImageSource.gallery,
      int maxSize = 5,
      required BuildContext context}) async {
    try {
      logToConsole("PICK_IMAGE: START!");

      XFile? file;
      if (kIsWeb) {
        var bytes = await pickImageWeb();
        if (bytes != null) {
          file = XFile.fromData(bytes);
        } else {
          file = null;
        }
      } else {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: ['jpg', 'jpeg', 'png', 'gif'],
        );
        if (result != null) {
          file = result.files.first.xFile;
        }
      }
      if (file != null) {
        var fileSize = await file.length();
        logToConsole("PICK_IMAGE: file size $fileSize");
        if (fileSize > maxSize * 1024 * 1024) {
          if (context.mounted) {
            CommonDialog.showErrorDialog(
              context: context,
              message: AppText.value(context).image_less_than_x_mb(maxSize),
            );
          }
          return null;
        }
        return file;
      }
      logToConsole("PICK_IMAGE: FILE IS NULL!");
      return null;
    } catch (e) {
      logToConsole("PICK_IMAGE: ERROR: $e");
      return null;
    }
  }

  static String? getCursorIndex(String? value) {
    final url = value;
    if (url != null) {
      final uri = Uri.tryParse(url);
      if (uri != null) {
        final queryParameters = uri.queryParameters;
        if (queryParameters.containsKey('cursor')) {
          return queryParameters['cursor'];
        }
      }
    }
    return null;
  }

  static String getStringOrder(bool isDESC) {
    if (isDESC) {
      return SortConstants.createdDESC;
    }
    return SortConstants.createdASC;
  }

  static String getApplyCompanyOrder(bool isDESC) {
    if (isDESC) {
      return SortConstants.createdApplyCompanyDESC;
    }
    return SortConstants.createdApplyCompanyASC;
  }

  /// Format yyyyMMdd
  static DateTime? stringToDateTime(String? value) {
    if (value?.isEmpty ?? true) {
      return null;
    }
    String newInput =
        '${value?.substring(0, 4)}/${value?.substring(4, 6)}/${value?.substring(6, 8)}';
    final data = DateFormat("yyyy/MM/dd").parse(newInput);
    return data;
  }

  /// Format yyyyMMdd
  static String dateTimeToString(DateTime? value, {String? format}) {
    if (value == null) {
      return "";
    }

    final data = DateFormat(format ?? "MMM dd, yyyy").format(value.toLocal());
    return data;
  }

  static String dateTimeToStringYMMM({
    required DateTime? value,
    required String? locale,
  }) {
    if (value == null) {
      return "";
    }
    if (locale == 'vi') {
      // vi: Tháng 1, 2025
      return DateFormat.yMMMM(locale).format(value.toLocal());
    }
    // en: Jan 2025 ,ja: 2025年1月

    return DateFormat.yMMM(locale).format(value.toLocal());
  }

  static String dateTimeToyMdJM({
    required DateTime value,
    required String? locale,
  }) {
    return DateFormat.yMd(locale).add_jm().format(value.toLocal());
  }

  static String dateTimeToStringYMD({
    required DateTime? value,
    required String? locale,
  }) {
    if (value == null) {
      return "";
    }
    final data = DateFormat.yMd(locale).add_Hm().format(value.toLocal());
    return data;
  }

  /// Format yyyyMMdd, hh:mm with AM/PM UTC
  static String fullDateTimeToStringUTC(DateTime? value, {String? format}) {
    if (value == null) {
      return "";
    }

    final data =
        "${DateFormat(format ?? "MMM dd, yyyy, hh:mm a").format(value)} UTC";
    return data;
  }

  /// Format yyyyMMdd, hh:mm with AM/PM
  static String fullDateTimeToString(DateTime? value, {String? format}) {
    if (value == null) {
      return "";
    }

    final data =
        DateFormat(format ?? "MMM dd, yyyy, hh:mm a").format(value.toLocal());
    return data;
  }

  /// Forma dd/MM/yyyy
  static String dateTimeDDMMYYYY(DateTime? value, {String? format}) {
    if (value == null) {
      return "-";
    }

    final data = DateFormat(format ?? "dd/MM/yyyy").format(value.toLocal());
    return data;
  }

  /// Format yyyyMMdd to String yyyy-MM
  static String? stringToStringYearMonth(String? value) {
    if (value?.isEmpty ?? true) {
      return null;
    }
    return '${value?.substring(0, 4)}/${value?.substring(4, 6)}';
  }

  static Future<Uint8List?> resizeImage(Uint8List imageBytes,
      {int? targetHeight, int? targetWidth}) async {
    // Decode the image
    img.Image? originalImage = img.decodeImage(imageBytes);
    if (originalImage == null) {
      return null;
    }

    // Calculate the target dimensions while maintaining the aspect ratio
    if (targetHeight == null && targetWidth == null) {
      // If neither dimension is provided, return the original image bytes
      return imageBytes;
    } else if (targetHeight != null && targetWidth == null) {
      // If only targetHeight is provided, calculate targetWidth
      targetWidth =
          (originalImage.width * targetHeight / originalImage.height).round();
    } else if (targetHeight == null && targetWidth != null) {
      // If only targetWidth is provided, calculate targetHeight
      targetHeight =
          (originalImage.height * targetWidth / originalImage.width).round();
    }

    // Resize the image
    img.Image resizedImage =
        img.copyResize(originalImage, height: targetHeight, width: targetWidth);

    // Encode the resized image to PNG
    return Uint8List.fromList(img.encodePng(resizedImage));
  }

  static List<Country> getListCountry({required BuildContext context}) {
    return context.read<AppBloc>().countryModel?.country ?? [];
  }

  static String getCountryName(
      {required BuildContext context, required String countryCode}) {
    var country = CountryLocalizations.of(context).getValue(countryCode);
    return country;
  }

  static String getAddressStateName(
      {required BuildContext context, required String addressCode}) {
    return StateCountryLocalizations.of(context).getValue(addressCode);
  }

  static String getLanguageName(
      {required BuildContext context, required String languageCode}) {
    return LanguageLocalizations.of(context).getValue(languageCode);
  }

  static String getLanguageNameByList(
      {required BuildContext context, required List<String> languageCodes}) {
    try {
      if (languageCodes.isEmpty) {
        return AppText.value(context).not_set;
      }
      var list = languageCodes.toSet();
      return list
          .map((e) => getLanguageName(context: context, languageCode: e))
          .reduce((a, b) => "$a, $b");
    } catch (e) {
      return AppText.value(context).not_set;
    }
  }

  static List<String> getLanguageNameByListModel(
      BuiltList<EngLanguage>? languages) {
    return languages
            ?.map((lang) => lang.languageName)
            .whereType<String>()
            .toList() ??
        [];
  }

  static String extractJobCodeNames({
    required BuildContext context,
    required BuiltList<HopeJobSkill>? jobSkills,
  }) {
    final jobCodes = jobSkills
            ?.map((skill) => skill.jobCode)
            .whereType<String>()
            .toSet()
            .toList() ??
        [];

    final names = jobCodes
        .map((code) => getJobCodeName(context, code))
        .whereType<String>()
        .toSet()
        .toList();

    return names.join(', ');
  }

  static String getAcademicLevelName(
      {required BuildContext context, required int? id}) {
    return AcademicLocalizations.of(context)
        .getAcademicLevelName(id.toString());
  }

  static List<JobCode> getJobCodes(BuildContext context) {
    return BlocProvider.of<AppBloc>(context).jobCodeModel?.jobCode?.toList() ??
        [];
  }

  static String? getJobCodeName(BuildContext context, String jobCode) {
    return JobLocalizations.of(context).getValue(jobCode);
  }

  static String getJobCodeNameByList(BuildContext context, List<String> codes) {
    if (codes.isEmpty) {
      return AppText.value(context).not_set;
    }
    var list = codes.toSet();
    var value = list
            .map((e) => getJobCodeName(context, e))
            .reduce((a, b) => "$a, $b") ??
        AppText.value(context).not_set;
    return value;
  }

  static List<SkillCode> getSkills(BuildContext context) {
    return BlocProvider.of<AppBloc>(context).skillCode?.skillCode.toList() ??
        [];
  }

  static String getSkillName(
      {required BuildContext context,
      required String? skill,
      String? customName}) {
    var skills = getSkills(context);
    var index = skills.indexWhere((e) => e.id == skill);
    if (index != -1) {
      return skills[index].name ?? AppText.value(context).not_set;
    } else if (customName != null) {
      return customName;
    }
    return AppText.value(context).not_set;
  }

  static CountryCodeModel? getCountryModel(
      {required BuildContext context, required String? countryCode}) {
    try {
      final list = context.read<AppBloc>().listCountryFlags;
      var index = list.indexWhere((e) => e.code == countryCode);
      if (index != -1) {
        return list[index];
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static List<LanguageLevel> getLanguageLevels(BuildContext context) {
    return BlocProvider.of<AppBloc>(context)
            .languageLevelModel
            ?.languageLevel
            .toList() ??
        [];
  }

  static List<Qualification> getQualifications(BuildContext context) {
    return BlocProvider.of<AppBloc>(context)
            .qualificationModel
            ?.qualification
            ?.toList() ??
        [];
  }

  static List<EmployCode> getEmploymentCodes(BuildContext context) {
    return BlocProvider.of<AppBloc>(context).employCode?.employCode.toList() ??
        [];
  }

  static String getRecruitProgressCodeName(
      {required BuildContext context, required int? code}) {
    switch (code) {
      case RecruitProgressCodeType.requestingAnAgent:
        return AppText.value(context).requesting_agent;
      case RecruitProgressCodeType.application:
        return AppText.value(context).applying;
      case RecruitProgressCodeType.interviewRequest:
        return AppText.value(context).requesting_interview;
      case RecruitProgressCodeType.interviewScheduling:
        return AppText.value(context).scheduling_interview_date;
      case RecruitProgressCodeType.interviewDateConfirmed:
        return AppText.value(context).interview_date_confirmed;
      case RecruitProgressCodeType.interviewCompleted:
        return AppText.value(context).interview_completed;
      case RecruitProgressCodeType.jobOffer:
        return AppText.value(context).job_offering;
      case RecruitProgressCodeType.offerAccepted:
        return AppText.value(context).job_offer_accepted;
      case RecruitProgressCodeType.employed:
        return AppText.value(context).currently_employed;
      case RecruitProgressCodeType.notPassed:
        return AppText.value(context).not_passed;
      case RecruitProgressCodeType.applicationWithdrawn:
        return AppText.value(context).application_declined;
      case RecruitProgressCodeType.interviewWithdrawn:
        return AppText.value(context).application_declined;
      case RecruitProgressCodeType.offerDeclined:
        return AppText.value(context).offer_declined;
      case RecruitProgressCodeType.otherCompanyOffer:
        return AppText.value(context).offer_from_another_company;
      default:
        return code?.toString() ?? "";
    }
  }

  static Color getRecruitProgressCodeColor({required int? code}) {
    switch (code) {
      case RecruitProgressCodeType.requestingAnAgent:
        return AppColors.requestingAnAgent;
      case RecruitProgressCodeType.application:
        return AppColors.application;
      case RecruitProgressCodeType.interviewRequest:
        return AppColors.interviewRequest;
      case RecruitProgressCodeType.interviewScheduling:
        return AppColors.interviewScheduling;
      case RecruitProgressCodeType.interviewDateConfirmed:
        return AppColors.interviewDateConfirmed;
      case RecruitProgressCodeType.interviewCompleted:
        return AppColors.interviewCompleted;
      case RecruitProgressCodeType.jobOffer:
        return AppColors.jobOffer;
      case RecruitProgressCodeType.offerAccepted:
        return AppColors.offerAccepted;
      case RecruitProgressCodeType.employed:
        return AppColors.hostCompanyPrimary;
      case RecruitProgressCodeType.notPassed:
      case RecruitProgressCodeType.applicationWithdrawn:
      case RecruitProgressCodeType.interviewWithdrawn:
      case RecruitProgressCodeType.offerDeclined:
      case RecruitProgressCodeType.otherCompanyOffer:
      default:
        return Colors.transparent;
    }
  }

  static int? getAge(DateTime? birthDate) {
    if (birthDate != null) {
      final today = DateTime.now();
      int age = today.year - birthDate.year;

      if (today.month < birthDate.month ||
          (today.month == birthDate.month && today.day < birthDate.day)) {
        age--;
      }
      return age;
    }
    return null;
  }

  static String getDurationCareer(
      {required DateTime? start,
      required DateTime? end,
      required BuildContext context}) {
    String endDate = AppText.value(context).present;
    if (start == null) {
      return "-:--";
    }
    String startDate = dateTimeToStringYMMM(
      value: start,
      locale: AppText.value(context).localeName,
    );

    if (end != null) {
      endDate = dateTimeToStringYMMM(
        value: end,
        locale: AppText.value(context).localeName,
      );
    }
    end ??= DateTime.now();
    String duration = '';
    int years = end.year - start.year;
    int months = end.month - start.month;

    // Adjust if months are negative
    if (months < 0) {
      years--;
      months += 12;
    }
    if (years > 0) {
      duration += "$years ${AppText.value(context).year}";
    }
    if (months > 0) {
      duration += "$months ${AppText.value(context).month}";
    }
    if (duration.isNotEmpty) {
      duration = "($duration)";
    }

    return "$startDate - $endDate$duration";
  }

  static String getEmployCodeName({
    required BuildContext context,
    required String? code,
  }) {
    return EmployeeLocalizations.of(context).getValue(code ?? "");
  }

  static String getJobRolesName({
    required BuildContext context,
    required String? code,
  }) {
    return JobRolesLocalizations.of(context).getValue(code ?? "");
  }

  static String getLanguageLevelName(
      {required BuildContext context, required int? level}) {
    var languages = getLanguageLevels(context);
    int index = languages.indexWhere((e) => e.id == level);
    if (index != -1) {
      Locale locale = Localizations.localeOf(context);
      if (locale.languageCode == AppConstants.jaLanguage) {
        return languages[index].levelJp ?? "";
      }
      if (locale.languageCode == AppConstants.viLanguage) {
        return languages[index].levelVn ?? "";
      }
      return languages[index].levelEn ?? "";
    }
    return "";
  }

  static String getQualificationName(
      {required BuildContext context,
      String? licenceCode,
      String? customName}) {
    if (licenceCode == AppConstants.otherLicenceCode.toString()) {
      return customName ?? '';
    }
    var list = getQualifications(context);
    var index = list.indexWhere((e) => e.id?.toString() == licenceCode);
    if (index != -1) {
      return list[index].name ?? '';
    }
    return "";
  }

  static String? parseQualificationsToString({
    required BuildContext context,
    required BuiltList<EngLicense>? qualifications,
  }) {
    if (qualifications == null) return null;
    if (qualifications.isEmpty) return null;

    final List<String> qualificationNames = qualifications
        .map((e) {
          return getQualificationName(
              context: context,
              licenceCode: e.licenceCode,
              customName: e.licenceName ?? '');
        })
        .where((name) => name.isNotEmpty)
        .whereType<String>()
        .toList();

    return qualificationNames.join(', ');
  }

  static String buildWorkHistoryRange(
      BuildContext context, List<EngWorkHistory> workHistories) {
    final Map<String, double> skillDurations = {};

    final now = DateTime.now();

    for (final work in workHistories) {
      DateTime entering = work.enteringDate?.toDateTime() ?? now;
      DateTime quitting = (work.quittingDate?.toDateTime() ?? now);
      final duration = quitting.difference(entering).inDays / 365.0;

      if (work.roleName != null) {
        final roles = work.roleName!.split(',').map((e) => e.trim());
        for (final skill in roles) {
          skillDurations[skill] = (skillDurations[skill] ?? 0) + duration;
        }
      }
    }

    String formatSkillDuration(String skill, double duration) {
      if (duration < 2) {
        return '$skill (1 ${AppText.value(context).year.toLowerCase()})';
      } else {
        return '$skill (${AppText.value(context).years_x(duration.floor())})';
      }
    }

    return skillDurations.entries
        .map((e) => formatSkillDuration(e.key, e.value))
        .join(', ');
  }

  static String mapSkillExperiencesToName(
      {required BuildContext context, required int? yearOfExperiences}) {
    if (yearOfExperiences == null) {
      return "";
    }
    if (yearOfExperiences <= 1) {
      return AppText.value(context).fresher;
    }
    if (yearOfExperiences < 3) {
      return AppText.value(context).junior;
    }
    if (yearOfExperiences < 5) {
      return AppText.value(context).middle;
    }
    if (yearOfExperiences >= 5) {
      return AppText.value(context).senior;
    }
    return "";
  }

  static List<Currency> getListCurrency({required BuildContext context}) {
    return BlocProvider.of<AppBloc>(context)
            .currencyModel
            ?.currency
            ?.toList() ??
        [];
  }

  static List<CountryState> getListStateCity(
      BuildContext context, String? countryCode) {
    final stateModel = context.read<AppBloc>().stateModel;
    var list = stateModel?.state ?? [];
    list = list
        .where((element) => element.code.split('-').first == countryCode)
        .toList();
    list.sort((a, b) => a.nameEn.compareTo(b.nameEn));
    return list;
  }

  static List<LanguageCode> getListLanguage(BuildContext context) {
    return BlocProvider.of<AppBloc>(context).languageModel?.languageCode ?? [];
  }

  static List<AcademicLevel> getListAcademic(BuildContext context) {
    return BlocProvider.of<AppBloc>(context).academicLevel?.academicLevel ?? [];
  }

  static String latinToUnicode(String value) {
    List<int> bytes = latin1.encode(value);
    String unicodeString = utf8.decode(bytes);
    return unicodeString;
  }

  static String? getStateAndCountryName(
      {required BuildContext context,
      required String? code,
      String? separator = ", "}) {
    try {
      if (code != null && code.isNotEmpty) {
        final country = code.substring(0, 2);
        var countryName =
            Helper.getCountryName(context: context, countryCode: country);
        var cityName =
            Helper.getAddressStateName(context: context, addressCode: code);
        return "$cityName$separator$countryName";
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static String getStatesAndCountries({
    required BuildContext context,
    required List<String?> codes,
    String separator = "; ",
  }) {
    final locations = codes
        .whereType<String>()
        .where((code) => code.isNotEmpty)
        .map((code) => getStateAndCountryName(context: context, code: code))
        .whereType<String>()
        .toSet()
        .toList();

    return locations.join(separator);
  }

  static OtherSkillLevel convertPointToOtherSkillLevel(int point) {
    if (point <= 5) {
      return OtherSkillLevel.poor;
    }
    if (point <= 8) {
      return OtherSkillLevel.average;
    }
    if (point <= 11) {
      return OtherSkillLevel.good;
    }
    return OtherSkillLevel.excellent;
  }

  static String formatMoney(String value) {
    try {
      var result = Decimal.parse(value);
      var formatter = DecimalFormatter(NumberFormat.decimalPattern('en-US'));
      return result.formatWith(formatter);
    } catch (e) {
      return value;
    }
  }

  static String formatYenInMan(String value) {
    try {
      final amount = Decimal.parse(value);

      if (amount < Decimal.fromInt(10000)) {
        return formatMoney(value);
      }

      final manUnits = (amount / Decimal.fromInt(10000))
          .toDecimal(scaleOnInfinitePrecision: 0);

      return '${manUnits.toString()}万';
    } catch (e) {
      return value;
    }
  }

  static String getUserSkillPointName(
      {required BuildContext context, required int type}) {
    return switch (type) {
      UserSkillPointType.average => AppText.value(context).average,
      UserSkillPointType.good => AppText.value(context).good,
      UserSkillPointType.excellent => AppText.value(context).excellent,
      (_) => '',
    };
  }

  static String getFullName(
      {required BuildContext context, String? firstName, String? lastName}) {
    if (firstName == null && lastName == null) {
      return AppText.value(context).not_set;
    }
    var locale = AppText.value(context).localeName;
    if (locale == "ja") {
      return "$lastName $firstName";
    } else if (locale == "en") {
      return "$firstName $lastName";
    } else if (locale == "vi") {
      return "$lastName $firstName";
    }
    return "$firstName $lastName";
  }

  static String mapLanguageCodeToCountryCode(String languageCode) {
    if (languageCode == "ja") {
      return "jp";
    } else if (languageCode == "en") {
      return "en";
    } else if (languageCode == "vi") {
      return "vn";
    }
    return "en";
  }

  static Locale? getLocale(String languageCode) {
    if (languageCode == "ja") {
      return const Locale("ja", "JP");
    }
    if (languageCode == "en") {
      return const Locale("en", "US");
    }
    if (languageCode == "vi") {
      return const Locale("vi", "VN");
    }
    return null;
  }

  static String getCurrencyName(
      {required BuildContext context, required String currencyCode}) {
    return CurrencyLocalizations.of(context).getValue(currencyCode);
  }

  static String convertFullWidthToHalfWidth(String text) {
    if (text.isEmpty) return text;

    final Map<String, String> fullToHalfMap = {
      '０': '0',
      '１': '1',
      '２': '2',
      '３': '3',
      '４': '4',
      '５': '5',
      '６': '6',
      '７': '7',
      '８': '8',
      '９': '9',
      '．': '.',
      '，': ',',
      '　': ' ',
    };

    String result = text;
    fullToHalfMap.forEach((full, half) {
      result = result.replaceAll(full, half);
    });

    return result;
  }

  static String getRemoteCodeName(
      {required BuildContext context, required String code}) {
    var remoteCode = int.tryParse(code);
    if (remoteCode != null) {
      var list = decomposeBitNumber(remoteCode);
      var remoteCodes = list.map((e) {
        var valueString =
            RemoteLocalizations.of(context).getValue(e.toString());
        return valueString;
      }).toList();
      return remoteCodes.join(", ");
    }
    return "";
  }

  static void openSocialLink(String path, SocialType type) {
    switch (type) {
      case SocialType.facebook:
        // Ensure it's a full URL
        launchUrlString(
          path.startsWith('http') ? path : 'https://$path',
          mode: LaunchMode.externalApplication,
        );
        break;
      case SocialType.zalo:
        launchUrlString("https://zalo.me/$path",
            mode: LaunchMode.externalApplication);
        break;
      case SocialType.linkedin:
        launchUrlString(
          path.startsWith('http') ? path : 'https://$path',
          mode: LaunchMode.externalApplication,
        );
        break;
      case SocialType.whatsapp:
        launchUrlString("https://wa.me/$path",
            mode: LaunchMode.externalApplication);
        break;
      case SocialType.email:
        launchUrlString("mailto:$path", mode: LaunchMode.externalApplication);
        break;
    }
  }

  /// Decompose a number into a list of its bit components
  /// Example: 7 -> [4, 2, 1]
  static List<int> decomposeBitNumber(int number, {List<int>? possibleValues}) {
    List<int> result = [];
    int remaining = number;
    final values = possibleValues ?? [32, 16, 8, 4, 2, 1];

    for (int value in values) {
      if (remaining >= value) {
        result.add(value);
        remaining -= value;
      }
    }

    return result;
  }

  /// Compose a list of bit values into a single number
  /// Example: [4, 2, 1] -> 7
  static int composeBitNumber(List<int> values) {
    return values.fold(0, (sum, value) => sum + value);
  }

  /// Convert a list of bit values to a comma-separated string
  /// Example: [4, 2, 1] -> "4,2,1"
  static String bitValuesToString(List<int> values) {
    return values.join(',');
  }

  /// Convert a comma-separated string to a list of bit values
  /// Example: "4,2,1" -> [4, 2, 1]
  static List<int> stringToBitValues(String value) {
    if (value.isEmpty) return [];
    return value.split(',').map((e) => int.parse(e)).toList();
  }

  static String getEngineerHightLight(
      {required BuildContext context,
      required String? countryCode,
      required int? sexType,
      required int? age,
      required int? jobChangeCount}) {
    try {
      String? gender;
      if (sexType != null) {
        gender =
            ProfileServices.getSexName(Helper.intToSexType(sexType), context);
      }
      String? countryName;
      if (countryCode != null) {
        countryName =
            Helper.getCountryName(countryCode: countryCode, context: context);
      }
      var text = AppText.value(context).engineer_info_highlight(
          age ?? "", countryName ?? "", gender ?? "", jobChangeCount ?? "0");

      var list = text.split(",");
      List<String> stringShouldRemove = [];
      if (countryCode == null) {
        stringShouldRemove.add(list[0]);
      }
      if (gender == null) {
        stringShouldRemove.add(list[1]);
      }
      if (age == null) {
        stringShouldRemove.add(list[2]);
      }
      for (var item in stringShouldRemove) {
        list.remove(item);
      }
      return list.join(",");
    } catch (e) {
      return "-";
    }
  }
}

getWidth(BuildContext context) {
  return MediaQuery.of(context).size.width;
}

getHeight(BuildContext context) {
  return MediaQuery.of(context).size.height;
}

final routerKey = GlobalKey<NavigatorState>();
BuildContext? get globalContext => routerKey.currentState?.context;

/// Google Cloud Logger instance
final googleCloudLoggingService = GoogleCloudLoggingService();

enum NotificationType { success, error, warning }

bool isAuthenticated({required int userType}) {
  final JWTLocalStorage jwtLocalStorage = JWTLocalStorage();
  var accessToken = jwtLocalStorage.getAccessTokenByUserType(userType);
  if (accessToken == null) {
    return false;
  }
  return true;
}

bool checkAuthRoute(BuildContext context) {
  JWTLocalStorage jwtLocalStorage = JWTLocalStorage();
  var accessToken = jwtLocalStorage.getAccessToken();
  if (accessToken == null) {
    return false;
  }
  var expired = jwtLocalStorage.isTokenExpired(accessToken);
  if (!expired) {
    return true;
  } else {
    clearAndNavigate(context, "/");
    return false;
  }
}

Color getPrimaryColor(int userType) {
  if (userType == UserTypeConstants.engineer) {
    return AppColors.primary;
  }
  return AppColors.hostCompanyPrimary;
}

Color getBackgroundColor(int userType) {
  if (userType == UserTypeConstants.engineer) {
    return AppColors.backgroundColor;
  }
  return AppColors.hostCompanyBackgroundColor;
}

int stringToInt(String value) {
  try {
    return int.parse(value);
  } catch (e) {
    return 0;
  }
}

Future<String?> authenticationRedirect(
    BuildContext context, GoRouterState state) async {
  final JWTLocalStorage jwtLocalStorage = JWTLocalStorage();
  bool existAccessToken = jwtLocalStorage.getAccessToken() != null;
  if (existAccessToken) {
    return state.uri.toString();
  }
  return "/";
}

bool checkIfWithinLast10Days(DateTime? created) {
  if (created == null) {
    return false;
  }
  DateTime now = DateTime.now();
  DateTime tenDaysAgo = now.subtract(const Duration(days: 10));

  return created.isAfter(tenDaysAgo);
}

String formatDateTimeToTimeString(DateTime? dateTime) {
  if (dateTime == null) {
    return "";
  }
  // Create a DateFormat instance with the desired time format
  final DateFormat timeFormat =
      DateFormat('HH:mm'); // Example format: 24-hour format with seconds

  // Format the DateTime object to a time string (convert to local time first)
  return timeFormat.format(dateTime.toLocal());
}

String formatDateTimeToDateString(DateTime? dateTime) {
  if (dateTime == null) {
    return "";
  }
  // Create a DateFormat instance with the desired time format
  final DateFormat dateFormat =
      DateFormat('dd/MM/yyyy'); // Example format: 24-hour format with seconds

  // Format the DateTime object to a time string (convert to local time first)
  return dateFormat.format(dateTime.toLocal());
}

String truncateWithEllipsis(int cutoff, String myString) {
  return (myString.length <= cutoff)
      ? myString
      : '${myString.substring(0, cutoff)}...';
}

setPrimaryColor(int? userType) {
  if ([
    UserTypeConstants.referralAgencyStaff,
    UserTypeConstants.hostCompanyStaff,
    UserTypeConstants.otherSupportAgencyStaff
  ].contains(userType)) {
    AppColors.primary = AppColors.hostCompanyPrimary;
    AppColors.secondary = AppColors.hostCompanySecondary;
    AppColors.backgroundColor = AppColors.hostCompanyBackgroundColor;
  } else if (userType == UserTypeConstants.admin) {
    AppColors.primary = AppColors.adminPrimary;
    AppColors.secondary = AppColors.adminSecondary;
    AppColors.backgroundColor = AppColors.adminBackgroundColor;
  } else if (userType == UserTypeConstants.hostSupportAgencyStaff) {
    AppColors.primary = AppColors.supportCompanyPrimary;
    AppColors.secondary = AppColors.supportCompanySecondary;
    AppColors.backgroundColor = AppColors.supportCompanyBackgroundColor;
  } else {
    AppColors.primary = AppColors.engineerPrimary;
    AppColors.secondary = AppColors.engineerSecondary;
    AppColors.backgroundColor = AppColors.engineerBackgroundColor;
  }
  var context = globalContext;
  if (context != null) {
    BlocProvider.of<ThemeBloc>(context)
        .add(UpdatePrimaryColorTheme(color: AppColors.primary));
  }
}

Color getPrimaryColorByUserType(int userType) {
  if (userType == UserTypeConstants.engineer) {
    return AppColors.engineerPrimary;
  }
  if (userType == UserTypeConstants.admin) {
    return AppColors.adminPrimary;
  }
  if (userType == UserTypeConstants.hostSupportAgencyStaff) {
    return AppColors.supportCompanyPrimary;
  }
  if ([
    UserTypeConstants.referralAgencyStaff,
    UserTypeConstants.hostCompanyStaff,
    UserTypeConstants.otherSupportAgencyStaff
  ].contains(userType)) {
    return AppColors.hostCompanyPrimary;
  }
  return AppColors.engineerPrimary;
}

int? getTempUserType() {
  LocalStorageService localStorageService = LocalStorageService();
  String? userTypeTemp =
      localStorageService.getString(AppConstants.userTypeTemp);
  var type = int.tryParse("$userTypeTemp");
  return type;
}

pushNewRoute(
    {required BuildContext context, required String path, Object? extra}) {
  final currentRoute =
      GoRouter.of(context).routeInformationProvider.value.uri.toString();
  if (currentRoute != path) {
    context.push(path, extra: extra);
  } else {
    context.pushReplacement(path, extra: extra);
  }
}

void pushSupportCompanyRoute(BuildContext context, String routeName,
    {bool isPushNewRoute = false, Object? extra}) {
  String path = "/support-company/$routeName";
  if (isPushNewRoute) {
    pushNewRoute(
      context: context,
      path: path,
      extra: extra,
    );
  } else {
    context.push(path);
  }
}

int? getUserTypeTemp() {
  LocalStorageService localStorageService = LocalStorageService();
  var userType = localStorageService.getString(AppConstants.userTypeTemp);
  return int.tryParse("$userType");
}

int? getUserType() {
  SessionStorageService sessionStorageService = SessionStorageService();
  var userType = sessionStorageService.getString(AppConstants.userType);
  return int.tryParse("$userType");
}

int? getUserTypeFromLocalStorage() {
  LocalStorageService localStorageService = LocalStorageService();
  var userType = localStorageService.getString(AppConstants.userType);
  return int.tryParse("$userType");
}

String generateCodeVerifier() {
  final random = Random.secure();
  final codeVerifier = List<int>.generate(128, (i) => random.nextInt(256));
  final value = base64UrlEncode(codeVerifier).replaceAll('=', '');
  LocalStorageService localStorageService = LocalStorageService();
  localStorageService.setString(AppConstants.zaloCodeVerifierKey, value);
  return value;
}

String generateCodeChallenge(String codeVerifier) {
  final bytes = utf8.encode(codeVerifier);
  final digest = sha256.convert(bytes);
  return base64UrlEncode(digest.bytes).replaceAll('=', '');
}

Future<void> launchAppUrl(String url) async {
  if (!await launchUrl(Uri.parse(url))) {
    throw Exception('Could not launch $url');
  }
}

String yearOfExperienceString(BuildContext context, int? yearsOfExperience) {
  if (yearsOfExperience == null) {
    return AppText.value(context).please_select;
  }

  return AppServices.getYearsOfExperience(yearsOfExperience, context);
}

String? checkValidateEmpty(BuildContext context, int? value,
    {bool isValidate = true}) {
  if (isValidate) {
    return AppValidator.checkEmpty(value, context);
  }
  return null;
}

bool isJwt(String token) {
  final jwtRegex = RegExp(r'^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$');
  return jwtRegex.hasMatch(token);
}

showLoginPopup() {
  CommonDialog.showInfoDialog(
    title: AppText.valueGlobal.login_failed,
    message: AppText.valueGlobal.please_login_to_continue,
  );
}

reloadRoute() {
  html.window.location.reload();
}

bool checkEndDate(DateTime? endDate) {
  if (endDate != null) {
    DateTime now = DateTime.now();
    return endDate.isAfter(now);
  }
  return false;
}

// Get age from date of birth
int getAgeFromDOB(DateTime? dob) {
  if (dob == null) {
    return 0;
  }
  final now = DateTime.now();
  final age = now.year - dob.year;
  if (now.month < dob.month || (now.month == dob.month && now.day < dob.day)) {
    return age - 1;
  }
  return age;
}

backToTopManagement(UserDetailsSerializers? value, context,
    TopManagementBloc? topManagementBloc) {
  if (value != null) {
    var path = Uri.base.path;
    var subPaths = path.split('/');
    subPaths.removeLast();
    path = subPaths.join('/');
    var optionalEngineerId =
        BlocProvider.of<TopManagementBloc>(context).optionalEngineerId;
    if (optionalEngineerId != null) {
      path = "$path?userId=$optionalEngineerId";
    }
    if (topManagementBloc != null) {
      topManagementBloc.add(InTopManagementEvent(profile: value));
      if (optionalEngineerId != null) {
        pop(context);
      } else {
        globalContext?.go(path);
      }
    } else {
      globalContext?.pushReplacement(path);
    }
  }
}

String ordinal(int number) {
  if (!(number >= 1 && number <= 100)) {
    //here you change the range
    throw Exception('Invalid number');
  }

  if (number >= 11 && number <= 13) {
    return 'th';
  }

  switch (number % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
}

DateTime roundTime(DateTime dateTime, int interval) {
  final int minute = dateTime.minute;
  final int mod = minute % interval;
  final int roundUp = mod == 0 ? 0 : interval - mod;
  return dateTime.add(Duration(minutes: roundUp));
}

String minifyHtml(String html) {
  // Remove unnecessary whitespace and newlines
  return html
      .replaceAll(RegExp(r'\s+'), ' ')
      .replaceAll(RegExp(r'>\s+<'), '><')
      .replaceAll(RegExp(r'<!--.*?-->'), '') // Remove comments
      .trim();
}

updateUrlQueryParams(BuildContext context, Map<String, dynamic> queryParams) {
  var currentPath = GoRouterState.of(context).uri.path;
  final uri = Uri(path: currentPath, queryParameters: queryParams);
  html.window.history.pushState(null, '', uri.toString());
}

logToConsole(String message) {
  if (kIsWeb) {
    debugPrint(message);
  }
}

String getFullNameAndNickName(
    {required String? firstName,
    required String? lastName,
    required String? nickName,
    required BuildContext context}) {
  if (nickName != null && nickName.isNotEmpty) {
    var fullName = Helper.getFullName(
      context: context,
      firstName: firstName,
      lastName: lastName,
    );
    return "$fullName ($nickName)";
  }
  return Helper.getFullName(
    context: context,
    firstName: firstName,
    lastName: lastName,
  );
}

logErrorToCloud(dynamic error) {
  try {
    googleCloudLoggingService.writeLog(
      level: Level.error,
      message:
          'Try Catch error: ${error.toString()} ', // Join the log lines with a new line, so that it is written as a single message
      appVersion: appVersion,
    );
  } catch (e) {
    if (kDebugMode) {
      print(e);
    }
  }
}
