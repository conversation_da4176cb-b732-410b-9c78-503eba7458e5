import 'package:asiantech_link/assets.gen.dart';
import 'package:asiantech_link/layers/presentation/resources/app_colors.dart';

import 'package:asiantech_link/layers/presentation/resources/enum.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:decimal/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../src/generated/i18n/app_localizations.dart';
import 'app_locale.dart';
import 'parse_error.dart';

extension LocalizedBuildContext on BuildContext {
  AppLocalizations get loc => AppLocalizations.of(this).nonNullable;
}

extension StringX on String {
  ///Example: abc -> a
  String getFirstWord() {
    try {
      return split(' ').first;
    } catch (e) {
      return '';
    }
  }

  ///Example: abc -> Abc
  String upperCaseFirstLetter() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }

  ///Example: "120"-> 120.0
  double? toDouble({fractionDigits = 2}) {
    try {
      return double.tryParse(
          double.tryParse(this).nonNullable.toStringAsFixed(fractionDigits));
    } catch (e) {
      return null;
    }
  }

  ///Check valid email
  bool isValidEmail() {
    var regExpEmail =
        r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9.-]+\.[a-zA-Z]+";
    return RegExp(regExpEmail).hasMatch(this);
  }

  //Check valid name: at least 2 characters
  bool isValidName() {
    return length >= 2;
  }

  //Check valid fullName: at least 2 words
  bool isValidFullName() {
    bool isValid = false;
    if (trim().split(' ').length >= 2) {
      isValid = true;
    }
    return isValid;
  }

  /// Example: Nate Diaz -> return {'first_name':'Nate', 'last_name':'Diaz'}
  Map<String, dynamic> getFirstNameLastName() {
    if (isEmpty) {
      return {'first_name': '', 'last_name': ''};
    }
    var fullNames = toString().trim().split(' ');
    var firstName = "";
    var lastName = "";
    fullNames.removeWhere((element) => element.trim() == '');
    firstName = fullNames.first.toString();
    if (fullNames.length > 1) {
      fullNames.removeAt(0);
      lastName = fullNames.reduce((a, b) => "$a $b");
    }
    return {'first_name': firstName, 'last_name': lastName};
  }

  ///Example: Nate Diaz -> return "Nate"
  String getFirstName() {
    try {
      return getFirstNameLastName()['first_name'];
    } catch (e) {
      return '-';
    }
  }

  ///Check valid password
  bool isValidPassword() {
    RegExp passwordRegex =
        RegExp(r'^(?=.*[0-9])((?=.*[a-z])|(?=.*[A-Z]))[0-9a-zA-Z]{8,20}$');
    return passwordRegex.hasMatch(this);
  }

  /// 8:30 PM
  String format12H() {
    return DateFormat('hh:mm a').format(DateTime.parse(this));
  }

  /// good morning -> Good Morning
  String upperCaseFirstLetterWord() {
    try {
      List<String> words = split(' ');

      for (int i = 0; i < words.length; i++) {
        if (words[i].isNotEmpty) {
          words[i] = words[i][0].toUpperCase() + words[i].substring(1);
        }
      }
      String result = words.join(' ');
      return result;
    } catch (e) {
      return this;
    }
  }
}

extension StringNullX on String? {
  bool get isEmptyOrNull => this == null || (this?.isEmpty ?? true);
  int? toInt() {
    return int.tryParse(toString());
  }
}

extension DoubleX on double {
  /// 3.333333=> 3.33
  double? toCurrencyUnit() {
    try {
      return double.tryParse(toStringAsFixed(2));
    } catch (e) {
      return 0.0;
    }
  }
}

extension DateTimeX on DateTime {
  bool isToday() {
    var now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  String format12H() {
    return DateFormat('hh:mm a').format(toLocal());
  }

  String format24H() {
    return DateFormat('HH:mm').format(toLocal());
  }

  String formatHumaneReadable({bool showTime = false}) {
    return DateFormat('dd MMM yyyy').format(toLocal());
  }

  String format(String format) {
    return DateFormat(format).format(toLocal());
  }

  bool isSameDay(DateTime dateTime) {
    return day == dateTime.day &&
        dateTime.month == month &&
        dateTime.year == year;
  }

  bool isBigThanDate(DateTime dateTime) {
    var date1 = DateTime(year, month, day);
    var date2 = DateTime(dateTime.year, dateTime.month, dateTime.day);
    return date1.isAfter(date2);
  }
}

extension ConvertError on Object {
  ParseError parseError() {
    ParseError error = ParseError.fromJson(this);
    return error;
  }

  String parseErrorMessage() {
    try {
      ParseError error = ParseError.fromJson(this);
      return error.message;
    } catch (e) {
      return toString();
    }
  }
}

extension ObjectX<T> on T? {
  getSafeValue(Function(T value) onValue, {Function()? onNullValue}) {
    var x = this;
    if (x != null) {
      return onValue(x);
    } else {
      return onNullValue?.call();
    }
  }

  T get nonNullable =>
      this ?? (throw ArgumentError('Cannot get value of null object'));
}

extension WaitingFlagExtension on WaitingFlag {
  String valueName(BuildContext context) {
    switch (this) {
      case WaitingFlag.cancel:
        return AppText.value(context).cancel;
      case WaitingFlag.underConsideration:
        return AppText.value(context).under_consideration;
      case WaitingFlag.applied:
        return AppText.value(context).applied;
    }
  }

  int valueIndex() {
    switch (this) {
      case WaitingFlag.cancel:
        return 0;
      case WaitingFlag.underConsideration:
        return 1;
      case WaitingFlag.applied:
        return 2;
    }
  }
}

extension YearsOfExperienceExtension on YearsOfExperience {
  int get value {
    switch (this) {
      case YearsOfExperience.lessThan1Year:
        return 1;
      case YearsOfExperience.lessThan2Year:
        return 2;
      case YearsOfExperience.lessThan3Year:
        return 3;
      case YearsOfExperience.lessThan5Year:
        return 5;
      case YearsOfExperience.moreThan5Years:
        return 6;
    }
  }

  static YearsOfExperience toModel(int id) => switch (id) {
        1 => YearsOfExperience.lessThan1Year,
        2 => YearsOfExperience.lessThan2Year,
        3 => YearsOfExperience.lessThan3Year,
        5 => YearsOfExperience.lessThan5Year,
        6 => YearsOfExperience.moreThan5Years,
        _ => YearsOfExperience.lessThan1Year,
      };
}

extension RecruitProgressCodeExtension on RecruitProgressCode {
  int get value {
    switch (this) {
      case RecruitProgressCode.requestingAnAgent:
        return 10;
      case RecruitProgressCode.application:
        return 20;
      case RecruitProgressCode.interviewRequest:
        return 30;
      case RecruitProgressCode.interviewScheduling:
        return 31;
      case RecruitProgressCode.interviewDateConfirmed:
        return 32;
      case RecruitProgressCode.interviewCompleted:
        return 40;
      case RecruitProgressCode.jobOffer:
        return 50;
      case RecruitProgressCode.offerAccepted:
        return 60;
      case RecruitProgressCode.employed:
        return 80;
      case RecruitProgressCode.notPassed:
        return 90;
      case RecruitProgressCode.applicationWithdrawn:
        return 91;
      case RecruitProgressCode.interviewWithdrawn:
        return 92;
      case RecruitProgressCode.offerDeclined:
        return 93;
      case RecruitProgressCode.otherCompanyOffer:
        return 94;
    }
  }

  String getStatusText(BuildContext context) {
    switch (this) {
      case RecruitProgressCode.requestingAnAgent:
        return '';
      case RecruitProgressCode.application:
        return AppText.value(context).applied;
      case RecruitProgressCode.interviewRequest:
        return AppText.value(context).interviewRequest;
      case RecruitProgressCode.interviewScheduling:
        return AppText.value(context).scheduling_interview_date;
      case RecruitProgressCode.interviewDateConfirmed:
        return AppText.value(context).interview_date_confirmed;
      case RecruitProgressCode.interviewCompleted:
        return AppText.value(context).interview_completed;
      case RecruitProgressCode.jobOffer:
        return AppText.value(context).offered;
      case RecruitProgressCode.offerAccepted:
        return AppText.value(context).job_offer_accepted;
      case RecruitProgressCode.employed:
        return AppText.value(context).currently_employed;
      case RecruitProgressCode.notPassed:
        return AppText.value(context).rejected;
      case RecruitProgressCode.applicationWithdrawn:
        return AppText.value(context).application_declined;
      case RecruitProgressCode.interviewWithdrawn:
        return AppText.value(context).declined_interview;
      case RecruitProgressCode.offerDeclined:
        return AppText.value(context).declining_job_offer;
      case RecruitProgressCode.otherCompanyOffer:
        return AppText.value(context).job_offer_from_other_company;
    }
  }

  Color get statusColor {
    switch (this) {
      case RecruitProgressCode.offerAccepted:
        return AppColors.brightOrange;
      case RecruitProgressCode.notPassed:
      case RecruitProgressCode.applicationWithdrawn:
      case RecruitProgressCode.interviewWithdrawn:
      case RecruitProgressCode.offerDeclined:
      case RecruitProgressCode.otherCompanyOffer:
        return AppColors.declineApplicationColor;
      default:
        return AppColors.hostCompanyPrimary;
    }
  }

  static RecruitProgressCode fromValue(int value) {
    return RecruitProgressCode.values.firstWhere((code) => code.value == value);
  }
}

extension CareerTypeExtension on CareerType {
  int get value {
    switch (this) {
      case CareerType.currentlyWorking:
        return 0;
      case CareerType.retire:
        return 1;
    }
  }

  String name(BuildContext context) {
    switch (this) {
      case CareerType.currentlyWorking:
        return AppText.value(context).currently_employed;
      case CareerType.retire:
        return AppText.value(context).not_employed;
    }
  }
}

extension UserTypeExtension on UserType {
  int get value {
    switch (this) {
      case UserType.engineer:
        return 0;
      case UserType.referralAgencyStaff:
        return 1;
      case UserType.hostCompanyStaff:
        return 2;
      case UserType.hostSupportAgencyStaff:
        return 3;
      case UserType.otherSupportAgencyStaff:
        return 4;
      case UserType.managementAccount:
        return 5;
    }
  }
}

extension ApplicationStatusExtension on ApplicationStatus {
  int get value {
    switch (this) {
      case ApplicationStatus.all:
        return 0;
      case ApplicationStatus.offered:
        return 1;
      case ApplicationStatus.interview:
        return 2;
      case ApplicationStatus.applied:
        return 3;
      case ApplicationStatus.underConsideration:
        return 4;
      case ApplicationStatus.rejected:
        return 5;
    }
  }

  String getName(BuildContext context) {
    switch (this) {
      case ApplicationStatus.all:
        return AppText.value(context).all;
      case ApplicationStatus.offered:
        return AppText.value(context).offered;
      case ApplicationStatus.interview:
        return AppText.value(context).interview_in_progress;
      case ApplicationStatus.applied:
        return AppText.value(context).applied;
      case ApplicationStatus.underConsideration:
        return AppText.value(context).under_consideration;
      case ApplicationStatus.rejected:
        return AppText.value(context).not_successful;
    }
  }
}

extension SocialStyleExtension on SocialStyle {
  String name(BuildContext context) {
    switch (this) {
      case SocialStyle.driving:
        return AppText.value(context).driving;
      case SocialStyle.expressive:
        return AppText.value(context).expressive;
      case SocialStyle.amiable:
        return AppText.value(context).amiable;
      case SocialStyle.analytical:
        return AppText.value(context).analytical;
    }
  }

  Color get color {
    switch (this) {
      case SocialStyle.driving:
        return AppColors.orange;
      case SocialStyle.expressive:
        return AppColors.orange;
      case SocialStyle.amiable:
        return AppColors.orange;
      case SocialStyle.analytical:
        return AppColors.orange;
    }
  }
}

extension OtherSkillLevelExtension on OtherSkillLevel {
  String name(BuildContext context) {
    switch (this) {
      case OtherSkillLevel.poor:
        return AppText.value(context).poor;
      case OtherSkillLevel.average:
        return AppText.value(context).average;
      case OtherSkillLevel.good:
        return AppText.value(context).good;
      case OtherSkillLevel.excellent:
        return AppText.value(context).excellent;
    }
  }

  Color get color {
    switch (this) {
      case OtherSkillLevel.poor:
        return AppColors.orange;
      case OtherSkillLevel.average:
        return AppColors.yellow2;
      case OtherSkillLevel.good:
        return AppColors.green2;
      case OtherSkillLevel.excellent:
        return AppColors.green3;
    }
  }
}

extension AssesmentSkillTypeExtension on AssesmentSkillType {
  String name(BuildContext context) {
    switch (this) {
      case AssesmentSkillType.reportingCommunicationSkill:
        return AppText.value(context).reportingCommunicationSkills;
      case AssesmentSkillType.communicationSkill:
        return AppText.value(context).communication_skills;
      case AssesmentSkillType.globalSkill:
        return AppText.value(context).global_skills;
      case AssesmentSkillType.remoteSkill:
        return AppText.value(context).remote_work_skills;
      case AssesmentSkillType.projectManagementSkill:
        return AppText.value(context).project_management_skills;
    }
  }
}

extension DecimalExtension on Decimal {
  String formatWith(DecimalFormatter formatter) {
    return formatter.format((this));
  }
}

extension ContextX on BuildContext {
  double get width {
    return MediaQuery.of(this).size.width;
  }

  double get height {
    return MediaQuery.of(this).size.height;
  }

  bool get isMobile {
    return width < 768;
  }

  bool get isSmallScreen {
    return width < 1024;
  }

  Future<dynamic> pushSubRoute(String path, {Object? extra}) async {
    var paths = Uri.base.pathSegments.toList();
    paths.removeLast();
    var newPath = "/${paths.join('/')}/$path";
    debugPrint(newPath);
    return await push(newPath, extra: extra);
  }
}

double widthDesign = 1440;
double heightDesign = 1024;

extension ScreenSize on int {
  double resize(BuildContext context) {
    if (context.isMobile) {
      return this * 0.8;
    }
    return toDouble();
  }
}

extension SocialTypeExtension on SocialType {
  String iconPath() {
    switch (this) {
      case SocialType.facebook:
        return Assets.svg.v2.facebookSVG;
      case SocialType.zalo:
        return Assets.svg.v2.zaloSVG;
      case SocialType.linkedin:
        return Assets.svg.v2.linkedinSVG;
      case SocialType.whatsapp:
        return Assets.svg.v2.whatsappSVG;
      case SocialType.email:
        return Assets.svg.v2.emailSVG;
    }
  }
}

extension EmploymentStatusExtension on EmploymentStatus {
  String name(BuildContext context) {
    switch (this) {
      case EmploymentStatus.available:
        return AppText.value(context).available;
      case EmploymentStatus.notEmployed:
        return AppText.value(context).not_employed;
      case EmploymentStatus.notSet:
        return AppText.value(context).not_set;
    }
  }

  static EmploymentStatus getEnumFromValue(int value) {
    switch (value) {
      case 1:
        return EmploymentStatus.available;
      case 0:
        return EmploymentStatus.notEmployed;
      default:
        return EmploymentStatus.notSet;
    }
  }

  int? get value {
    switch (this) {
      case EmploymentStatus.available:
        return 1;
      case EmploymentStatus.notEmployed:
        return 0;
      case EmploymentStatus.notSet:
        return null;
    }
  }
}

extension PolicyAcceptTypeExtension on PolicyAcceptType {
  int? get value {
    switch (this) {
      case PolicyAcceptType.approved:
        return 1;
      case PolicyAcceptType.denied:
        return 0;
      case PolicyAcceptType.unapproved:
        return null;
    }
  }

  String name(BuildContext context) {
    switch (this) {
      case PolicyAcceptType.approved:
        return AppText.value(context).approved;
      case PolicyAcceptType.denied:
        return AppText.value(context).denied;
      case PolicyAcceptType.unapproved:
        return AppText.value(context).unapproved;
    }
  }
}

extension OrderingOptionExtension on OrderingOption {
  String get valueRequestType {
    switch (this) {
      case OrderingOption.newest:
        return 'created';
      case OrderingOption.popular:
        return 'total_recruit_progress_code_active';
      case OrderingOption.age:
        return 'age';
      case OrderingOption.salary:
        return 'payroll_price_usd';
    }
  }

  String displayName(BuildContext context) {
    switch (this) {
      case OrderingOption.newest:
        return AppText.value(context).newest_filter;
      case OrderingOption.popular:
        return AppText.value(context).popular_filter;
      case OrderingOption.age:
        return AppText.value(context).age_filter;
      case OrderingOption.salary:
        return AppText.value(context).salary_filter;
    }
  }
}

extension SexTypeX on SexType {
  int toInt() {
    switch (this) {
      case SexType.unknown:
        return 0;
      case SexType.male:
        return 1;
      case SexType.female:
        return 2;
      case SexType.other:
        return 3;
    }
  }

  static SexType fromInt(int? i) {
    switch (i) {
      case 1:
        return SexType.male;
      case 2:
        return SexType.female;
      case 3:
        return SexType.other;
      default:
        return SexType.unknown;
    }
  }

  String label(BuildContext ctx) {
    switch (this) {
      case SexType.male:
        return AppText.value(ctx).male;
      case SexType.female:
        return AppText.value(ctx).female;
      case SexType.other:
        return AppText.value(ctx).other;
      case SexType.unknown:
        return AppText.value(ctx).unanswered;
    }
  }
}

extension ColorBrightness on Color {
  Color lighten([double amount = .1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(this);
    final light = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(light).toColor();
  }
}

extension WorkHistoryTabExtension on WorkHistoryTab {
  WorkHistoryTab tabType(int index) {
    switch (index) {
      case 0:
        return WorkHistoryTab.professionalSummary;
      case 1:
        return WorkHistoryTab.workHistory;
      case 2:
        return WorkHistoryTab.majorProjects;
      default:
        return WorkHistoryTab.professionalSummary;
    }
  }

  String title(BuildContext context) {
    switch (this) {
      case WorkHistoryTab.professionalSummary:
        return AppText.value(context).professional_summary;
      case WorkHistoryTab.workHistory:
        return AppText.value(context).professional_experience;
      case WorkHistoryTab.majorProjects:
        return AppText.value(context).major_projects;
    }
  }
}
