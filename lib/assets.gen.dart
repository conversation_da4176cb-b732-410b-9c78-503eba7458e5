// GENERATED CODE - DO NOT MODIFY BY HAND
// Regenerate with: 'dart tools/gen_assets.dart'
class Assets {
  Assets._();
  static final svg = _Svg._();
  static final images = _Images._();
  static final json = _Json._();
  static final data = _Data._();
}

class _Svg {
  _Svg._();
  final v2 = _SvgV2._();
  final aiIconSVG = 'assets/svg/ai_icon.svg';
  final alertIconSVG = 'assets/svg/alert_icon.svg';
  final appliedJobSVG = 'assets/svg/applied_job.svg';
  final apply2IconSVG = 'assets/svg/apply2_icon.svg';
  final applyIconSVG = 'assets/svg/apply_icon.svg';
  final arrowBackSVG = 'assets/svg/arrow_back.svg';
  final arrowDownIconSVG = 'assets/svg/arrow_down_icon.svg';
  final arrowDownwardSVG = 'assets/svg/arrow_downward.svg';
  final arrowDropDownSVG = 'assets/svg/arrow_drop_down.svg';
  final arrowUpIconSVG = 'assets/svg/arrow_up_icon.svg';
  final ascIconSVG = 'assets/svg/asc_icon.svg';
  final backIconSVG = 'assets/svg/back_icon.svg';
  final bagIconSVG = 'assets/svg/bag_icon.svg';
  final bellIconSVG = 'assets/svg/bell_icon.svg';
  final birthDayIconSVG = 'assets/svg/birth_day_icon.svg';
  final blockSVG = 'assets/svg/block.svg';
  final businessCenterSVG = 'assets/svg/business_center.svg';
  final cakeSVG = 'assets/svg/cake.svg';
  final calendarIconSVG = 'assets/svg/calendar_icon.svg';
  final calendarTodaySVG = 'assets/svg/calendar_today.svg';
  final cameraAltSVG = 'assets/svg/camera_alt.svg';
  final chat2IconSVG = 'assets/svg/chat2_icon.svg';
  final chat3IconSVG = 'assets/svg/chat3_icon.svg';
  final chatIconSVG = 'assets/svg/chat_icon.svg';
  final checkSVG = 'assets/svg/check.svg';
  final checkCircleSVG = 'assets/svg/check_circle.svg';
  final chevronRightSVG = 'assets/svg/chevron_right.svg';
  final circleIconSVG = 'assets/svg/circle_icon.svg';
  final companyCircleSVG = 'assets/svg/company_circle.svg';
  final companyIconSVG = 'assets/svg/company_icon.svg';
  final connectIconSVG = 'assets/svg/connect_icon.svg';
  final copyIconSVG = 'assets/svg/copy_icon.svg';
  final defaultAvatarSVG = 'assets/svg/default_avatar.svg';
  final deleteSVG = 'assets/svg/delete.svg';
  final descIconSVG = 'assets/svg/desc_icon.svg';
  final domainSVG = 'assets/svg/domain.svg';
  final editSVG = 'assets/svg/edit.svg';
  final edit2IconSVG = 'assets/svg/edit2_icon.svg';
  final educationIconSVG = 'assets/svg/education_icon.svg';
  final emailIconSVG = 'assets/svg/email_icon.svg';
  final employmentTypeSVG = 'assets/svg/employment_type.svg';
  final errorSVG = 'assets/svg/error.svg';
  final europeFlagSVG = 'assets/svg/europe_flag.svg';
  final facebookIconSVG = 'assets/svg/facebook_icon.svg';
  final favoriteSVG = 'assets/svg/favorite.svg';
  final filter2IconSVG = 'assets/svg/filter2_icon.svg';
  final filterIconSVG = 'assets/svg/filter_icon.svg';
  final filterIconCompanySVG = 'assets/svg/filter_icon_company.svg';
  final foisIconSVG = 'assets/svg/fois_icon.svg';
  final folderIconSVG = 'assets/svg/folder_icon.svg';
  final formEmploymentSVG = 'assets/svg/form_employment.svg';
  final googleCalendarIconSVG = 'assets/svg/google_calendar_icon.svg';
  final googleDriveSVG = 'assets/svg/google_drive.svg';
  final hearFillIconSVG = 'assets/svg/hear_fill_icon.svg';
  final heartCircleSVG = 'assets/svg/heart_circle.svg';
  final heartFillSVG = 'assets/svg/heart_fill.svg';
  final heartIconSVG = 'assets/svg/heart_icon.svg';
  final heartRoundedSVG = 'assets/svg/heart_rounded.svg';
  final helpCircleIconSVG = 'assets/svg/help_circle_icon.svg';
  final helpOutlineSVG = 'assets/svg/help_outline.svg';
  final homeSVG = 'assets/svg/home.svg';
  final iconEngineerApplySVG = 'assets/svg/icon_engineer_apply.svg';
  final imagePlaceholderIconSVG = 'assets/svg/image_placeholder_icon.svg';
  final jobSVG = 'assets/svg/job.svg';
  final keyboardArrowDownSVG = 'assets/svg/keyboard_arrow_down.svg';
  final keyboardArrowLeftSVG = 'assets/svg/keyboard_arrow_left.svg';
  final keyboardArrowRightSVG = 'assets/svg/keyboard_arrow_right.svg';
  final keyboardArrowUpSVG = 'assets/svg/keyboard_arrow_up.svg';
  final keyboardBackspaceLeftSVG = 'assets/svg/keyboard_backspace_left.svg';
  final keyboardBackspaceRightSVG = 'assets/svg/keyboard_backspace_right.svg';
  final languageSVG = 'assets/svg/language.svg';
  final languageCircleIconSVG = 'assets/svg/language_circle_icon.svg';
  final languageIconSVG = 'assets/svg/language_icon.svg';
  final linkedinIconSVG = 'assets/svg/linkedin_icon.svg';
  final location2IconSVG = 'assets/svg/location2_icon.svg';
  final locationIconSVG = 'assets/svg/location_icon.svg';
  final lockSVG = 'assets/svg/lock.svg';
  final loginIconSVG = 'assets/svg/login_icon.svg';
  final logoSVG = 'assets/svg/logo.svg';
  final mailIconSVG = 'assets/svg/mail_icon.svg';
  final mailOutlineSVG = 'assets/svg/mail_outline.svg';
  final manageUserIconSVG = 'assets/svg/manage_user_icon.svg';
  final meetingIconSVG = 'assets/svg/meeting_icon.svg';
  final menu2IconSVG = 'assets/svg/menu2_icon.svg';
  final menuIconSVG = 'assets/svg/menu_icon.svg';
  final moneyBagSVG = 'assets/svg/money_bag.svg';
  final moneyIconSVG = 'assets/svg/money_icon.svg';
  final newMessageIconSVG = 'assets/svg/new_message_icon.svg';
  final newestIconSVG = 'assets/svg/newest_icon.svg';
  final noImageSVG = 'assets/svg/no_image.svg';
  final notificationIconSVG = 'assets/svg/notification_icon.svg';
  final officeIconSVG = 'assets/svg/office_icon.svg';
  final oldestIconSVG = 'assets/svg/oldest_icon.svg';
  final pauseIconSVG = 'assets/svg/pause_icon.svg';
  final personSVG = 'assets/svg/person.svg';
  final phoneSVG = 'assets/svg/phone.svg';
  final phoneCallIconSVG = 'assets/svg/phone_call_icon.svg';
  final phoneIconSVG = 'assets/svg/phone_icon.svg';
  final playIconSVG = 'assets/svg/play_icon.svg';
  final privacyIconSVG = 'assets/svg/privacy_icon.svg';
  final profileCircleIconSVG = 'assets/svg/profile_circle_icon.svg';
  final profileIconSVG = 'assets/svg/profile_icon.svg';
  final pushPinSVG = 'assets/svg/push_pin.svg';
  final qualificationIconSVG = 'assets/svg/qualification_icon.svg';
  final radioButtonUncheckedSVG = 'assets/svg/radio_button_unchecked.svg';
  final recruitManageIconSVG = 'assets/svg/recruit_manage_icon.svg';
  final salaryIconSVG = 'assets/svg/salary_icon.svg';
  final schoolIconSVG = 'assets/svg/school_icon.svg';
  final searchSVG = 'assets/svg/search.svg';
  final search2IconSVG = 'assets/svg/search2_icon.svg';
  final searchIconSVG = 'assets/svg/search_icon.svg';
  final searchJobSVG = 'assets/svg/search_job.svg';
  final searchUserIconSVG = 'assets/svg/search_user_icon.svg';
  final selectedUserIconSVG = 'assets/svg/selected_user_icon.svg';
  final settingsCircleSVG = 'assets/svg/settings_circle.svg';
  final signatureIconSVG = 'assets/svg/signature_icon.svg';
  final skillIconSVG = 'assets/svg/skill_icon.svg';
  final speakerIconSVG = 'assets/svg/speaker_icon.svg';
  final stopIconSVG = 'assets/svg/stop_icon.svg';
  final stripeDecorationSVG = 'assets/svg/stripe_decoration.svg';
  final supportIconSVG = 'assets/svg/support_icon.svg';
  final termIconSVG = 'assets/svg/term_icon.svg';
  final trashIconSVG = 'assets/svg/trash_icon.svg';
  final uploadImageIconSVG = 'assets/svg/upload_image_icon.svg';
  final visibilitySVG = 'assets/svg/visibility.svg';
  final visibilityOffSVG = 'assets/svg/visibility_off.svg';
  final warningSVG = 'assets/svg/warning.svg';
}

class _SvgV2 {
  _SvgV2._();
  final aiSVG = 'assets/svg/v2/ai.svg';
  final bagSVG = 'assets/svg/v2/bag.svg';
  final bellSVG = 'assets/svg/v2/bell.svg';
  final birthdaySVG = 'assets/svg/v2/birthday.svg';
  final chatSVG = 'assets/svg/v2/chat.svg';
  final companySVG = 'assets/svg/v2/company.svg';
  final editSVG = 'assets/svg/v2/edit.svg';
  final educationSVG = 'assets/svg/v2/education.svg';
  final emailSVG = 'assets/svg/v2/email.svg';
  final facebookSVG = 'assets/svg/v2/facebook.svg';
  final heartSVG = 'assets/svg/v2/heart.svg';
  final heartFillSVG = 'assets/svg/v2/heart_fill.svg';
  final historySVG = 'assets/svg/v2/history.svg';
  final homeSVG = 'assets/svg/v2/home.svg';
  final infoSVG = 'assets/svg/v2/info.svg';
  final languageSVG = 'assets/svg/v2/language.svg';
  final linkedinSVG = 'assets/svg/v2/linkedin.svg';
  final locationSVG = 'assets/svg/v2/location.svg';
  final logoutSVG = 'assets/svg/v2/logout.svg';
  final peopleSVG = 'assets/svg/v2/people.svg';
  final searchSVG = 'assets/svg/v2/search.svg';
  final starSVG = 'assets/svg/v2/star.svg';
  final taskSVG = 'assets/svg/v2/task.svg';
  final trendingSVG = 'assets/svg/v2/trending.svg';
  final uploadFileSVG = 'assets/svg/v2/upload_file.svg';
  final whatsappSVG = 'assets/svg/v2/whatsapp.svg';
  final yenSVG = 'assets/svg/v2/yen.svg';
  final zaloSVG = 'assets/svg/v2/zalo.svg';
}

class _Images {
  _Images._();
  final companySearchBackgroundPNG = 'assets/images/company_search_background.png';
  final congratulationsJPG = 'assets/images/congratulations.jpg';
  final facebookPNG = 'assets/images/facebook.png';
  final foisLogoPNG = 'assets/images/fois-logo.png';
  final foisIconPNG = 'assets/images/fois_icon.png';
  final homeBackgroundDecorationPNG = 'assets/images/home_background_decoration.png';
  final jobOfferPNG = 'assets/images/job_offer.png';
  final linkedinPNG = 'assets/images/linkedin.png';
  final loadingGIF = 'assets/images/loading.gif';
  final noDataImagePNG = 'assets/images/no_data_image.png';
  final signedSuccessJPG = 'assets/images/signed_success.jpg';
  final supportCompanySearchBackgroundPNG = 'assets/images/support_company_search_background.png';
  final testGIF = 'assets/images/test.gif';
  final whatsappPNG = 'assets/images/whatsapp.png';
  final zaloPNG = 'assets/images/zalo.png';
}

class _Json {
  _Json._();
  final academicLevelJSON = 'assets/json/academic_level.json';
  final animationAiLoadingJSON = 'assets/json/animation_ai_loading.json';
  final categorySkillJSON = 'assets/json/category_skill.json';
  final countryJSON = 'assets/json/country.json';
  final countryCurrenciesJSON = 'assets/json/country_currencies.json';
  final countryFlagsJSON = 'assets/json/country_flags.json';
  final currencyJSON = 'assets/json/currency.json';
  final employCodeJSON = 'assets/json/employ_code.json';
  final jobCodeJSON = 'assets/json/job_code.json';
  final jobRolesJSON = 'assets/json/job_roles.json';
  final languageCodeJSON = 'assets/json/language_code.json';
  final languageLevelJSON = 'assets/json/language_level.json';
  final qualificationJSON = 'assets/json/qualification.json';
  final remoteCodeJSON = 'assets/json/remote_code.json';
  final skillCodeJSON = 'assets/json/skill_code.json';
  final statesJSON = 'assets/json/states.json';
}

class _Data {
  _Data._();
  final countryCodesJSON = 'assets/data/country_codes.json';
}

