{"swagger": "2.0", "info": {"title": "AsianTech.Link API", "description": "API documentation", "version": "v1"}, "host": "************:8001", "schemes": ["http"], "basePath": "/api", "consumes": ["application/json"], "produces": ["application/json"], "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"Bearer": []}], "paths": {"/admin/create-email-schedule": {"post": {"operationId": "Create Email Schedule", "description": "Create a new email schedule", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateEmailSchedule"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/EmailSchedule"}}}, "tags": ["admin"]}, "parameters": []}, "/admin/delete-email-schedule-list": {"delete": {"operationId": "delete_email_schedule_list", "description": "Delete a list of email schedules by list id", "parameters": [{"name": "email_schedule_ids", "in": "query", "required": true, "type": "array", "items": {"type": "integer"}}], "responses": {"204": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["admin"]}, "parameters": []}, "/admin/delete-email-schedule/{id}/": {"delete": {"operationId": "Delete Email Schedule", "description": "Delete an existing email schedule", "parameters": [], "responses": {"204": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["admin"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/admin/delete-engineers/": {"delete": {"operationId": "Delete Engineers", "description": "Delete engineers", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DeleteEngineers"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["admin"]}, "parameters": []}, "/admin/export-user-data": {"post": {"operationId": "export_user_data", "description": "Export user data", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ExportUserDataBody"}}], "responses": {"200": {"description": "", "schema": {"type": "file", "format": "binary"}}}, "tags": ["admin"]}, "parameters": []}, "/admin/get-email-schedule-by-id/{id}/": {"get": {"operationId": "get_email_schedule_by_id", "description": "Get email schedules by id", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GetEmailDetailResponseModel"}}}, "tags": ["admin"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/admin/get-list-engineer/": {"get": {"operationId": "Get Engineer List", "description": "Get list of engineers", "parameters": [{"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "cursor", "in": "query", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "required": false, "type": "string"}, {"name": "register_date_from", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "register_date_to", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "query_search", "in": "query", "required": false, "type": "string", "x-nullable": true}, {"name": "created_user", "in": "query", "required": false, "type": "string", "x-nullable": true}, {"name": "is_data_policy_accept", "in": "query", "required": false, "type": "integer", "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GetListEngineerResponseModel"}}}, "tags": ["admin"]}, "parameters": []}, "/admin/get-list-of-email-schedules": {"get": {"operationId": "get_list_of_email_schedules", "description": "Get list of email schedules", "parameters": [{"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "cursor", "in": "query", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "required": false, "type": "string"}, {"name": "date_from", "in": "query", "required": false, "type": "string", "format": "date"}, {"name": "date_to", "in": "query", "required": false, "type": "string", "format": "date"}, {"name": "query_search", "in": "query", "required": false, "type": "string", "x-nullable": true}, {"name": "created_user", "in": "query", "required": false, "type": "string", "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GetEmailScheduleResponseModel"}}}, "tags": ["admin"]}, "parameters": []}, "/admin/get-list-registrar": {"get": {"operationId": "Get List Registrar", "description": "Get list of registrars", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GetListRegistrarResponseModel"}}}, "tags": ["admin"]}, "parameters": []}, "/admin/update-email-schedule/{id}/": {"put": {"operationId": "Update Email Schedule", "description": "Update an existing email schedule", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateEmailSchedule"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/EmailSchedule"}}}, "tags": ["admin"]}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}]}, "/admin/update-engineer/": {"put": {"operationId": "update_engineer", "description": "Update engineer", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateEngineer"}}, {"name": "user_id", "in": "query", "required": true, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["admin"]}, "parameters": []}, "/authentication/captcha/": {"get": {"operationId": "generate-captcha", "description": "", "parameters": [{"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CaptchaResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/check-captcha-required-in-login/": {"post": {"operationId": "check_captcha_required_in_login", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CheckCaptchaRequireInLogin"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CheckCaptchaRequireInLoginResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/confirm-login/": {"post": {"operationId": "confirm-login", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ConfirmLogin"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/confirm-whatsapp-code/": {"post": {"operationId": "confirm_whatsapp_code", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ConfirmWhatsappCode"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/data-deletion-request/": {"post": {"operationId": "data_deletion_request", "description": "", "parameters": [{"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/delete-account/": {"delete": {"operationId": "delete_account", "description": "", "parameters": [{"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/login-with-sns/": {"post": {"operationId": "login_with_sns", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LoginWithSNS"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LoginWithSNSResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/login/": {"post": {"operationId": "login", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Login"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LoginSuccessResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/logout/": {"post": {"operationId": "logout", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Logout"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/register/": {"post": {"operationId": "register", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Register"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/resend-verification-email/": {"post": {"operationId": "resend_verification_email", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SendCodeRequest"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/reset-password-confirm/": {"post": {"operationId": "reset-password-confirm", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ResetPasswordConfirm"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/reset-password/": {"post": {"operationId": "reset-password", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ResetPassword"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/send-email-verify/": {"post": {"operationId": "send_code", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SendCodeRequest"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/send-whatsapp-code/": {"post": {"operationId": "send_whatsapp_code", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SendWhatsappCode"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/authentication/verify-email/": {"post": {"operationId": "verify_email", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/VerifyEmail"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["authentication"]}, "parameters": []}, "/chat/mark-read-all/{group_id}/": {"post": {"operationId": "mark_read_all_messages", "description": "Mark all messages in a chat group as read for the current user", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/MarkReadAllMessagesResponseModel"}}}, "tags": ["chat"]}, "parameters": [{"name": "group_id", "in": "path", "required": true, "type": "string"}]}, "/engineers/add-agency-company/": {"post": {"operationId": "add_agency_company", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/EngineerUpdateAgencyCompany"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/delete-agency-company/": {"delete": {"operationId": "delete-agency-company", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/EngineerRemoveAgencyCompany"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/get-list-best-companies": {"get": {"operationId": "get-best-companies", "description": "", "parameters": [{"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "cursor", "in": "query", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/EngineerGetListBestCompaniesResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/get-list-of-select-agency-companies/": {"get": {"operationId": "get_list_of_select_agency_companies", "description": "", "parameters": [{"name": "optional_engineer_id", "in": "query", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/EngineerListAgencyCompanyResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/list-agency-company/": {"get": {"operationId": "get_list_agency_company", "description": "", "parameters": [{"name": "contact_mail", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/EngineerListAgencyCompanyResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/list-apply-company/": {"get": {"operationId": "get_list_apply_company", "description": "", "parameters": [{"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "cursor", "in": "query", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "required": false, "type": "string"}, {"name": "apply_status_filter_code", "in": "query", "required": false, "type": "integer", "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/EngineerListApplyCompanyResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/recruit/apply-recruit/{recruit_id}/{company_id}": {"post": {"operationId": "apply_recruit", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/BoolResponseModel"}}}, "tags": ["engineers"]}, "parameters": [{"name": "recruit_id", "in": "path", "required": true, "type": "string"}, {"name": "company_id", "in": "path", "required": true, "type": "string"}]}, "/engineers/recruit/cancel-recruit/": {"patch": {"operationId": "cancel_recruit", "description": "", "parameters": [{"name": "recruit_id", "in": "query", "required": true, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/recruit/change-status-interested/{recruit_id}": {"post": {"operationId": "change_status_interested", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/BoolResponseModel"}}}, "tags": ["engineers"]}, "parameters": [{"name": "recruit_id", "in": "path", "required": true, "type": "string"}]}, "/engineers/recruit/contract-details/{recruitment_id}": {"get": {"operationId": "get_contract_details", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/RecruitContractDetailsResponseModel"}}}, "tags": ["engineers"]}, "parameters": [{"name": "recruitment_id", "in": "path", "required": true, "type": "string"}]}, "/engineers/recruit/count-filter/": {"get": {"operationId": "count_filter", "description": "", "parameters": [{"name": "name", "in": "query", "required": false, "type": "string", "minLength": 1}, {"name": "employ_code", "in": "query", "required": false, "type": "string", "minLength": 1}, {"name": "places", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "job_code", "in": "query", "required": false, "type": "string", "minLength": 1}, {"name": "skill_codes_1", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "skill_codes_2", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "skill_codes_3", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "payroll_code", "in": "query", "required": false, "type": "string", "minLength": 1}, {"name": "payroll_price_from", "in": "query", "required": false, "type": "string", "minLength": 1}, {"name": "payroll_price_to", "in": "query", "required": false, "type": "string", "minLength": 1}, {"name": "language_codes", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "show_old_post", "in": "query", "required": true, "type": "boolean"}, {"name": "company_id", "in": "query", "required": false, "type": "integer"}, {"name": "job_codes", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "waiting_flag", "in": "query", "required": false, "type": "integer"}, {"name": "remote_code", "in": "query", "required": false, "type": "string", "minLength": 1}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/RecruitCountFilterResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/recruit/explore/": {"get": {"operationId": "list_recruits_explore", "description": "", "parameters": [{"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "ordering", "in": "query", "required": false, "type": "string"}, {"name": "name", "in": "query", "required": false, "type": "string"}, {"name": "employ_code", "in": "query", "required": false, "type": "string"}, {"name": "places", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "job_code", "in": "query", "required": false, "type": "string"}, {"name": "skill_codes_1", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "skill_codes_2", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "skill_codes_3", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "payroll_code", "in": "query", "required": false, "type": "string"}, {"name": "payroll_price_from", "in": "query", "required": false, "type": "string", "minLength": 1}, {"name": "payroll_price_to", "in": "query", "required": false, "type": "string", "minLength": 1}, {"name": "language_codes", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "cursor", "in": "query", "required": false, "type": "string"}, {"name": "show_old_post", "in": "query", "required": true, "type": "boolean"}, {"name": "company_id", "in": "query", "required": false, "type": "integer"}, {"name": "job_codes", "in": "query", "required": false, "type": "array", "items": {"type": "string", "minLength": 1}}, {"name": "waiting_flag", "in": "query", "required": false, "type": "integer"}, {"name": "remote_code", "in": "query", "required": false, "type": "string", "minLength": 1}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/RecruitExploreResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/recruit/get-list-featured-job": {"get": {"operationId": "get_list_featured_jobs", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ListTopFeaturedJobsResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/recruit/recruitment-management-detail/{recruitment_id}": {"get": {"operationId": "get_recruitment_management_detail", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/RecruitmentManagementDetailsResponseModel"}}}, "tags": ["engineers"]}, "parameters": [{"name": "recruitment_id", "in": "path", "required": true, "type": "string"}]}, "/engineers/recruit/sign-contract/{recruitment_id}": {"get": {"operationId": "get_work_conditions", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SignContractResponseModel"}}}, "tags": ["engineers"]}, "patch": {"operationId": "update_recruit_progress", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["engineers"]}, "parameters": [{"name": "recruitment_id", "in": "path", "required": true, "type": "string"}]}, "/engineers/recruit/update-interview-datetime/": {"patch": {"operationId": "update_interview_datetime", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateInterviewDatetime"}}, {"name": "recruit_id", "in": "query", "required": true, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/update-data-policy/": {"patch": {"operationId": "update_data_policy", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/EngineerUpdateDataPolicy"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["engineers"]}, "parameters": []}, "/engineers/{user_id}/agency-company/": {"get": {"operationId": "get_user_agency_company", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/EngineerAgencyCompanyResponseModel"}}}, "tags": ["engineers"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/general-company/apply/details/": {"get": {"operationId": "get-apply-details", "description": "", "parameters": [{"name": "apply_id", "in": "query", "required": true, "type": "integer"}, {"name": "host_company_id", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyApplyDetailsResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/apply/download-employment-contract": {"post": {"operationId": "download_employee_contract", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GeneralCompanyParamGetContractDetails"}}], "responses": {"200": {"description": "", "schema": {"type": "file", "format": "binary"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/apply/get-calendar-interview": {"get": {"operationId": "get_calendar_interview", "description": "", "parameters": [{"name": "date", "in": "query", "required": true, "type": "string", "format": "date"}, {"name": "host_company_id", "in": "query", "required": false, "type": "integer", "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyCalendarInterviewResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/apply/get-contract-details": {"get": {"operationId": "get_company_contract_details", "description": "", "parameters": [{"name": "apply_id", "in": "query", "required": true, "type": "integer"}, {"name": "host_company_id", "in": "query", "required": false, "type": "integer", "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyContractDetailsResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/apply/reject/": {"post": {"operationId": "reject_apply", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GeneralCompanyRejectApply"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/apply/request-interview": {"post": {"operationId": "accept_apply", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GeneralCompanyRequestInterviewApply"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/apply/update-interview-datetime": {"post": {"operationId": "update_interview_date", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GeneralCompanyUpdateInterviewDateTime"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/details/": {"get": {"operationId": "company_details", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyResponseModel"}}}, "tags": ["general-company"]}, "patch": {"operationId": "update_company", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GeneralCompanyUpdate"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/engineer-details/get-communication-skills-self-assessment-engineer/{user_id}/": {"get": {"operationId": "get_communication_skills_self_assessment_engineer", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AssessmentQuestionsResponseModel"}}}, "tags": ["general-company"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/general-company/engineer-details/get-global-responsiveness-skills-engineer/{user_id}/": {"get": {"operationId": "get_global_responsiveness_skills_engineer", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AssessmentQuestionsResponseModel"}}}, "tags": ["general-company"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/general-company/engineer-details/get-project-management-skills-self-evaluation-engineer/{user_id}/": {"get": {"operationId": "get_project_management_skills_self_evaluation_engineer", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AssessmentQuestionsResponseModel"}}}, "tags": ["general-company"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/general-company/engineer-details/get-remote-work-skills-engineer/{user_id}/": {"get": {"operationId": "get_remote_work_skills_engineer", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AssessmentQuestionsResponseModel"}}}, "tags": ["general-company"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/general-company/engineer-details/get-reporting-consultation-skills-self-evaluation-engineer/{user_id}/": {"get": {"operationId": "get_reporting_consultation_skills_self_evaluation_engineer", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AssessmentQuestionsResponseModel"}}}, "tags": ["general-company"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/general-company/engineer-details/{user_id}/": {"get": {"operationId": "get_user_details", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/UserExploreDetailsResponseModel"}}}, "tags": ["general-company"]}, "parameters": [{"name": "user_id", "in": "path", "required": true, "type": "string"}]}, "/general-company/explore/": {"get": {"operationId": "explore_users", "description": "", "parameters": [{"name": "filter_id", "in": "query", "required": false, "type": "integer"}, {"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "page", "in": "query", "required": false, "type": "integer"}, {"name": "ordering", "in": "query", "required": false, "type": "string"}, {"name": "show_favorite", "in": "query", "required": false, "type": "boolean", "x-nullable": true}, {"name": "remote_work_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "global_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "communication_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "horenso_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "project_management_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "payroll_price_from", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "payroll_price_to", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "global_work_exp", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "search_type", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "search_query", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "career_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "skills", "in": "query", "required": false, "type": "array", "items": {"type": "string", "x-nullable": true}, "x-nullable": true}, {"name": "recruit_id", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "age_from", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "age_to", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "sex_type", "in": "query", "required": false, "type": "string", "maxLength": 1, "x-nullable": true}, {"name": "country_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "address_code1", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "address_code2", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "last_academic_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "language_code1", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "language_level_type1", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "language_code2", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "language_level_type2", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "experienced_job_code1", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "years_of_experience1", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "licence_code1", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "licence_point1", "in": "query", "required": false, "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, {"name": "licence_code2", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "licence_point2", "in": "query", "required": false, "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, {"name": "licence_code3", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "licence_point3", "in": "query", "required": false, "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, {"name": "recruiting_job_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "recruiting_employ_code", "in": "query", "required": false, "type": "string", "maxLength": 2, "x-nullable": true}, {"name": "work_place_code1", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "work_place_code2", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "work_place_code3", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "payroll_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "agent_fee", "in": "query", "required": false, "type": "string", "format": "decimal", "x-nullable": true}, {"name": "agent_fee_curr_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyListUserExploreResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/get-explore-user-count": {"get": {"operationId": "get_explore_user_count", "description": "", "parameters": [{"name": "filter_id", "in": "query", "required": false, "type": "integer"}, {"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "page", "in": "query", "required": false, "type": "integer"}, {"name": "ordering", "in": "query", "required": false, "type": "string"}, {"name": "show_favorite", "in": "query", "required": false, "type": "boolean", "x-nullable": true}, {"name": "remote_work_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "global_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "communication_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "horenso_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "project_management_skill_point_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "payroll_price_from", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "payroll_price_to", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "global_work_exp", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "search_type", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "search_query", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "career_type", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "skills", "in": "query", "required": false, "type": "array", "items": {"type": "string", "x-nullable": true}, "x-nullable": true}, {"name": "recruit_id", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}, {"name": "age_from", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "age_to", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "sex_type", "in": "query", "required": false, "type": "string", "maxLength": 1, "x-nullable": true}, {"name": "country_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "address_code1", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "address_code2", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "last_academic_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "language_code1", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "language_level_type1", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "language_code2", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "language_level_type2", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "experienced_job_code1", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "years_of_experience1", "in": "query", "required": false, "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, {"name": "licence_code1", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "licence_point1", "in": "query", "required": false, "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, {"name": "licence_code2", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "licence_point2", "in": "query", "required": false, "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, {"name": "licence_code3", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "licence_point3", "in": "query", "required": false, "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, {"name": "recruiting_job_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "recruiting_employ_code", "in": "query", "required": false, "type": "string", "maxLength": 2, "x-nullable": true}, {"name": "work_place_code1", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "work_place_code2", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "work_place_code3", "in": "query", "required": false, "type": "string", "maxLength": 6, "x-nullable": true}, {"name": "payroll_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}, {"name": "agent_fee", "in": "query", "required": false, "type": "string", "format": "decimal", "x-nullable": true}, {"name": "agent_fee_curr_code", "in": "query", "required": false, "type": "string", "maxLength": 3, "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyGetUserExploreCountResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/get-list-group-chat/": {"get": {"operationId": "get_list_group_chat", "description": "", "parameters": [{"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "cursor", "in": "query", "required": false, "type": "string"}, {"name": "ordering", "in": "query", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyListGroupChatResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/list-applied-engineers/": {"get": {"operationId": "list_applied_engineers", "description": "", "parameters": [{"name": "recruit_ids", "in": "query", "required": false, "type": "array", "items": {"type": "integer"}, "x-nullable": true}, {"name": "ordering", "in": "query", "required": false, "type": "string"}, {"name": "apply_status_filter_codes", "in": "query", "required": false, "type": "array", "items": {"type": "integer"}, "x-nullable": true}, {"name": "search", "in": "query", "required": false, "type": "string"}, {"name": "host_company_id", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "page", "in": "query", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyListUserAppliedCompanyResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/register/": {"post": {"operationId": "register_company", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GeneralCompanyRegister"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/save-filter/": {"post": {"operationId": "save_filter", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GeneralCompanySaveFilter"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/saved-filter/{filter_id}/": {"delete": {"operationId": "delete_filter", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["general-company"]}, "parameters": [{"name": "filter_id", "in": "path", "required": true, "type": "string"}]}, "/general-company/saved-filters/": {"get": {"operationId": "get_saved_filters", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyListFilterResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general-company/update-favorite": {"post": {"operationId": "add-favorite-user", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GeneralCompanyUpdateFavoriteUser"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["general-company"]}, "parameters": []}, "/general/convert-currency/": {"post": {"operationId": "convert_currency", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ConvertCurrency"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["general"]}, "parameters": []}, "/general/current_language/": {"get": {"operationId": "general_current_language_list", "description": "", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["general"]}, "parameters": []}, "/general/dummy-user-chat/": {"get": {"operationId": "general_dummy-user-chat_list", "description": "Dummy view to display UserChatSerializer in Swagger UI", "parameters": [], "responses": {"200": {"description": "", "schema": {"type": "array", "items": {"$ref": "#/definitions/UserChat"}}}}, "tags": ["general"]}, "parameters": []}, "/general/get-video-social-source/": {"get": {"operationId": "get_video_social_source", "description": "", "parameters": [{"name": "link", "in": "query", "required": true, "type": "string", "minLength": 1}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GetVideoSocialSourceResponseModel"}}}, "tags": ["general"]}, "parameters": []}, "/general/support_language/": {"get": {"operationId": "support_language", "description": "", "parameters": [{"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"support": {"type": "array", "items": {"type": "string"}}}}}}, "tags": ["general"]}, "parameters": []}, "/host-companies/count-unresolved-issues/": {"get": {"operationId": "count_unresolved_issues", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/HostCompanyListUserAppliedCompanyResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/list-recruitment-title/": {"get": {"operationId": "list_recruitment_title", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/HostCompanyListUserAppliedCompanyResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/list-support-company-subscribed": {"get": {"operationId": "get_support_company_subscribed", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/HostCompanyListSupportCompanyResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/list-support-company/": {"get": {"operationId": "get_list_support_company", "description": "", "parameters": [{"name": "email", "in": "query", "required": false, "type": "string", "minLength": 1, "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/HostCompanyListSupportCompanyResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/recruit/company-recruit/{recruitment_id}/": {"get": {"operationId": "get_company_recruitment_details", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CompanyMyRecruitmentDetailsResponseModel"}}}, "tags": ["host-companies"]}, "patch": {"operationId": "update_company_recruitment_details", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateRecruitment"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["host-companies"]}, "parameters": [{"name": "recruitment_id", "in": "path", "required": true, "type": "string"}]}, "/host-companies/recruit/create/": {"post": {"operationId": "create_recruitment", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateRecruitment"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/recruit/delete/": {"delete": {"operationId": "delete_recruitment", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DeleteRecruitment"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/recruit/list-recruits-uploaded/": {"get": {"operationId": "list_recruits_uploaded", "description": "", "parameters": [{"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "cursor", "in": "query", "required": false, "type": "string", "x-nullable": true}, {"name": "ordering", "in": "query", "required": false, "type": "string"}, {"name": "show_old_post", "in": "query", "required": true, "type": "boolean"}, {"name": "display_flag", "in": "query", "required": false, "type": "integer", "x-nullable": true}, {"name": "engineer_id", "in": "query", "required": false, "type": "integer", "x-nullable": true}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ListRecruitUploadedResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/recruit/{recruitment_id}/": {"get": {"operationId": "get_recruitment_details", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/RecruitmentDetailsResponseModel"}}}, "tags": ["host-companies"]}, "parameters": [{"name": "recruitment_id", "in": "path", "required": true, "type": "string"}]}, "/host-companies/request-interview/": {"post": {"operationId": "request_interview", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/HostCompanyRequestInterview"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyRequestInterviewApplyResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/subscribe-support-company": {"post": {"operationId": "subscribe_support_company", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/HostCompanySubscribeSupportCompany"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/unsubscribe-support-company": {"post": {"operationId": "unsubscribe_support_company", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/HostCompanyUnsubscribeSupportCompany"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/host-companies/update-apply-admission": {"post": {"operationId": "update_apply_admission", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/HostCompanyUpdateInterviewAdmission"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["host-companies"]}, "parameters": []}, "/image/upload-avatar-engineer-by-admin": {"post": {"operationId": "Admin upload avatar", "description": "Upload image and compress it", "parameters": [{"name": "image", "in": "formData", "description": "Image file to upload", "required": true, "type": "file"}, {"name": "engineer_id", "in": "formData", "description": "Engineer ID", "required": true, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/MediaResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "delete": {"operationId": "Ad<PERSON> Delete avatar engineer", "description": "Delete user avatar", "parameters": [{"name": "engineer_id", "in": "formData", "description": "Engineer ID", "required": true, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "parameters": []}, "/image/upload-avatar/": {"post": {"operationId": "Upload Image", "description": "Upload image and compress it", "parameters": [{"name": "image", "in": "formData", "description": "Image file to upload", "required": true, "type": "file"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/MediaResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "delete": {"operationId": "Delete avatar", "description": "Delete user avatar", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "parameters": []}, "/image/upload-company-logo/": {"post": {"operationId": "upload-company-logo", "description": "Upload image and compress it", "parameters": [{"name": "image", "in": "formData", "description": "Image file to upload", "required": true, "type": "file"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/MediaResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "parameters": []}, "/image/upload-company-pr/": {"post": {"operationId": "upload-company-pr", "description": "Upload image and compress it", "parameters": [{"name": "image", "in": "formData", "description": "Image file to upload", "required": true, "type": "file"}, {"name": "index", "in": "formData", "description": "Index of the image to upload(0,1,2)", "required": true, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/MediaResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "parameters": []}, "/image/upload-contract-company-signature/": {"post": {"operationId": "Upload Company Contract Image", "description": "Upload image and compress it", "parameters": [{"name": "image", "in": "formData", "description": "Image file to upload", "required": true, "type": "file"}, {"name": "apply_id", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/MediaResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "parameters": []}, "/image/upload-contract-signature/": {"post": {"operationId": "Upload Contract Image", "description": "Upload image and compress it", "parameters": [{"name": "image", "in": "formData", "description": "Image file to upload", "required": true, "type": "file"}, {"name": "recruit_id", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/MediaResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "parameters": []}, "/image/upload-passport/": {"post": {"operationId": "Upload Passport Image", "description": "Upload image and compress it", "parameters": [{"name": "image", "in": "formData", "description": "Image file to upload", "required": true, "type": "file"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/MediaResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "delete": {"operationId": "Delete Passport Image", "description": "Delete passport image", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "parameters": []}, "/image/upload-recruit-cover/": {"post": {"operationId": "upload-recruit-cover", "description": "Upload image and compress it", "parameters": [{"name": "image", "in": "formData", "description": "Image file to upload", "required": true, "type": "file"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/MediaResponseModel"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["image"]}, "parameters": []}, "/notify/get-count-unread-notify": {"get": {"operationId": "get_count_unread_notify", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CountUnreadNotifyResponseModel"}}}, "tags": ["notify"]}, "parameters": []}, "/notify/get-list-notify": {"get": {"operationId": "get_list_notify", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ListNotifyResponseModel"}}}, "tags": ["notify"]}, "parameters": []}, "/notify/set-read-all-notify": {"post": {"operationId": "set_read_all_notify", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SetReadNotifyResponseModel"}}}, "tags": ["notify"]}, "parameters": []}, "/notify/set-read-notify": {"post": {"operationId": "set_read_notify", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SetReadNotify"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SetReadNotifyResponseModel"}}}, "tags": ["notify"]}, "parameters": []}, "/profile/": {"get": {"operationId": "get_profile", "description": "", "parameters": [{"name": "optional_engineer_id", "in": "query", "required": false, "type": "integer"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/UserDetailsResponseModel"}}}, "tags": ["profile"]}, "patch": {"operationId": "update_profile", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateUser"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/UpdateUserResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/download-cv/": {"post": {"operationId": "download_cv", "description": "Download cv", "parameters": [], "responses": {"200": {"description": "", "schema": {"type": "file", "format": "binary"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/get-communication-skills-self-assessment/": {"get": {"operationId": "get_communication_skills_self_assessment", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SelfAssessmentResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/get-global-responsiveness-skills/": {"get": {"operationId": "get_global_responsiveness_skills", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SelfAssessmentResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/get-project-management-skills-self-evaluation/": {"get": {"operationId": "get_project_management_skills_self_evaluation", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SelfAssessmentResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/get-remote-work-skills/": {"get": {"operationId": "get_remote_work_skills", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SelfAssessmentResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/get-reporting-consultation-skills-self-evaluation/": {"get": {"operationId": "get_reporting_consultation_skills_self_evaluation", "description": "", "parameters": [], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SelfAssessmentResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/save-communication-skills-self-assessment/": {"post": {"operationId": "save_communication_skills_self_assessment", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SelfAssessmentAnswer"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/save-cv-uploaded/": {"post": {"operationId": "Save CV uploaded", "description": "Save CV uploaded", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SaveCVUploaded"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SaveCVUploadedResponse"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/save-global-responsiveness-skills/": {"post": {"operationId": "save_global_responsiveness_skills", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SelfAssessmentAnswer"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/save-project-management-skills-self-evaluation/": {"post": {"operationId": "save_project_management_skills_self_evaluation", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SelfAssessmentAnswer"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/save-reporting-communication-skills/": {"post": {"operationId": "save_remote_work_skills", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SelfAssessmentAnswer"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/save-reporting-consultation-skills-self-evaluation/": {"post": {"operationId": "save_reporting_consultation_skills_self_evaluation", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SelfAssessmentAnswer"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/update-password/": {"put": {"operationId": "update-password", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdatePassword"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResponseModel"}}}, "tags": ["profile"]}, "parameters": []}, "/profile/upload-cv-ai/": {"post": {"operationId": "Upload CV PDF", "description": "Upload CV PDF", "parameters": [{"name": "cv_pdf", "in": "formData", "description": "CV PDF to upload", "required": true, "type": "file"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/UploadCVUploadedResponse"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["profile"]}, "parameters": []}, "/profile/upload-cv-test/": {"post": {"operationId": "Upload CV Test", "description": "Upload CV Test", "parameters": [{"name": "cv_pdf", "in": "formData", "description": "CV PDF to upload", "required": true, "type": "file"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/UploadCVUploadedResponse"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["profile"]}, "parameters": []}, "/profile/upload-cv-v2/": {"post": {"operationId": "Upload CV PDF V2", "description": "Upload CV PDF V2", "parameters": [{"name": "cv_pdf", "in": "formData", "description": "CV PDF to upload", "required": true, "type": "file"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/UploadCVUploadedResponse"}}}, "consumes": ["multipart/form-data", "application/x-www-form-urlencoded"], "tags": ["profile"]}, "parameters": []}, "/support-agencies/host/get-list-recruit-host-company/": {"get": {"operationId": "get_list_recruit_of_host_company", "description": "", "parameters": [{"name": "host_company_id", "in": "query", "required": true, "type": "integer"}, {"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "cursor", "in": "query", "required": false, "type": "string", "x-nullable": true}, {"name": "ordering", "in": "query", "required": false, "type": "string"}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ListRecruitUploadedResponseModel"}}}, "tags": ["support-agencies"]}, "parameters": []}, "/support-agencies/host/get-list-registered-company/": {"get": {"operationId": "get_list_registered_company", "description": "", "parameters": [{"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SPListRegisteredCompanyResponseModel"}}}, "tags": ["support-agencies"]}, "parameters": []}, "/support-agencies/host/request_interview/": {"post": {"operationId": "support_request_interview", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SPRequestInterviewBody"}}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/GeneralCompanyRequestInterviewApplyResponseModel"}}}, "tags": ["support-agencies"]}, "parameters": []}, "/support-agencies/host/support-company/get-list-manage-host-company/": {"get": {"operationId": "get_list_manage_host_company", "description": "", "parameters": [{"name": "page_size", "in": "query", "required": false, "type": "integer"}, {"name": "cursor", "in": "query", "required": false, "type": "string", "x-nullable": true}, {"name": "sort_by", "in": "query", "required": false, "type": "string"}, {"name": "progress_status", "in": "query", "required": false, "type": "string"}, {"name": "search", "in": "query", "required": false, "type": "string"}, {"name": "Accept-Language", "in": "header", "description": "en,ja,etc", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ListManageHostCompanyResponseModel"}}}, "tags": ["support-agencies"]}, "parameters": []}, "/token/refresh/": {"post": {"operationId": "token_refresh_create", "description": "", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/TokenRefresh"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/TokenRefresh"}}}, "tags": ["token"]}, "parameters": []}, "/token/verify/": {"post": {"operationId": "token_verify_create", "description": "Takes a token and indicates if it is valid.  This view provides no\ninformation about a token's fitness for a particular use.", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/TokenVerify"}}], "responses": {"201": {"description": "", "schema": {"$ref": "#/definitions/TokenVerify"}}}, "tags": ["token"]}, "parameters": []}}, "definitions": {"CreateEmailSchedule": {"type": "object", "properties": {"type": {"title": "Type", "type": "integer", "x-nullable": true}, "subject": {"title": "Subject", "type": "string", "minLength": 1, "x-nullable": true}, "body": {"title": "Body", "type": "string", "minLength": 1, "x-nullable": true}, "weekday": {"title": "Weekday", "type": "string", "minLength": 1, "x-nullable": true}, "send_time": {"title": "Send time", "type": "string", "minLength": 1, "x-nullable": true}, "send_datetime": {"title": "Send datetime", "type": "string", "format": "date-time", "x-nullable": true}, "is_valid": {"title": "Is valid", "type": "integer", "x-nullable": true}, "is_repeat": {"title": "Is repeat", "type": "integer", "x-nullable": true}, "target_email": {"title": "Target email", "type": "string", "minLength": 1, "x-nullable": true}}}, "EmailSchedule": {"type": "object", "properties": {"id": {"title": "ID", "type": "integer", "readOnly": true}, "type": {"title": "Type", "type": "integer", "maximum": 65535, "minimum": 0, "x-nullable": true}, "subject": {"title": "Subject", "type": "string", "maxLength": 255, "x-nullable": true}, "body": {"title": "Body", "type": "string", "x-nullable": true}, "weekday": {"title": "Weekday", "type": "string", "maxLength": 255, "x-nullable": true}, "send_time": {"title": "Send time", "type": "string", "maxLength": 255, "x-nullable": true}, "send_datetime": {"title": "Send datetime", "type": "string", "format": "date-time", "x-nullable": true}, "is_valid": {"title": "Is valid", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "is_deleted": {"title": "Is deleted", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "is_repeat": {"title": "Is repeat", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "x-nullable": true}}}, "ErrorDetail": {"required": ["message", "title", "field", "code"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "title": {"title": "Title", "type": "string", "minLength": 1, "x-nullable": true}, "field": {"title": "Field", "type": "string", "minLength": 1, "x-nullable": true}, "code": {"title": "Code", "type": "integer", "x-nullable": true}}}, "ResponseModel": {"required": ["message", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"title": "Data", "type": "object", "additionalProperties": {"type": "string", "x-nullable": true}, "default": {}, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "DeleteEngineers": {"required": ["engineer_ids"], "type": "object", "properties": {"engineer_ids": {"type": "array", "items": {"type": "integer"}}}}, "ExportUserDataBody": {"required": ["engineer_ids", "export_type"], "type": "object", "properties": {"engineer_ids": {"type": "array", "items": {"type": "integer"}}, "export_type": {"title": "Export type", "type": "string", "minLength": 1}}}, "GetEmailDetailResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/EmailSchedule"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "EngSkill": {"required": ["engineer_id"], "type": "object", "properties": {"engineer_id": {"title": "Engineer id", "type": "integer"}, "skill_code": {"title": "Skill code", "type": "string", "maxLength": 6, "x-nullable": true}, "level_type": {"title": "Level type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_id": {"title": "Skill id", "type": "integer", "readOnly": true}, "skill_name": {"title": "Skill name", "type": "string", "readOnly": true}, "level_type_name": {"title": "Level type name", "type": "string", "readOnly": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "temp_name": {"title": "Temp name", "type": "string", "minLength": 1, "x-nullable": true}, "temp_category_id": {"title": "Temp category id", "type": "integer", "x-nullable": true}}}, "HopeJobSkill": {"type": "object", "properties": {"job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "skill_code": {"title": "Skill code", "type": "string", "maxLength": 6, "x-nullable": true}}}, "HopeCategorySkill": {"required": ["category_id", "skills"], "type": "object", "properties": {"category_id": {"title": "Category id", "type": "string", "minLength": 1}, "skills": {"type": "array", "items": {"$ref": "#/definitions/HopeJobSkill"}}}}, "EngHope": {"required": ["category_skills"], "type": "object", "properties": {"employ_code": {"title": "Employ code", "type": "string", "maxLength": 2, "x-nullable": true}, "place_code1": {"title": "Place code1", "type": "string", "maxLength": 9, "x-nullable": true}, "place_code2": {"title": "Place code2", "type": "string", "maxLength": 9, "x-nullable": true}, "place_code3": {"title": "Place code3", "type": "string", "maxLength": 9, "x-nullable": true}, "payroll_price": {"title": "Payroll price", "type": "string", "readOnly": true}, "payroll_code": {"title": "Payroll code", "type": "string", "readOnly": true}, "job_skills": {"type": "array", "items": {"$ref": "#/definitions/HopeJobSkill"}, "readOnly": true}, "place_code1_name": {"title": "Place code1 name", "type": "string", "readOnly": true}, "place_code2_name": {"title": "Place code2 name", "type": "string", "readOnly": true}, "place_code3_name": {"title": "Place code3 name", "type": "string", "readOnly": true}, "country_place_code1_name": {"title": "Country place code1 name", "type": "string", "readOnly": true}, "country_place_code2_name": {"title": "Country place code2 name", "type": "string", "readOnly": true}, "country_place_code3_name": {"title": "Country place code3 name", "type": "string", "readOnly": true}, "remote_code": {"title": "Remote code", "type": "string", "maxLength": 2, "x-nullable": true}, "category_skills": {"type": "array", "items": {"$ref": "#/definitions/HopeCategorySkill"}}, "payroll_price_usd": {"title": "Payroll price usd", "type": "string", "format": "decimal", "x-nullable": true}}}, "EngLanguage": {"required": ["engineer_id"], "type": "object", "properties": {"engineer_id": {"title": "Engineer id", "type": "integer"}, "language_level_type": {"title": "Language level type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "language_id": {"title": "Language id", "type": "integer", "readOnly": true}, "language_code": {"title": "Language code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "language_name": {"title": "Language name", "type": "string", "readOnly": true}, "language_level_name": {"title": "Language level name", "type": "string", "readOnly": true}}}, "GetListEngineer": {"required": ["email", "employment_status", "sales_memo", "is_data_policy_accept"], "type": "object", "properties": {"profile_image_path": {"title": "Profile image path", "type": "string", "maxLength": 200, "minLength": 1, "x-nullable": true}, "email": {"title": "Email", "type": "string", "format": "email", "maxLength": 100, "minLength": 1}, "first_name": {"title": "First name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "last_name": {"title": "Last name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time"}, "created_user": {"title": "Created user", "type": "string", "maxLength": 60, "x-nullable": true}, "skills": {"type": "array", "items": {"$ref": "#/definitions/EngSkill"}, "readOnly": true}, "user_id": {"title": "User id", "type": "integer", "readOnly": true}, "last_academic_name": {"title": "Last academic name", "type": "string", "readOnly": true}, "facebook_url": {"title": "Facebook url", "type": "string", "minLength": 1, "x-nullable": true}, "linkedin_url": {"title": "Linkedin url", "type": "string", "minLength": 1, "x-nullable": true}, "whatsapp_url": {"title": "Whatsapp url", "type": "string", "x-nullable": true}, "zalo_id": {"title": "<PERSON><PERSON>", "type": "string", "maxLength": 50, "x-nullable": true}, "requirements": {"$ref": "#/definitions/EngHope"}, "languages": {"type": "array", "items": {"$ref": "#/definitions/EngLanguage"}, "readOnly": true}, "employment_status": {"title": "Employment status", "type": "integer", "x-nullable": true}, "sales_memo": {"title": "Sales memo", "type": "string", "x-nullable": true}, "is_data_policy_accept": {"title": "Is data policy accept", "type": "integer", "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "minLength": 1, "x-nullable": true}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}}}, "PagingGetListEngineer": {"required": ["next", "previous", "results", "total_count"], "type": "object", "properties": {"next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/GetListEngineer"}}, "total_count": {"title": "Total count", "type": "integer"}}}, "GetListEngineerResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/PagingGetListEngineer"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "PagingEmailSchedule": {"required": ["next", "previous", "results", "total_count"], "type": "object", "properties": {"next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/EmailSchedule"}}, "total_count": {"title": "Total count", "type": "integer"}}}, "GetEmailScheduleResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/PagingEmailSchedule"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "GetListRegistrarResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"type": "array", "items": {"type": "string", "minLength": 1}}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "UpdateEngineer": {"type": "object", "properties": {"employment_status": {"title": "Employment status", "type": "integer", "x-nullable": true}, "sales_memo": {"title": "Sales memo", "type": "string", "x-nullable": true}}}, "Captcha": {"required": ["key", "image_url"], "type": "object", "properties": {"key": {"title": "Key", "type": "string", "minLength": 1}, "image_url": {"title": "Image url", "type": "string", "format": "uri", "minLength": 1}}}, "CaptchaResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/Captcha"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "CheckCaptchaRequireInLogin": {"required": ["email"], "type": "object", "properties": {"email": {"title": "Email", "type": "string", "format": "email", "minLength": 1}}}, "CaptchaAndUserStatus": {"required": ["require_captcha", "is_user_verified"], "type": "object", "properties": {"require_captcha": {"title": "Require captcha", "type": "boolean"}, "is_user_verified": {"title": "Is user verified", "type": "boolean"}}}, "CheckCaptchaRequireInLoginResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/CaptchaAndUserStatus"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "ConfirmLogin": {"required": ["email", "code", "password"], "type": "object", "properties": {"email": {"title": "Email", "type": "string", "minLength": 1}, "code": {"title": "Code", "type": "string", "maxLength": 6, "minLength": 1}, "password": {"title": "Password", "type": "string", "maxLength": 128}}}, "ConfirmWhatsappCode": {"required": ["phone_number", "code"], "type": "object", "properties": {"phone_number": {"title": "Phone number", "type": "string", "minLength": 1}, "code": {"title": "Code", "type": "string", "minLength": 1}}}, "LoginWithSNS": {"required": ["sns_type"], "type": "object", "properties": {"sns_type": {"title": "Sns type", "type": "string", "minLength": 1}, "code": {"title": "Code", "type": "string", "minLength": 1, "x-nullable": true}, "redirect_uri": {"title": "Redirect uri", "type": "string", "minLength": 1, "x-nullable": true}, "code_verifier": {"title": "Code verifier", "type": "string", "minLength": 1, "x-nullable": true}, "whatsapp_number": {"title": "Whatsapp number", "type": "string", "minLength": 1, "x-nullable": true}}}, "LoginWithSNSResponseData": {"required": ["access_token", "refresh_token", "user_type", "email"], "type": "object", "properties": {"access_token": {"title": "Access token", "type": "string", "minLength": 1, "x-nullable": true}, "refresh_token": {"title": "Refresh token", "type": "string", "minLength": 1, "x-nullable": true}, "user_type": {"title": "User type", "type": "string", "minLength": 1, "x-nullable": true}, "email": {"title": "Email", "type": "string", "format": "email", "minLength": 1, "x-nullable": true}}}, "LoginWithSNSResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/LoginWithSNSResponseData"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "Login": {"required": ["email", "password", "captcha_key", "captcha_value"], "type": "object", "properties": {"email": {"title": "Email", "type": "string", "format": "email", "minLength": 1}, "password": {"title": "Password", "type": "string", "maxLength": 128, "minLength": 1}, "captcha_key": {"title": "<PERSON><PERSON> key", "type": "string", "minLength": 1, "x-nullable": true}, "captcha_value": {"title": "Captcha value", "type": "string", "minLength": 1, "x-nullable": true}, "user_type": {"title": "User type", "type": "integer", "x-nullable": true}}}, "LoginSuccess": {"required": ["captcha_key", "captcha_image_url", "is_email_verified", "refresh", "access"], "type": "object", "properties": {"captcha_key": {"title": "<PERSON><PERSON> key", "type": "string", "minLength": 1, "x-nullable": true}, "captcha_image_url": {"title": "Captcha image url", "type": "string", "minLength": 1, "x-nullable": true}, "is_email_verified": {"title": "Is email verified", "type": "boolean"}, "refresh": {"title": "Refresh", "type": "string", "minLength": 1, "x-nullable": true}, "access": {"title": "Access", "type": "string", "minLength": 1, "x-nullable": true}}}, "LoginSuccessResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/LoginSuccess"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "Logout": {"required": ["refresh"], "type": "object", "properties": {"refresh": {"title": "Refresh", "type": "string", "minLength": 1}}}, "Register": {"required": ["email", "password"], "type": "object", "properties": {"email": {"title": "Email", "type": "string", "format": "email", "minLength": 1}, "password": {"title": "Password", "type": "string", "maxLength": 128, "minLength": 1}}}, "SendCodeRequest": {"required": ["email", "captcha_key", "captcha_value", "sns_email"], "type": "object", "properties": {"email": {"title": "Email", "type": "string", "format": "email", "minLength": 1}, "captcha_key": {"title": "<PERSON><PERSON> key", "type": "string", "minLength": 1}, "captcha_value": {"title": "Captcha value", "type": "string", "minLength": 1}, "sns_email": {"title": "Sns email", "type": "string", "format": "email", "minLength": 1, "x-nullable": true}}}, "ResetPasswordConfirm": {"required": ["token"], "type": "object", "properties": {"token": {"title": "Token", "type": "string", "minLength": 1}}}, "ResetPassword": {"required": ["email"], "type": "object", "properties": {"email": {"title": "Email", "type": "string", "format": "email", "minLength": 1}}}, "SendWhatsappCode": {"required": ["phone_number"], "type": "object", "properties": {"phone_number": {"title": "Phone number", "type": "string", "minLength": 1}}}, "VerifyEmail": {"required": ["token"], "type": "object", "properties": {"token": {"title": "Token", "type": "string", "minLength": 1}}}, "MarkReadAllMessagesResponseModel": {"required": ["message", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"title": "Data", "type": "boolean", "default": true, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "EngineerUpdateAgencyCompany": {"required": ["contact_mail"], "type": "object", "properties": {"optional_engineer_id": {"title": "Optional engineer id", "type": "integer"}, "contact_mail": {"title": "Contact mail", "type": "string", "format": "email", "minLength": 1}}}, "EngineerRemoveAgencyCompany": {"required": ["company_id"], "type": "object", "properties": {"optional_engineer_id": {"title": "Optional engineer id", "type": "integer"}, "company_id": {"title": "Company id", "type": "integer"}}}, "EngineerBestCompany": {"type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}}}, "EngineerListBestCompanyPagination": {"required": ["next", "previous", "results"], "type": "object", "properties": {"next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/EngineerBestCompany"}}}}, "EngineerGetListBestCompaniesResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/EngineerListBestCompanyPagination"}}}, "EngineerListAgencyCompany": {"required": ["user_type"], "type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "user_type": {"title": "User type", "type": "integer", "maximum": **********, "minimum": -**********}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "contact_mail": {"title": "Contact mail", "type": "string", "maxLength": 100, "x-nullable": true}, "agent_fee": {"title": "Agent fee", "type": "string", "format": "decimal", "x-nullable": true}, "agent_fee_curr_code": {"title": "Agent fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}}}, "EngineerListAgencyCompanyResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/EngineerListAgencyCompany"}}}}, "EngineerListApplyCompanyInformation": {"type": "object", "properties": {"name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}}, "x-nullable": true}, "EngineerListApplyCompany": {"required": ["is_closed", "is_read"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer", "x-nullable": true}, "engineer_id": {"title": "Engineer id", "type": "integer", "x-nullable": true}, "recruit_id": {"title": "Recruit id", "type": "integer", "x-nullable": true}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "readOnly": true}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "readOnly": true}, "payroll_code": {"title": "Payroll code", "type": "string", "readOnly": true}, "host_company_id": {"title": "Host company id", "type": "integer", "x-nullable": true}, "recruit_progress_code": {"title": "Recruit progress code", "type": "integer", "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "progress_update_datetime": {"title": "Progress update datetime", "type": "string", "format": "date-time", "x-nullable": true}, "is_closed": {"title": "Is closed", "type": "boolean", "x-nullable": true}, "is_read": {"title": "Is read", "type": "boolean", "x-nullable": true}, "host_company": {"$ref": "#/definitions/EngineerListApplyCompanyInformation"}}}, "EngineerListApplyCompanyPagination": {"required": ["next", "previous", "results"], "type": "object", "properties": {"next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/EngineerListApplyCompany"}}}}, "EngineerListApplyCompanyResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/EngineerListApplyCompanyPagination"}}}, "BoolResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"title": "Data", "type": "boolean"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "RecruitGetContractDetails": {"type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer", "readOnly": true}, "host_company_id": {"title": "Host company id", "type": "integer", "readOnly": true, "x-nullable": true}, "host_company_name": {"title": "Host company name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "host_agent_first_name": {"title": "Host agent first name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "host_agent_last_name": {"title": "Host agent last name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "host_agent_accept_sign_path": {"title": "Host agent accept sign path", "type": "string", "readOnly": true}, "support_company_id": {"title": "Support company id", "type": "integer", "readOnly": true, "x-nullable": true}, "support_company_name": {"title": "Support company name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "support_agent_first_name": {"title": "Support agent first name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "support_agent_last_name": {"title": "Support agent last name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "support_agent_accept_sign_path": {"title": "Support agent accept sign path", "type": "string", "readOnly": true}, "agency_company_id": {"title": "Agency company id", "type": "integer", "readOnly": true, "x-nullable": true}, "agency_company_name": {"title": "Agency company name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "agency_agent_first_name": {"title": "Agency agent first name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "agency_agent_last_name": {"title": "Agency agent last name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "agency_agent_accept_sign_path": {"title": "Agency agent accept sign path", "type": "string", "readOnly": true}, "engineer_id": {"title": "Engineer id", "type": "integer", "readOnly": true, "x-nullable": true}, "engineer_first_name": {"title": "Engineer first name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "engineer_last_name": {"title": "Engineer last name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "engineer_accept_sign_path": {"title": "Engineer accept sign path", "type": "string", "readOnly": true}}}, "RecruitContractDetailsResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/RecruitGetContractDetails"}}}, "RecruitCountFilterResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"title": "Data", "type": "integer"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "RecruitCompany": {"required": ["capital_stock", "capital_stock_curr_code"], "type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "about_us": {"title": "About us", "type": "string", "maxLength": 5000, "x-nullable": true}, "business_details": {"title": "Business details", "type": "string", "maxLength": 5000, "x-nullable": true}, "employees_type": {"title": "Employees type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "x-nullable": true}, "address": {"title": "Address", "type": "string", "maxLength": 200, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "pr_image_path1": {"title": "Pr image path1", "type": "string", "maxLength": 500, "x-nullable": true}, "pr_image_path2": {"title": "Pr image path2", "type": "string", "maxLength": 500, "x-nullable": true}, "pr_image_path3": {"title": "Pr image path3", "type": "string", "maxLength": 500, "x-nullable": true}, "contact_mail": {"title": "Contact mail", "type": "string", "maxLength": 100, "x-nullable": true}, "web_url": {"title": "Web url", "type": "string", "x-nullable": true}, "introduction_url": {"title": "Introduction url", "type": "string", "x-nullable": true}, "memo": {"title": "Memo", "type": "string", "minLength": 1, "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "benefits": {"title": "Benefits", "type": "string", "maxLength": 1000, "x-nullable": true}, "capital_stock": {"title": "Capital stock", "type": "string", "x-nullable": true}, "capital_stock_curr_code": {"title": "Capital stock curr code", "type": "string", "x-nullable": true}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "agent_fee": {"title": "Agent fee", "type": "string", "format": "decimal", "x-nullable": true}, "agent_fee_curr_code": {"title": "Agent fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "accepting_fee": {"title": "Accepting fee", "type": "string", "format": "decimal", "x-nullable": true}, "accepting_fee_curr_code": {"title": "Accepting fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "support_outsourcing_fee": {"title": "Support outsourcing fee", "type": "string", "format": "decimal", "x-nullable": true}, "support_outsourcing_fee_curr_code": {"title": "Support outsourcing fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "support": {"title": "Support", "type": "string", "maxLength": 5000, "x-nullable": true}, "status": {"title": "Status", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "introduction_pr": {"title": "Introduction pr", "type": "string", "readOnly": true}, "working_hours_from": {"title": "Working hours from", "type": "string", "x-nullable": true}, "working_hours_to": {"title": "Working hours to", "type": "string", "x-nullable": true}}}, "RecruitExplore": {"required": ["payroll_price_from", "payroll_price_to"], "type": "object", "properties": {"recruit_id": {"title": "Recruit id", "type": "integer", "readOnly": true}, "title": {"title": "Title", "type": "string", "minLength": 1, "x-nullable": true}, "catch_copy": {"title": "Catch copy", "type": "string", "x-nullable": true}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "minLength": 1}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "minLength": 1}, "payroll_code": {"title": "Payroll code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "start_date": {"title": "Start date", "type": "string", "format": "date-time", "x-nullable": true}, "end_date": {"title": "End date", "type": "string", "format": "date-time", "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "host_company": {"$ref": "#/definitions/RecruitCompany"}, "skill_job_code1": {"title": "Skill job code1", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_job_code2": {"title": "Skill job code2", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_job_code3": {"title": "Skill job code3", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "waiting_flag": {"title": "Waiting flag", "type": "string", "readOnly": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}}}, "PagingResponseModel": {"required": ["next", "previous", "results"], "type": "object", "properties": {"next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/RecruitExplore"}}}}, "RecruitExploreResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/PagingResponseModel"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "FeaturedJob": {"required": ["id", "name_jp", "name_en", "image", "total_jobs"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "minLength": 1}, "name_jp": {"title": "Name jp", "type": "string", "minLength": 1}, "name_en": {"title": "Name en", "type": "string", "minLength": 1}, "image": {"title": "Image", "type": "string", "minLength": 1}, "total_jobs": {"title": "Total jobs", "type": "integer"}}}, "ListTopFeaturedJobsResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/FeaturedJob"}}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "RecruitCompanyInformation": {"required": ["capital_stock", "capital_stock_curr_code"], "type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "pr_image_path1": {"title": "Pr image path1", "type": "string", "maxLength": 500, "x-nullable": true}, "pr_image_path2": {"title": "Pr image path2", "type": "string", "maxLength": 500, "x-nullable": true}, "pr_image_path3": {"title": "Pr image path3", "type": "string", "maxLength": 500, "x-nullable": true}, "introduction_url": {"title": "Introduction url", "type": "string", "x-nullable": true}, "address": {"title": "Address", "type": "string", "maxLength": 200, "x-nullable": true}, "capital_stock": {"title": "Capital stock", "type": "string", "x-nullable": true}, "employees_type": {"title": "Employees type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "x-nullable": true}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "contact_mail": {"title": "Contact mail", "type": "string", "maxLength": 100, "x-nullable": true}, "about_us": {"title": "About us", "type": "string", "maxLength": 5000, "x-nullable": true}, "business_details": {"title": "Business details", "type": "string", "maxLength": 5000, "x-nullable": true}, "benefits": {"title": "Benefits", "type": "string", "maxLength": 1000, "x-nullable": true}, "capital_stock_curr_code": {"title": "Capital stock curr code", "type": "string", "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "x-nullable": true}, "web_url": {"title": "Web url", "type": "string", "x-nullable": true}}}, "RecRecruit": {"required": ["host_company_id", "host_company"], "type": "object", "properties": {"title": {"title": "Title", "type": "string", "minLength": 1, "x-nullable": true}, "recruit_id": {"title": "Recruit id", "type": "integer", "readOnly": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "catch_copy": {"title": "Catch copy", "type": "string", "x-nullable": true}, "host_company_id": {"title": "Host company id", "type": "integer"}, "host_company": {"$ref": "#/definitions/RecruitCompany"}}}, "RecruitmentManagementDetail": {"type": "object", "properties": {"recruit_id": {"title": "Recruit id", "type": "integer", "readOnly": true}, "title": {"title": "Title", "type": "string", "minLength": 1, "x-nullable": true}, "total_recruit": {"title": "Total recruit", "type": "integer", "readOnly": true}, "catch_copy": {"title": "Catch copy", "type": "string", "x-nullable": true}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "readOnly": true}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "readOnly": true}, "payroll_code": {"title": "Payroll code", "type": "string", "readOnly": true}, "start_date": {"title": "Start date", "type": "string", "format": "date-time", "x-nullable": true}, "end_date": {"title": "End date", "type": "string", "format": "date-time", "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "employ_code": {"title": "Employ code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "place_code1": {"title": "Place code1", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code2": {"title": "Place code2", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code3": {"title": "Place code3", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "pref_code1": {"title": "Pref code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code2": {"title": "Pref code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code3": {"title": "Pref code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "age_from": {"title": "Age from", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "age_to": {"title": "Age to", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "language_code1": {"title": "Language code1", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type1": {"title": "Language level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "language_code2": {"title": "Language code2", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type2": {"title": "Language level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "experienced_job_code": {"title": "Experienced job code", "type": "string", "maxLength": 3, "x-nullable": true}, "years_of_experience": {"title": "Years of experience", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code1": {"title": "Skill job code1", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code1": {"title": "Skill code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type1": {"title": "Skill level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code2": {"title": "Skill job code2", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code2": {"title": "Skill code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type2": {"title": "Skill level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code3": {"title": "Skill job code3", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code3": {"title": "Skill code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type3": {"title": "Skill level type3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "content": {"title": "Content", "type": "string", "maxLength": 10000, "minLength": 1, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "licence_code1": {"title": "Licence code1", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point1": {"title": "Licence point1", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code2": {"title": "Licence code2", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name1": {"title": "Licence name1", "type": "string", "x-nullable": true}, "licence_name2": {"title": "Licence name2", "type": "string", "x-nullable": true}, "licence_name3": {"title": "Licence name3", "type": "string", "x-nullable": true}, "licence_point2": {"title": "Licence point2", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code3": {"title": "Licence code3", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point3": {"title": "Licence point3", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "recruit_progress_code": {"title": "Recruit progress code", "type": "integer", "x-nullable": true}, "waiting_flag": {"title": "Waiting flag", "type": "integer", "x-nullable": true}, "host_company": {"$ref": "#/definitions/RecruitCompanyInformation"}, "similar_recruits": {"type": "array", "items": {"$ref": "#/definitions/RecRecruit"}, "readOnly": true}, "interview_datetime": {"title": "Interview datetime", "type": "string", "format": "date-time", "x-nullable": true}, "payroll_price": {"title": "Payroll price", "type": "string", "format": "decimal", "x-nullable": true}, "place_code": {"title": "Place code", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "joing_date": {"title": "Joing date", "type": "string", "format": "date", "x-nullable": true}, "group_id": {"title": "Group id", "type": "integer", "x-nullable": true}, "apply_id": {"title": "Apply id", "type": "integer", "readOnly": true}}}, "RecruitmentManagementDetailsResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/RecruitmentManagementDetail"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "SignContract": {"required": ["payroll_price", "host_company"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer", "readOnly": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "employ_code": {"title": "Employ code", "type": "string", "maxLength": 2, "x-nullable": true}, "payroll_price": {"title": "Payroll price", "type": "string", "minLength": 1, "x-nullable": true}, "payroll_code": {"title": "Payroll code", "type": "string", "maxLength": 3, "x-nullable": true}, "place_code": {"title": "Place code", "type": "string", "maxLength": 6, "x-nullable": true}, "joing_date": {"title": "Joing date", "type": "string", "format": "date", "x-nullable": true}, "host_company": {"$ref": "#/definitions/RecruitCompany"}, "interview_datetime": {"title": "Interview datetime", "type": "string", "format": "date-time", "x-nullable": true}, "recruit_id": {"title": "Recruit id", "type": "string", "readOnly": true}, "recruit_progress_code": {"title": "Recruit progress code", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}}, "x-nullable": true}, "SignContractResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/SignContract"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "UpdateInterviewDatetime": {"type": "object", "properties": {"interview_datetime": {"title": "Interview datetime", "type": "string", "format": "date-time", "x-nullable": true}}}, "EngineerUpdateDataPolicy": {"required": ["is_data_policy_accept", "code"], "type": "object", "properties": {"is_data_policy_accept": {"title": "Is data policy accept", "type": "boolean"}, "code": {"title": "Code", "type": "string", "minLength": 1}}}, "EngineerAgencyCompany": {"type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "agent_fee": {"title": "Agent fee", "type": "string", "format": "decimal", "x-nullable": true}, "agent_fee_curr_code": {"title": "Agent fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}}}, "EngineerAgencyCompanyResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/EngineerAgencyCompany"}}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "GeneralCompanyApplyDetails": {"required": ["engineer"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer", "readOnly": true}, "payroll_price": {"title": "Payroll price", "type": "string", "readOnly": true}, "payroll_code": {"title": "Payroll code", "type": "string", "readOnly": true}, "recruit_progress_code": {"title": "Recruit progress code", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "progress_update_datetime": {"title": "Progress update datetime", "type": "string", "format": "date-time", "x-nullable": true}, "interview_datetime": {"title": "Interview datetime", "type": "string", "format": "date-time", "x-nullable": true}, "joing_date": {"title": "Joing date", "type": "string", "format": "date", "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "employ_code": {"title": "Employ code", "type": "string", "maxLength": 2, "x-nullable": true}, "place_code": {"title": "Place code", "type": "string", "maxLength": 6, "x-nullable": true}, "benefits": {"title": "Benefits", "type": "string", "maxLength": 10000, "x-nullable": true}, "offer_contract_date": {"title": "Offer contract date", "type": "string", "format": "date", "x-nullable": true}, "offer_pdf_path": {"title": "Offer pdf path", "type": "string", "maxLength": 75, "x-nullable": true}, "expiry_date": {"title": "Expiry date", "type": "string", "format": "date", "x-nullable": true}, "is_from_support": {"title": "Is from support", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "recruit": {"title": "Recruit", "type": "integer", "x-nullable": true}, "group": {"title": "Group", "type": "integer", "x-nullable": true}, "engineer": {"title": "Engineer", "type": "integer"}, "agency_company": {"title": "Agency company", "type": "integer", "x-nullable": true}, "agency_agent": {"title": "Agency agent", "type": "integer", "x-nullable": true}, "host_company": {"title": "Host company", "type": "integer", "x-nullable": true}, "host_agent": {"title": "Host agent", "type": "integer", "x-nullable": true}, "support_company": {"title": "Support company", "type": "integer", "x-nullable": true}, "support_agent": {"title": "Support agent", "type": "integer", "x-nullable": true}, "engineer_accept_sign": {"title": "Engineer accept sign", "type": "integer", "x-nullable": true}}}, "GeneralCompanyRecruitInfo": {"required": ["host_company", "host_agent"], "type": "object", "properties": {"recruit_id": {"title": "Recruit id", "type": "integer", "readOnly": true}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "readOnly": true}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "readOnly": true}, "payroll_code": {"title": "Payroll code", "type": "string", "readOnly": true}, "save_type": {"title": "Save type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "title": {"title": "Title", "type": "string", "minLength": 1, "x-nullable": true}, "catch_copy": {"title": "Catch copy", "type": "string", "x-nullable": true}, "start_date": {"title": "Start date", "type": "string", "format": "date-time", "x-nullable": true}, "end_date": {"title": "End date", "type": "string", "format": "date-time", "x-nullable": true}, "display_flag": {"title": "Display flag", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "employ_code": {"title": "Employ code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "content": {"title": "Content", "type": "string", "maxLength": 10000, "minLength": 1, "x-nullable": true}, "place_code1": {"title": "Place code1", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code2": {"title": "Place code2", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code3": {"title": "Place code3", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "age_from": {"title": "Age from", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "age_to": {"title": "Age to", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "pref_code1": {"title": "Pref code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code2": {"title": "Pref code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code3": {"title": "Pref code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "language_code1": {"title": "Language code1", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type1": {"title": "Language level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "language_code2": {"title": "Language code2", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type2": {"title": "Language level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "experienced_job_code": {"title": "Experienced job code", "type": "string", "maxLength": 3, "x-nullable": true}, "years_of_experience": {"title": "Years of experience", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code1": {"title": "Skill job code1", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code1": {"title": "Skill code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type1": {"title": "Skill level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code2": {"title": "Skill job code2", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code2": {"title": "Skill code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type2": {"title": "Skill level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code3": {"title": "Skill job code3", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code3": {"title": "Skill code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type3": {"title": "Skill level type3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "create_agent_id": {"title": "Create agent id", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "update_agent_id": {"title": "Update agent id", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "licence_code1": {"title": "Licence code1", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name1": {"title": "Licence name1", "type": "string", "x-nullable": true}, "licence_point1": {"title": "Licence point1", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code2": {"title": "Licence code2", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name2": {"title": "Licence name2", "type": "string", "x-nullable": true}, "licence_point2": {"title": "Licence point2", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code3": {"title": "Licence code3", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name3": {"title": "Licence name3", "type": "string", "x-nullable": true}, "licence_point3": {"title": "Licence point3", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "recruit_image_path": {"title": "Recruit image path", "type": "string", "maxLength": 200, "x-nullable": true}, "remote_code": {"title": "Remote code", "type": "string", "maxLength": 2, "x-nullable": true}, "payroll_price_from_usd": {"title": "Payroll price from usd", "type": "string", "format": "decimal", "x-nullable": true}, "payroll_price_to_usd": {"title": "Payroll price to usd", "type": "string", "format": "decimal", "x-nullable": true}, "host_company": {"title": "Host company", "type": "integer"}, "host_agent": {"title": "Host agent", "type": "integer"}, "support_company": {"title": "Support company", "type": "integer", "x-nullable": true}, "support_agent": {"title": "Support agent", "type": "integer", "x-nullable": true}}}, "EngAcademic": {"required": ["engineer_id"], "type": "object", "properties": {"engineer_id": {"title": "Engineer id", "type": "integer"}, "school": {"title": "School", "type": "string", "minLength": 1, "x-nullable": true}, "type": {"title": "Type", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "out_date": {"title": "Out date", "type": "string", "format": "date", "x-nullable": true}, "faculty": {"title": "Faculty", "type": "string", "minLength": 1, "x-nullable": true}, "type_name": {"title": "Type name", "type": "string", "readOnly": true}}}, "EngLicense": {"required": ["engineer_id", "licence_point"], "type": "object", "properties": {"engineer_id": {"title": "Engineer id", "type": "integer"}, "licence_code": {"title": "Licence code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "get_date": {"title": "Get date", "type": "string", "maxLength": 8, "x-nullable": true}, "licence_point": {"title": "Licence point", "type": "string", "minLength": 1, "x-nullable": true}, "licence_name": {"title": "Licence name", "type": "string", "x-nullable": true}, "license_code_name": {"title": "License code name", "type": "string", "readOnly": true}}}, "EngCareerJobSkill": {"type": "object", "properties": {"job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "skill_code": {"title": "Skill code", "type": "string", "maxLength": 6, "x-nullable": true}, "years_of_experience": {"title": "Years of experience", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "skill_code_name": {"title": "Skill code name", "type": "string", "readOnly": true}, "job_name": {"title": "Job name", "type": "string", "readOnly": true}, "level_name": {"title": "Level name", "type": "string", "readOnly": true}}}, "EngCareer": {"required": ["engineer_id"], "type": "object", "properties": {"career_id": {"title": "Career id", "type": "integer", "readOnly": true}, "engineer_id": {"title": "Engineer id", "type": "integer"}, "company_name": {"title": "Company name", "type": "string", "x-nullable": true}, "job_description": {"title": "Job description", "type": "string", "x-nullable": true}, "career_type": {"title": "Career type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "entering_date": {"title": "Entering date", "type": "string", "format": "date", "x-nullable": true}, "quitting_date": {"title": "Quitting date", "type": "string", "format": "date", "x-nullable": true}, "career_job_skills": {"type": "array", "items": {"$ref": "#/definitions/EngCareerJobSkill"}, "readOnly": true}, "role_name": {"title": "Role name", "type": "string", "maxLength": 1000, "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "minLength": 1, "x-nullable": true}}}, "EngineerSelfAssesmentDetails": {"required": ["remote_exp_years", "remote_job_description", "total_remote_skill", "total_global_skill", "total_communication_skill", "total_report_skill", "total_management_skill", "social_style", "durability_score", "global_work_exp"], "type": "object", "properties": {"remote_exp_years": {"title": "Remote exp years", "type": "integer", "x-nullable": true}, "remote_job_description": {"title": "Remote job description", "type": "string", "minLength": 1, "x-nullable": true}, "total_remote_skill": {"title": "Total remote skill", "type": "integer", "x-nullable": true}, "total_global_skill": {"title": "Total global skill", "type": "integer", "x-nullable": true}, "total_communication_skill": {"title": "Total communication skill", "type": "integer", "x-nullable": true}, "total_report_skill": {"title": "Total report skill", "type": "integer", "x-nullable": true}, "total_management_skill": {"title": "Total management skill", "type": "integer", "x-nullable": true}, "social_style": {"title": "Social style", "type": "integer", "x-nullable": true}, "durability_score": {"title": "Durability score", "type": "integer", "x-nullable": true}, "global_work_exp": {"title": "Global work exp", "type": "integer", "x-nullable": true}}}, "UserExploreDetailsSerializers": {"required": ["email", "user_type"], "type": "object", "properties": {"profile_image_path": {"title": "Profile image path", "type": "string", "maxLength": 200, "minLength": 1, "x-nullable": true}, "email": {"title": "Email", "type": "string", "format": "email", "maxLength": 100, "minLength": 1}, "first_name": {"title": "First name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "last_name": {"title": "Last name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "birth_date": {"title": "Birth date", "type": "string", "format": "date", "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "minLength": 1, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "city_name": {"title": "City name", "type": "string", "maxLength": 50, "minLength": 1, "x-nullable": true}, "passport_number": {"title": "Passport number", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "passport_image_path": {"title": "Passport image path", "type": "string", "maxLength": 200, "minLength": 1, "x-nullable": true}, "educations": {"type": "array", "items": {"$ref": "#/definitions/EngAcademic"}, "readOnly": true}, "languages": {"type": "array", "items": {"$ref": "#/definitions/EngLanguage"}, "readOnly": true}, "qualifications": {"type": "array", "items": {"$ref": "#/definitions/EngLicense"}, "readOnly": true}, "skills": {"type": "array", "items": {"$ref": "#/definitions/EngSkill"}, "readOnly": true}, "experiences": {"type": "array", "items": {"$ref": "#/definitions/EngCareer"}, "readOnly": true}, "requirements": {"$ref": "#/definitions/EngHope"}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "pr": {"title": "Pr", "type": "string", "maxLength": 10000, "minLength": 1, "x-nullable": true}, "self_introduction_url": {"title": "Self introduction url", "type": "string", "minLength": 1, "x-nullable": true}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "user_type": {"title": "User type", "type": "integer", "maximum": **********, "minimum": -**********}, "facebook_url": {"title": "Facebook url", "type": "string"}, "linkedin_url": {"title": "Linkedin url", "type": "string"}, "whatsapp_url": {"title": "Whatsapp url", "type": "string"}, "interested_flag": {"title": "Interested flag", "type": "integer", "readOnly": true}, "user_id": {"title": "User id", "type": "integer", "readOnly": true}, "last_academic_name": {"title": "Last academic name", "type": "string", "readOnly": true}, "self_assesment_details": {"$ref": "#/definitions/EngineerSelfAssesmentDetails"}, "can_request_interview": {"title": "Can request interview", "type": "boolean", "readOnly": true}, "nickname": {"title": "Nickname", "type": "string", "maxLength": 30, "x-nullable": true}}}, "GeneralCompanyPRComment": {"required": ["agency_company"], "type": "object", "properties": {"introduction_pr": {"title": "Introduction pr", "type": "string", "maxLength": 10000, "x-nullable": true}, "create_datetime": {"title": "Create datetime", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "map_id": {"title": "Map id", "type": "integer", "readOnly": true}, "agency_company": {"title": "Agency company", "type": "integer"}, "agency_agent": {"title": "Agency agent", "type": "integer", "x-nullable": true}, "update_datetime": {"title": "Update datetime", "type": "string", "format": "date-time", "x-nullable": true}}}, "GeneralCompanyCompareDetails": {"type": "object", "properties": {"description": {"title": "Description", "type": "string", "readOnly": true}, "point": {"title": "Point", "type": "integer", "readOnly": true}, "salary_matched": {"title": "Salary matched", "type": "boolean", "readOnly": true}, "work_location_matched": {"title": "Work location matched", "type": "boolean", "readOnly": true}, "employ_code_matched": {"title": "Employ code matched", "type": "boolean", "readOnly": true}, "job_skills_matched": {"title": "Job skills matched", "type": "boolean", "readOnly": true}}}, "GeneralCompanyGetApplyDetails": {"required": ["apply", "recruit", "pr_comments"], "type": "object", "properties": {"apply": {"$ref": "#/definitions/GeneralCompanyApplyDetails"}, "recruit": {"$ref": "#/definitions/GeneralCompanyRecruitInfo"}, "engineer": {"$ref": "#/definitions/UserExploreDetailsSerializers"}, "pr_comments": {"type": "array", "items": {"$ref": "#/definitions/GeneralCompanyPRComment"}}, "compare_details": {"$ref": "#/definitions/GeneralCompanyCompareDetails"}, "host_company_address": {"title": "Host company address", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "host_company_contact_email": {"title": "Host company contact email", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "support_company_contact_email": {"title": "Support company contact email", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "agency_company_contact_email": {"title": "Agency company contact email", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}}}, "GeneralCompanyApplyDetailsResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/GeneralCompanyGetApplyDetails"}}}, "GeneralCompanyParamGetContractDetails": {"required": ["apply_id"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer"}, "host_company_id": {"title": "Host company id", "type": "integer", "x-nullable": true}}}, "GeneralCompanyInterviewData": {"type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer", "readOnly": true}, "interview_datetime": {"title": "Interview datetime", "type": "string", "format": "date-time", "x-nullable": true}, "host_company": {"title": "Host company", "type": "integer", "x-nullable": true}, "host_agent": {"title": "Host agent", "type": "integer", "x-nullable": true}, "engineer_first_name": {"title": "Engineer first name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "engineer_last_name": {"title": "Engineer last name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "engineer_nick_name": {"title": "Engineer nick name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}}}, "GeneralCompanyCalendarInterviewResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/GeneralCompanyInterviewData"}}}}, "GeneralCompanyGetContractDetails": {"type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer", "readOnly": true}, "host_company_id": {"title": "Host company id", "type": "integer", "readOnly": true, "x-nullable": true}, "host_company_name": {"title": "Host company name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "host_agent_first_name": {"title": "Host agent first name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "host_agent_last_name": {"title": "Host agent last name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "host_agent_accept_sign_path": {"title": "Host agent accept sign path", "type": "string", "readOnly": true}, "support_company_id": {"title": "Support company id", "type": "integer", "readOnly": true, "x-nullable": true}, "support_company_name": {"title": "Support company name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "support_agent_first_name": {"title": "Support agent first name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "support_agent_last_name": {"title": "Support agent last name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "support_agent_accept_sign_path": {"title": "Support agent accept sign path", "type": "string", "readOnly": true}, "agency_company_id": {"title": "Agency company id", "type": "integer", "readOnly": true, "x-nullable": true}, "agency_company_name": {"title": "Agency company name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "agency_agent_first_name": {"title": "Agency agent first name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "agency_agent_last_name": {"title": "Agency agent last name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "agency_agent_accept_sign_path": {"title": "Agency agent accept sign path", "type": "string", "readOnly": true}, "engineer_id": {"title": "Engineer id", "type": "integer", "readOnly": true, "x-nullable": true}, "engineer_first_name": {"title": "Engineer first name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "engineer_last_name": {"title": "Engineer last name", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "engineer_accept_sign_path": {"title": "Engineer accept sign path", "type": "string", "readOnly": true}}}, "GeneralCompanyContractDetailsResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/GeneralCompanyGetContractDetails"}}}, "GeneralCompanyRejectApply": {"required": ["apply_id"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer"}, "host_company_id": {"title": "Host company id", "type": "integer", "x-nullable": true}}}, "GeneralCompanyRequestInterviewApply": {"required": ["apply_id"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer"}, "host_company_id": {"title": "Host company id", "type": "integer", "x-nullable": true}}}, "GeneralCompanyUpdateInterviewDateTime": {"required": ["apply_id", "interview_datetime"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer"}, "interview_datetime": {"title": "Interview datetime", "type": "string", "format": "date-time"}, "host_company_id": {"title": "Host company id", "type": "integer", "x-nullable": true}}}, "GeneralCompanyDetails": {"required": ["capital_stock", "accepting_fee", "support_outsourcing_fee", "user_type"], "type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "capital_stock": {"title": "Capital stock", "type": "string", "minLength": 1, "x-nullable": true}, "accepting_fee": {"title": "Accepting fee", "type": "string", "minLength": 1, "x-nullable": true}, "support_outsourcing_fee": {"title": "Support outsourcing fee", "type": "string", "minLength": 1, "x-nullable": true}, "user_type": {"title": "User type", "type": "integer", "maximum": **********, "minimum": -**********}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "about_us": {"title": "About us", "type": "string", "maxLength": 5000, "x-nullable": true}, "business_details": {"title": "Business details", "type": "string", "maxLength": 5000, "x-nullable": true}, "employees_type": {"title": "Employees type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "x-nullable": true}, "address": {"title": "Address", "type": "string", "maxLength": 200, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "pr_image_path1": {"title": "Pr image path1", "type": "string", "maxLength": 500, "x-nullable": true}, "pr_image_path2": {"title": "Pr image path2", "type": "string", "maxLength": 500, "x-nullable": true}, "pr_image_path3": {"title": "Pr image path3", "type": "string", "maxLength": 500, "x-nullable": true}, "contact_mail": {"title": "Contact mail", "type": "string", "maxLength": 100, "x-nullable": true}, "web_url": {"title": "Web url", "type": "string", "x-nullable": true}, "introduction_url": {"title": "Introduction url", "type": "string", "x-nullable": true}, "memo": {"title": "Memo", "type": "string", "minLength": 1, "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "benefits": {"title": "Benefits", "type": "string", "maxLength": 1000, "x-nullable": true}, "capital_stock_curr_code": {"title": "Capital stock curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "agent_fee": {"title": "Agent fee", "type": "string", "format": "decimal", "x-nullable": true}, "agent_fee_curr_code": {"title": "Agent fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "accepting_fee_curr_code": {"title": "Accepting fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "support_outsourcing_fee_curr_code": {"title": "Support outsourcing fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "support": {"title": "Support", "type": "string", "maxLength": 5000, "x-nullable": true}, "status": {"title": "Status", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "working_hours_from": {"title": "Working hours from", "type": "string", "x-nullable": true}, "working_hours_to": {"title": "Working hours to", "type": "string", "x-nullable": true}}}, "GeneralCompanyResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/GeneralCompanyDetails"}}}, "GeneralCompanyUpdate": {"required": ["introduction_url", "contact_mail", "web_url", "capital_stock", "capital_stock_curr_code", "accepting_fee", "accepting_fee_curr_code", "support_outsourcing_fee", "support_outsourcing_fee_curr_code", "support"], "type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "introduction_url": {"title": "Introduction url", "type": "string", "x-nullable": true}, "employees_type": {"title": "Employees type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "x-nullable": true}, "address": {"title": "Address", "type": "string", "maxLength": 200, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "x-nullable": true}, "contact_mail": {"title": "Contact mail", "type": "string", "format": "email", "minLength": 1, "x-nullable": true}, "about_us": {"title": "About us", "type": "string", "maxLength": 5000, "x-nullable": true}, "business_details": {"title": "Business details", "type": "string", "maxLength": 5000, "x-nullable": true}, "web_url": {"title": "Web url", "type": "string", "minLength": 1, "x-nullable": true}, "capital_stock": {"title": "Capital stock", "type": "string", "minLength": 1, "x-nullable": true}, "capital_stock_curr_code": {"title": "Capital stock curr code", "type": "string", "x-nullable": true}, "accepting_fee": {"title": "Accepting fee", "type": "string", "x-nullable": true}, "accepting_fee_curr_code": {"title": "Accepting fee curr code", "type": "string", "minLength": 1, "x-nullable": true}, "support_outsourcing_fee": {"title": "Support outsourcing fee", "type": "string", "x-nullable": true}, "support_outsourcing_fee_curr_code": {"title": "Support outsourcing fee curr code", "type": "string", "minLength": 1, "x-nullable": true}, "support": {"title": "Support", "type": "string", "maxLength": 5000, "minLength": 1, "x-nullable": true}, "benefits": {"title": "Benefits", "type": "string", "maxLength": 1000, "x-nullable": true}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "working_hours_from": {"title": "Working hours from", "type": "string", "x-nullable": true}, "working_hours_to": {"title": "Working hours to", "type": "string", "x-nullable": true}}}, "AssessmentQuestion": {"required": ["text_en", "text_vi"], "type": "object", "properties": {"text": {"title": "Text", "type": "string", "readOnly": true}, "text_en": {"title": "Text en", "type": "string", "minLength": 1}, "text_vi": {"title": "Text vi", "type": "string", "minLength": 1}}}, "AssessmentAnswer": {"required": ["uuid", "text_en", "text_vi", "is_selected"], "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "minLength": 1}, "text": {"title": "Text", "type": "string", "readOnly": true}, "text_en": {"title": "Text en", "type": "string", "minLength": 1}, "text_vi": {"title": "Text vi", "type": "string", "minLength": 1}, "is_selected": {"title": "Is selected", "type": "boolean", "x-nullable": true}, "description": {"title": "Description", "type": "string", "readOnly": true}}}, "AssessmentQuestionData": {"required": ["question", "options"], "type": "object", "properties": {"question": {"$ref": "#/definitions/AssessmentQuestion"}, "options": {"type": "array", "items": {"$ref": "#/definitions/AssessmentAnswer"}}}}, "AssessmentQuestionsResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/AssessmentQuestionData"}}, "errors": {"type": "array", "items": {"type": "string", "minLength": 1}, "x-nullable": true}}}, "UserExploreDetailsResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/UserExploreDetailsSerializers"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "MainSkill": {"required": ["skill_code", "skill_name"], "type": "object", "properties": {"skill_code": {"title": "Skill code", "type": "string", "minLength": 1}, "skill_name": {"title": "Skill name", "type": "string", "minLength": 1}}}, "EngWorkHistory": {"required": ["engineer_id"], "type": "object", "properties": {"career_id": {"title": "Career id", "type": "integer", "readOnly": true}, "engineer_id": {"title": "Engineer id", "type": "integer"}, "company_name": {"title": "Company name", "type": "string", "x-nullable": true}, "job_description": {"title": "Job description", "type": "string", "x-nullable": true}, "career_type": {"title": "Career type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "entering_date": {"title": "Entering date", "type": "string", "format": "date", "x-nullable": true}, "quitting_date": {"title": "Quitting date", "type": "string", "format": "date", "x-nullable": true}, "role_name": {"title": "Role name", "type": "string", "maxLength": 1000, "x-nullable": true}}}, "GeneralCompanyExploreUser": {"required": ["payroll_price_usd", "reason_ai_recommend", "reason_ai_recommend_ja", "reason_ai_recommend_vi", "recruit_id"], "type": "object", "properties": {"user_id": {"title": "User id", "type": "integer", "readOnly": true}, "profile_image_path": {"title": "Profile image path", "type": "string", "maxLength": 200, "minLength": 1, "x-nullable": true}, "first_name": {"title": "First name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "last_name": {"title": "Last name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "age": {"title": "Age", "type": "integer", "readOnly": true}, "created": {"title": "Created", "type": "string", "format": "date-time"}, "languages": {"type": "array", "items": {"$ref": "#/definitions/EngLanguage"}, "readOnly": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "interested_flag": {"title": "Interested flag", "type": "integer", "readOnly": true}, "skills": {"type": "array", "items": {"$ref": "#/definitions/EngSkill"}, "readOnly": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "academic_level_name": {"title": "Academic level name", "type": "string", "readOnly": true}, "requirements": {"$ref": "#/definitions/EngHope"}, "nickname": {"title": "Nickname", "type": "string", "maxLength": 30, "x-nullable": true}, "school_name": {"title": "School name", "type": "string", "readOnly": true}, "exist_apply_id": {"title": "Exist apply id", "type": "integer", "readOnly": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "qualifications": {"type": "array", "items": {"$ref": "#/definitions/EngLicense"}, "readOnly": true}, "payroll_price_usd": {"title": "Payroll price usd", "type": "string", "minLength": 1, "x-nullable": true}, "last_login": {"title": "Last login", "type": "string", "format": "date-time", "x-nullable": true}, "total_apply_count": {"title": "Total apply count", "type": "integer", "readOnly": true}, "total_company_changed_count": {"title": "Total company changed count", "type": "integer", "readOnly": true}, "job_code_has_worked_recently": {"title": "Job code has worked recently", "type": "integer", "readOnly": true}, "total_recruit_progress_code_active": {"title": "Total recruit progress code active", "type": "integer", "readOnly": true}, "current_recruit_progress_code_with_my_company": {"title": "Current recruit progress code with my company", "type": "integer", "readOnly": true}, "total_match_skill_codes": {"title": "Total match skill codes", "type": "integer", "readOnly": true}, "remote_code_score": {"title": "Remote code score", "type": "integer", "readOnly": true}, "salary_score": {"title": "Salary score", "type": "integer", "readOnly": true}, "total_score": {"title": "Total score", "type": "integer", "readOnly": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "main_skill": {"type": "array", "items": {"$ref": "#/definitions/MainSkill"}, "readOnly": true}, "hope_job_codes": {"type": "array", "items": {"type": "string", "minLength": 1}, "readOnly": true}, "work_histories": {"type": "array", "items": {"$ref": "#/definitions/EngWorkHistory"}, "readOnly": true}, "reason_ai_recommend": {"title": "Reason ai recommend", "type": "string", "minLength": 1, "x-nullable": true}, "reason_ai_recommend_ja": {"title": "Reason ai recommend ja", "type": "string", "minLength": 1, "x-nullable": true}, "reason_ai_recommend_vi": {"title": "Reason ai recommend vi", "type": "string", "minLength": 1, "x-nullable": true}, "recruit_id": {"title": "Recruit id", "type": "integer", "x-nullable": true}}}, "PagingGeneralCompanyExploreUserResponseModel": {"required": ["count", "next", "previous", "ai_summary", "ai_summary_ja", "ai_summary_vi", "recruit_id", "results"], "type": "object", "properties": {"count": {"title": "Count", "type": "integer"}, "next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "ai_summary": {"title": "Ai summary", "type": "string", "minLength": 1, "x-nullable": true}, "ai_summary_ja": {"title": "Ai summary ja", "type": "string", "minLength": 1, "x-nullable": true}, "ai_summary_vi": {"title": "Ai summary vi", "type": "string", "minLength": 1, "x-nullable": true}, "recruit_id": {"title": "Recruit id", "type": "integer", "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/GeneralCompanyExploreUser"}}}}, "GeneralCompanyListUserExploreResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/PagingGeneralCompanyExploreUserResponseModel"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "GeneralCompanyGetUserExploreCountResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"title": "Data", "type": "integer"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "Chat": {"type": "object", "properties": {"chat_id": {"title": "Chat id", "type": "integer", "readOnly": true}, "text": {"title": "Text", "type": "string", "maxLength": 1000, "x-nullable": true}, "send": {"title": "Send", "type": "string", "format": "date-time", "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "group": {"title": "Group", "type": "integer", "x-nullable": true}, "user": {"title": "User", "type": "integer", "x-nullable": true}}}, "HostCompany": {"required": ["web_url", "contact_mail", "capital_stock", "capital_stock_curr_code", "accepting_fee", "accepting_fee_curr_code", "support_outsourcing_fee", "support_outsourcing_fee_curr_code", "support"], "type": "object", "properties": {"name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "web_url": {"title": "Web url", "type": "string", "maxLength": 255, "x-nullable": true}, "employees_type": {"title": "Employees type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "x-nullable": true}, "address": {"title": "Address", "type": "string", "maxLength": 200, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "x-nullable": true}, "contact_mail": {"title": "Contact mail", "type": "string", "maxLength": 200, "minLength": 1}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "capital_stock": {"title": "Capital stock", "type": "string", "x-nullable": true}, "capital_stock_curr_code": {"title": "Capital stock curr code", "type": "string", "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "accepting_fee": {"title": "Accepting fee", "type": "string", "x-nullable": true}, "accepting_fee_curr_code": {"title": "Accepting fee curr code", "type": "string", "x-nullable": true}, "support_outsourcing_fee": {"title": "Support outsourcing fee", "type": "string", "x-nullable": true}, "support_outsourcing_fee_curr_code": {"title": "Support outsourcing fee curr code", "type": "string", "x-nullable": true}, "support": {"title": "Support", "type": "string", "maxLength": 5000, "minLength": 1, "x-nullable": true}}}, "UserChat": {"required": ["user_id", "user_type", "email", "profile_image_path", "first_name", "last_name"], "type": "object", "properties": {"user_id": {"title": "User id", "type": "integer"}, "user_type": {"title": "User type", "type": "integer"}, "email": {"title": "Email", "type": "string", "format": "email", "minLength": 1, "x-nullable": true}, "profile_image_path": {"title": "Profile image path", "type": "string", "minLength": 1, "x-nullable": true}, "first_name": {"title": "First name", "type": "string", "minLength": 1, "x-nullable": true}, "last_name": {"title": "Last name", "type": "string", "minLength": 1, "x-nullable": true}, "company": {"$ref": "#/definitions/HostCompany"}, "chat_id": {"title": "Chat id", "type": "integer", "readOnly": true, "x-nullable": true}, "age": {"title": "Age", "type": "integer", "readOnly": true, "x-nullable": true}, "nickname": {"title": "Nickname", "type": "string", "maxLength": 30, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}}}, "GeneralCompanyUserAppliedCompany": {"type": "object", "properties": {"user_id": {"title": "User id", "type": "integer", "readOnly": true}, "profile_image_path": {"title": "Profile image path", "type": "string", "maxLength": 200, "minLength": 1, "x-nullable": true}, "first_name": {"title": "First name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "last_name": {"title": "Last name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "age": {"title": "Age", "type": "integer", "readOnly": true}, "languages": {"type": "array", "items": {"$ref": "#/definitions/EngLanguage"}, "readOnly": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "interested_flag": {"title": "Interested flag", "type": "integer", "readOnly": true}, "skills": {"type": "array", "items": {"$ref": "#/definitions/EngSkill"}, "readOnly": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "academic_level_name": {"title": "Academic level name", "type": "string", "readOnly": true}, "nickname": {"title": "Nickname", "type": "string", "maxLength": 30, "x-nullable": true}, "school_name": {"title": "School name", "type": "string", "readOnly": true}, "exist_apply_id": {"title": "Exist apply id", "type": "integer", "readOnly": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "qualifications": {"type": "array", "items": {"$ref": "#/definitions/EngLicense"}, "readOnly": true}, "payroll_price_usd": {"title": "Payroll price usd", "type": "string", "readOnly": true, "minLength": 1, "x-nullable": true}, "last_login": {"title": "Last login", "type": "string", "format": "date-time", "x-nullable": true}, "total_apply_count": {"title": "Total apply count", "type": "integer", "readOnly": true}, "total_company_changed_count": {"title": "Total company changed count", "type": "integer", "readOnly": true}, "job_code_has_worked_recently": {"title": "Job code has worked recently", "type": "integer", "readOnly": true}, "total_recruit_progress_code_active": {"title": "Total recruit progress code active", "type": "integer", "readOnly": true}, "current_recruit_progress_code_with_my_company": {"title": "Current recruit progress code with my company", "type": "integer", "readOnly": true}, "total_match_skill_codes": {"title": "Total match skill codes", "type": "integer", "readOnly": true}, "remote_code_score": {"title": "Remote code score", "type": "integer", "readOnly": true}, "salary_score": {"title": "Salary score", "type": "integer", "readOnly": true}, "total_score": {"title": "Total score", "type": "integer", "readOnly": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "main_skill": {"type": "array", "items": {"$ref": "#/definitions/MainSkill"}, "readOnly": true}, "hope_job_codes": {"type": "array", "items": {"type": "string", "x-nullable": true}, "readOnly": true}, "work_histories": {"type": "array", "items": {"$ref": "#/definitions/EngWorkHistory"}, "readOnly": true}, "under_selection_count": {"title": "Under selection count", "type": "integer", "readOnly": true}, "requirements": {"$ref": "#/definitions/EngHope"}}}, "GroupChat": {"required": ["id", "group"], "type": "object", "properties": {"id": {"title": "Id", "type": "integer", "maximum": **********, "minimum": -**********}, "group": {"title": "Group", "type": "integer"}, "chat": {"title": "Cha<PERSON>", "type": "integer", "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "lasted_message": {"$ref": "#/definitions/Chat"}, "users": {"type": "array", "items": {"$ref": "#/definitions/UserChat"}, "readOnly": true}, "progress_code": {"title": "Progress code", "type": "integer", "readOnly": true}, "engineer": {"$ref": "#/definitions/GeneralCompanyUserAppliedCompany"}}}, "ListGroupChat": {"required": ["next", "previous", "results", "total_count"], "type": "object", "properties": {"next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/GroupChat"}}, "total_count": {"title": "Total count", "type": "integer"}}}, "GeneralCompanyListGroupChatResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/ListGroupChat"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "GeneralCompanyAppliedEngineers": {"required": ["recruit_id", "group_id", "engineer_id", "host_company_id", "engineer_accept_sign_id", "engineer"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer", "readOnly": true}, "recruit_id": {"title": "Recruit id", "type": "integer"}, "group_id": {"title": "Group id", "type": "integer", "x-nullable": true}, "title": {"title": "Title", "type": "string", "readOnly": true}, "engineer_id": {"title": "Engineer id", "type": "integer", "x-nullable": true}, "host_company_id": {"title": "Host company id", "type": "integer", "x-nullable": true}, "progress_update_datetime": {"title": "Progress update datetime", "type": "string", "format": "date-time", "x-nullable": true}, "recruit_progress_code": {"title": "Recruit progress code", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "engineer_accept_sign_id": {"title": "Engineer accept sign id", "type": "integer", "x-nullable": true}, "interview_datetime": {"title": "Interview datetime", "type": "string", "format": "date-time", "x-nullable": true}, "is_new": {"title": "Is new", "type": "boolean", "readOnly": true}, "unread_message_count": {"title": "Unread message count", "type": "integer", "readOnly": true}, "engineer": {"$ref": "#/definitions/GeneralCompanyUserAppliedCompany"}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}}}, "PagingGeneralCompanyAppliedEngineersResponseModel": {"required": ["count", "next", "previous", "results"], "type": "object", "properties": {"count": {"title": "Count", "type": "integer"}, "next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/GeneralCompanyAppliedEngineers"}}}}, "GeneralCompanyListUserAppliedCompanyResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/PagingGeneralCompanyAppliedEngineersResponseModel"}}}, "GeneralCompanyUser": {"required": ["email", "password"], "type": "object", "properties": {"email": {"title": "Email", "type": "string", "format": "email", "minLength": 1}, "password": {"title": "Password", "type": "string", "maxLength": 128, "minLength": 1}, "first_name": {"title": "First name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "last_name": {"title": "Last name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}}}, "GeneralCompany": {"required": ["web_url", "contact_mail", "capital_stock", "capital_stock_curr_code", "accepting_fee", "accepting_fee_curr_code", "support_outsourcing_fee", "support_outsourcing_fee_curr_code", "support"], "type": "object", "properties": {"name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "web_url": {"title": "Web url", "type": "string", "maxLength": 255, "x-nullable": true}, "employees_type": {"title": "Employees type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "x-nullable": true}, "address": {"title": "Address", "type": "string", "maxLength": 200, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "x-nullable": true}, "contact_mail": {"title": "Contact mail", "type": "string", "maxLength": 200, "minLength": 1}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "capital_stock": {"title": "Capital stock", "type": "string", "x-nullable": true}, "capital_stock_curr_code": {"title": "Capital stock curr code", "type": "string", "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "accepting_fee": {"title": "Accepting fee", "type": "string", "x-nullable": true}, "accepting_fee_curr_code": {"title": "Accepting fee curr code", "type": "string", "x-nullable": true}, "support_outsourcing_fee": {"title": "Support outsourcing fee", "type": "string", "x-nullable": true}, "support_outsourcing_fee_curr_code": {"title": "Support outsourcing fee curr code", "type": "string", "x-nullable": true}, "support": {"title": "Support", "type": "string", "maxLength": 5000, "minLength": 1, "x-nullable": true}}}, "GeneralCompanyRegister": {"required": ["user", "company", "user_type"], "type": "object", "properties": {"user": {"$ref": "#/definitions/GeneralCompanyUser"}, "company": {"$ref": "#/definitions/GeneralCompany"}, "user_type": {"title": "User type", "type": "integer"}}}, "GeneralCompanySaveFilter": {"required": ["filter_name", "host_agent"], "type": "object", "properties": {"filter_name": {"title": "Filter name", "type": "string", "minLength": 1}, "age_from": {"title": "Age from", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "age_to": {"title": "Age to", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "string", "maxLength": 1, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "x-nullable": true}, "address_code1": {"title": "Address code1", "type": "string", "maxLength": 6, "x-nullable": true}, "address_code2": {"title": "Address code2", "type": "string", "maxLength": 6, "x-nullable": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "x-nullable": true}, "language_code1": {"title": "Language code1", "type": "string", "maxLength": 3, "x-nullable": true}, "language_level_type1": {"title": "Language level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "language_code2": {"title": "Language code2", "type": "string", "maxLength": 3, "x-nullable": true}, "language_level_type2": {"title": "Language level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "experienced_job_code1": {"title": "Experienced job code1", "type": "string", "maxLength": 3, "x-nullable": true}, "years_of_experience1": {"title": "Years of experience1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "experienced_job_code2": {"title": "Experienced job code2", "type": "string", "maxLength": 3, "x-nullable": true}, "years_of_experience2": {"title": "Years of experience2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "experienced_job_code3": {"title": "Experienced job code3", "type": "string", "maxLength": 3, "x-nullable": true}, "years_of_experience3": {"title": "Years of experience3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_code1_1": {"title": "Skill code1 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_level_type1_1": {"title": "Skill level type1 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_code1_2": {"title": "Skill code1 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_level_type1_2": {"title": "Skill level type1 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_code1_3": {"title": "Skill code1 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_level_type1_3": {"title": "Skill level type1 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_code2_1": {"title": "Skill code2 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_level_type2_1": {"title": "Skill level type2 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_code2_2": {"title": "Skill code2 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_level_type2_2": {"title": "Skill level type2 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_code2_3": {"title": "Skill code2 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_level_type2_3": {"title": "Skill level type2 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_code3_1": {"title": "Skill code3 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_level_type3_1": {"title": "Skill level type3 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_code3_2": {"title": "Skill code3 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_level_type3_2": {"title": "Skill level type3 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_code3_3": {"title": "Skill code3 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_level_type3_3": {"title": "Skill level type3 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "licence_code1": {"title": "Licence code1", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point1": {"title": "Licence point1", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code2": {"title": "Licence code2", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point2": {"title": "Licence point2", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code3": {"title": "Licence code3", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point3": {"title": "Licence point3", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "recruiting_job_code": {"title": "Recruiting job code", "type": "string", "maxLength": 3, "x-nullable": true}, "recruiting_employ_code": {"title": "Recruiting employ code", "type": "string", "maxLength": 2, "x-nullable": true}, "work_place_code1": {"title": "Work place code1", "type": "string", "maxLength": 6, "x-nullable": true}, "work_place_code2": {"title": "Work place code2", "type": "string", "maxLength": 6, "x-nullable": true}, "work_place_code3": {"title": "Work place code3", "type": "string", "maxLength": 6, "x-nullable": true}, "payroll_code": {"title": "Payroll code", "type": "string", "maxLength": 3, "x-nullable": true}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "format": "decimal", "x-nullable": true}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "format": "decimal", "x-nullable": true}, "agent_fee": {"title": "Agent fee", "type": "string", "format": "decimal", "x-nullable": true}, "agent_fee_curr_code": {"title": "Agent fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "host_agent": {"title": "Host agent", "type": "integer"}}}, "GeneralCompanyFilter": {"required": ["filter_name"], "type": "object", "properties": {"filter_id": {"title": "Filter id", "type": "integer", "readOnly": true}, "filter_name": {"title": "Filter name", "type": "string", "minLength": 1}}}, "GeneralCompanyListFilterResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/GeneralCompanyFilter"}}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "GeneralCompanyUpdateFavoriteUser": {"required": ["user_id", "is_favorite"], "type": "object", "properties": {"user_id": {"title": "User id", "type": "integer"}, "is_favorite": {"title": "Is favorite", "type": "boolean"}}}, "ConvertCurrency": {"required": ["amount", "from_currency", "to_currency"], "type": "object", "properties": {"amount": {"title": "Amount", "type": "number"}, "from_currency": {"title": "From currency", "type": "string", "minLength": 1}, "to_currency": {"title": "To currency", "type": "string", "minLength": 1}}}, "GetVideoSocialSourceResponseModel": {"required": ["data"], "type": "object", "properties": {"data": {"title": "Data", "type": "string", "minLength": 1}}}, "HostCompanyParamListUserAppliedCompany": {"type": "object", "properties": {"recruit_ids": {"type": "array", "items": {"type": "integer"}, "x-nullable": true}, "ordering": {"title": "Ordering", "type": "string"}, "apply_status_filter_codes": {"type": "array", "items": {"type": "integer"}, "x-nullable": true}, "search": {"title": "Search", "type": "string"}}}, "HostCompanyListAppliedEngineer": {"required": ["data", "total_engineers"], "type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/HostCompanyParamListUserAppliedCompany"}}, "total_engineers": {"title": "Total engineers", "type": "integer"}}}, "HostCompanyListUserAppliedCompanyResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/HostCompanyListAppliedEngineer"}}}, "HostCompanyGetSupportCompany": {"type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "accepting_fee": {"title": "Accepting fee", "type": "string", "format": "decimal", "x-nullable": true}, "support_outsourcing_fee": {"title": "Support outsourcing fee", "type": "string", "format": "decimal", "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "x-nullable": true}, "support_outsourcing_fee_curr_code": {"title": "Support outsourcing fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "contact_mail": {"title": "Contact mail", "type": "string", "maxLength": 100, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "x-nullable": true}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "accepting_fee_curr_code": {"title": "Accepting fee curr code", "type": "string", "maxLength": 3, "x-nullable": true}, "introduction_pr": {"title": "Introduction pr", "type": "string", "readOnly": true}}}, "HostCompanyListSupportCompanyResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/HostCompanyGetSupportCompany"}}}}, "CompanyRecruitDetail": {"required": ["payroll_price_from", "payroll_price_to", "host_agent"], "type": "object", "properties": {"recruit_id": {"title": "Recruit id", "type": "integer", "readOnly": true}, "support_company": {"$ref": "#/definitions/RecruitCompany"}, "host_company": {"$ref": "#/definitions/RecruitCompany"}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "x-nullable": true}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "x-nullable": true}, "save_type": {"title": "Save type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "title": {"title": "Title", "type": "string", "minLength": 1, "x-nullable": true}, "catch_copy": {"title": "Catch copy", "type": "string", "x-nullable": true}, "start_date": {"title": "Start date", "type": "string", "format": "date-time", "x-nullable": true}, "end_date": {"title": "End date", "type": "string", "format": "date-time", "x-nullable": true}, "display_flag": {"title": "Display flag", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "employ_code": {"title": "Employ code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "content": {"title": "Content", "type": "string", "maxLength": 10000, "minLength": 1, "x-nullable": true}, "place_code1": {"title": "Place code1", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code2": {"title": "Place code2", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code3": {"title": "Place code3", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "payroll_code": {"title": "Payroll code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "age_from": {"title": "Age from", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "age_to": {"title": "Age to", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "pref_code1": {"title": "Pref code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code2": {"title": "Pref code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code3": {"title": "Pref code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "language_code1": {"title": "Language code1", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type1": {"title": "Language level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "language_code2": {"title": "Language code2", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type2": {"title": "Language level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "experienced_job_code": {"title": "Experienced job code", "type": "string", "maxLength": 3, "x-nullable": true}, "years_of_experience": {"title": "Years of experience", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code1": {"title": "Skill job code1", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code1": {"title": "Skill code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type1": {"title": "Skill level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code2": {"title": "Skill job code2", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code2": {"title": "Skill code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type2": {"title": "Skill level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code3": {"title": "Skill job code3", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code3": {"title": "Skill code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type3": {"title": "Skill level type3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "create_agent_id": {"title": "Create agent id", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "update_agent_id": {"title": "Update agent id", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "licence_code1": {"title": "Licence code1", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name1": {"title": "Licence name1", "type": "string", "x-nullable": true}, "licence_point1": {"title": "Licence point1", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code2": {"title": "Licence code2", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name2": {"title": "Licence name2", "type": "string", "x-nullable": true}, "licence_point2": {"title": "Licence point2", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code3": {"title": "Licence code3", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name3": {"title": "Licence name3", "type": "string", "x-nullable": true}, "licence_point3": {"title": "Licence point3", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "recruit_image_path": {"title": "Recruit image path", "type": "string", "maxLength": 200, "x-nullable": true}, "remote_code": {"title": "Remote code", "type": "string", "maxLength": 2, "x-nullable": true}, "payroll_price_from_usd": {"title": "Payroll price from usd", "type": "string", "format": "decimal", "x-nullable": true}, "payroll_price_to_usd": {"title": "Payroll price to usd", "type": "string", "format": "decimal", "x-nullable": true}, "host_agent": {"title": "Host agent", "type": "integer"}, "support_agent": {"title": "Support agent", "type": "integer", "x-nullable": true}}}, "CompanyMyRecruitmentDetailsResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/CompanyRecruitDetail"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "UpdateRecruitment": {"required": ["title", "catch_copy", "content", "payroll_price_from", "payroll_price_to", "remote_code"], "type": "object", "properties": {"title": {"title": "Title", "type": "string", "x-nullable": true}, "catch_copy": {"title": "Catch copy", "type": "string", "maxLength": 100, "x-nullable": true}, "content": {"title": "Content", "type": "string", "maxLength": 10000, "x-nullable": true}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "x-nullable": true}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "x-nullable": true}, "payroll_code": {"title": "Payroll code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "start_date": {"title": "Start date", "type": "string", "format": "date-time", "x-nullable": true}, "end_date": {"title": "End date", "type": "string", "format": "date-time", "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "employ_code": {"title": "Employ code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "place_code1": {"title": "Place code1", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code2": {"title": "Place code2", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code3": {"title": "Place code3", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "pref_code1": {"title": "Pref code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code2": {"title": "Pref code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code3": {"title": "Pref code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "age_from": {"title": "Age from", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "age_to": {"title": "Age to", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "language_code1": {"title": "Language code1", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type1": {"title": "Language level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "language_code2": {"title": "Language code2", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type2": {"title": "Language level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "experienced_job_code": {"title": "Experienced job code", "type": "string", "maxLength": 3, "x-nullable": true}, "years_of_experience": {"title": "Years of experience", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code1": {"title": "Skill job code1", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code1": {"title": "Skill code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type1": {"title": "Skill level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code2": {"title": "Skill job code2", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code2": {"title": "Skill code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type2": {"title": "Skill level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code3": {"title": "Skill job code3", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code3": {"title": "Skill code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type3": {"title": "Skill level type3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "support_company": {"title": "Support company", "type": "integer", "x-nullable": true}, "licence_code1": {"title": "Licence code1", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point1": {"title": "Licence point1", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code2": {"title": "Licence code2", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point2": {"title": "Licence point2", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code3": {"title": "Licence code3", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point3": {"title": "Licence point3", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "recruit_image_path": {"title": "Recruit image path", "type": "string", "maxLength": 200, "x-nullable": true}, "licence_name1": {"title": "Licence name1", "type": "string", "x-nullable": true}, "licence_name2": {"title": "Licence name2", "type": "string", "x-nullable": true}, "licence_name3": {"title": "Licence name3", "type": "string", "x-nullable": true}, "display_flag": {"title": "Display flag", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "remote_code": {"title": "Remote code", "type": "string", "x-nullable": true}, "payroll_price_from_usd": {"title": "Payroll price from usd", "type": "string", "format": "decimal", "x-nullable": true}, "payroll_price_to_usd": {"title": "Payroll price to usd", "type": "string", "format": "decimal", "x-nullable": true}}}, "CreateRecruitment": {"required": ["catch_copy", "content", "payroll_price_from", "payroll_price_to", "title", "remote_code"], "type": "object", "properties": {"catch_copy": {"title": "Catch copy", "type": "string", "maxLength": 100, "x-nullable": true}, "content": {"title": "Content", "type": "string", "maxLength": 10000, "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "employ_code": {"title": "Employ code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "place_code1": {"title": "Place code1", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code2": {"title": "Place code2", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code3": {"title": "Place code3", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "x-nullable": true}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "age_from": {"title": "Age from", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "age_to": {"title": "Age to", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "pref_code1": {"title": "Pref code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code2": {"title": "Pref code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code3": {"title": "Pref code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "payroll_code": {"title": "Payroll code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "language_code1": {"title": "Language code1", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type1": {"title": "Language level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "language_code2": {"title": "Language code2", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type2": {"title": "Language level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "experienced_job_code": {"title": "Experienced job code", "type": "string", "maxLength": 3, "x-nullable": true}, "years_of_experience": {"title": "Years of experience", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code1": {"title": "Skill job code1", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code1": {"title": "Skill code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type1": {"title": "Skill level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code2": {"title": "Skill job code2", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code2": {"title": "Skill code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type2": {"title": "Skill level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code3": {"title": "Skill job code3", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code3": {"title": "Skill code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type3": {"title": "Skill level type3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "licence_code1": {"title": "Licence code1", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point1": {"title": "Licence point1", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code2": {"title": "Licence code2", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point2": {"title": "Licence point2", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code3": {"title": "Licence code3", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_point3": {"title": "Licence point3", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "start_date": {"title": "Start date", "type": "string", "format": "date-time", "x-nullable": true}, "end_date": {"title": "End date", "type": "string", "format": "date-time", "x-nullable": true}, "support_company": {"title": "Support company", "type": "integer", "x-nullable": true}, "title": {"title": "Title", "type": "string", "x-nullable": true}, "recruit_image_path": {"title": "Recruit image path", "type": "string", "maxLength": 200, "x-nullable": true}, "licence_name1": {"title": "Licence name1", "type": "string", "x-nullable": true}, "licence_name2": {"title": "Licence name2", "type": "string", "x-nullable": true}, "licence_name3": {"title": "Licence name3", "type": "string", "x-nullable": true}, "display_flag": {"title": "Display flag", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "remote_code": {"title": "Remote code", "type": "string", "x-nullable": true}}}, "DeleteRecruitment": {"required": ["recruit_id"], "type": "object", "properties": {"recruit_id": {"title": "Recruit id", "type": "string", "minLength": 1}}}, "RecruitUploadedSerializers": {"required": ["payroll_price_from", "payroll_price_to", "host_company", "host_agent"], "type": "object", "properties": {"recruit_id": {"title": "Recruit id", "type": "integer", "readOnly": true}, "title": {"title": "Title", "type": "string", "minLength": 1, "x-nullable": true}, "catch_copy": {"title": "Catch copy", "type": "string", "x-nullable": true}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "minLength": 1, "x-nullable": true}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "minLength": 1, "x-nullable": true}, "payroll_code": {"title": "Payroll code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "start_date": {"title": "Start date", "type": "string", "format": "date-time", "x-nullable": true}, "end_date": {"title": "End date", "type": "string", "format": "date-time", "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "host_company": {"title": "Host company", "type": "integer"}, "skill_job_code1": {"title": "Skill job code1", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_job_code2": {"title": "Skill job code2", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_job_code3": {"title": "Skill job code3", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "host_agent": {"title": "Host agent", "type": "integer"}, "company_user_id": {"title": "Company user id", "type": "integer", "readOnly": true}, "total_apply": {"title": "Total apply", "type": "integer", "readOnly": true}, "job_name": {"title": "Job name", "type": "string", "readOnly": true, "x-nullable": true}, "applied": {"title": "Applied", "type": "boolean", "readOnly": true}, "recruit_image_path": {"title": "Recruit image path", "type": "string", "maxLength": 200, "x-nullable": true}, "is_expired": {"title": "Is expired", "type": "boolean", "readOnly": true}, "place_code1": {"title": "Place code1", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code2": {"title": "Place code2", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code3": {"title": "Place code3", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "total_request": {"title": "Total request", "type": "integer", "readOnly": true}, "total_under_selection": {"title": "Total under selection", "type": "integer", "readOnly": true}, "display_flag": {"title": "Display flag", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "remote_code": {"title": "Remote code", "type": "string", "maxLength": 2, "x-nullable": true}}}, "ListRecruitSerializers": {"required": ["next", "previous", "results", "total_count"], "type": "object", "properties": {"next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/RecruitUploadedSerializers"}}, "total_count": {"title": "Total count", "type": "integer"}}}, "ListRecruitUploadedResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/ListRecruitSerializers"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "RecruitDetail": {"type": "object", "properties": {"recruit_id": {"title": "Recruit id", "type": "integer", "readOnly": true}, "title": {"title": "Title", "type": "string", "minLength": 1, "x-nullable": true}, "catch_copy": {"title": "Catch copy", "type": "string", "x-nullable": true}, "payroll_price_from": {"title": "Payroll price from", "type": "string", "readOnly": true}, "payroll_price_to": {"title": "Payroll price to", "type": "string", "readOnly": true}, "payroll_code": {"title": "Payroll code", "type": "string", "readOnly": true}, "start_date": {"title": "Start date", "type": "string", "format": "date-time", "x-nullable": true}, "end_date": {"title": "End date", "type": "string", "format": "date-time", "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "host_company": {"$ref": "#/definitions/RecruitCompany"}, "waiting_flag": {"title": "Waiting flag", "type": "string", "readOnly": true}, "employ_code": {"title": "Employ code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "place_code1": {"title": "Place code1", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code2": {"title": "Place code2", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "place_code3": {"title": "Place code3", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "pref_code1": {"title": "Pref code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code2": {"title": "Pref code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "pref_code3": {"title": "Pref code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "age_from": {"title": "Age from", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "age_to": {"title": "Age to", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "language_code1": {"title": "Language code1", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type1": {"title": "Language level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "language_code2": {"title": "Language code2", "type": "string", "maxLength": 2, "minLength": 1, "x-nullable": true}, "language_level_type2": {"title": "Language level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "experienced_job_code": {"title": "Experienced job code", "type": "string", "maxLength": 3, "x-nullable": true}, "years_of_experience": {"title": "Years of experience", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code1": {"title": "Skill job code1", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code1": {"title": "Skill code1", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type1": {"title": "Skill level type1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code2": {"title": "Skill job code2", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code2": {"title": "Skill code2", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type2": {"title": "Skill level type2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "skill_job_code3": {"title": "Skill job code3", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "skill_code3": {"title": "Skill code3", "type": "string", "maxLength": 6, "minLength": 1, "x-nullable": true}, "skill_level_type3": {"title": "Skill level type3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "content": {"title": "Content", "type": "string", "maxLength": 10000, "minLength": 1, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "support_company": {"$ref": "#/definitions/RecruitCompany"}, "licence_code1": {"title": "Licence code1", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name1": {"title": "Licence name1", "type": "string", "x-nullable": true}, "licence_point1": {"title": "Licence point1", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code2": {"title": "Licence code2", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name2": {"title": "Licence name2", "type": "string", "x-nullable": true}, "licence_point2": {"title": "Licence point2", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "licence_code3": {"title": "Licence code3", "type": "string", "maxLength": 3, "x-nullable": true}, "licence_name3": {"title": "Licence name3", "type": "string", "x-nullable": true}, "licence_point3": {"title": "Licence point3", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "total_recruit": {"title": "Total recruit", "type": "integer", "readOnly": true}, "similar_recruits": {"type": "array", "items": {"$ref": "#/definitions/RecRecruit"}, "readOnly": true}, "recruit_image_path": {"title": "Recruit image path", "type": "string", "maxLength": 200, "x-nullable": true}, "remote_code": {"title": "Remote code", "type": "string", "maxLength": 2, "x-nullable": true}}}, "RecruitmentDetailsResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/RecruitDetail"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "HostCompanyRequestInterview": {"required": ["recruit_id", "user_id", "message"], "type": "object", "properties": {"recruit_id": {"title": "Recruit id", "type": "integer"}, "user_id": {"title": "User id", "type": "integer"}, "message": {"title": "Message", "type": "string", "minLength": 1}}}, "RequestInterviewSuccess": {"required": ["apply_id"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer"}}}, "GeneralCompanyRequestInterviewApplyResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/RequestInterviewSuccess"}}}, "HostCompanySubscribeSupportCompany": {"required": ["support_company_id"], "type": "object", "properties": {"support_company_id": {"title": "Support company id", "type": "string", "minLength": 1}}}, "HostCompanyUnsubscribeSupportCompany": {"required": ["support_company_id"], "type": "object", "properties": {"support_company_id": {"title": "Support company id", "type": "string", "minLength": 1}}}, "HostCompanyUpdateInterviewAdmission": {"required": ["apply_id", "job_code", "employ_code", "place_code", "payroll_code", "payroll_price", "joining_date"], "type": "object", "properties": {"apply_id": {"title": "Apply id", "type": "integer"}, "job_code": {"title": "Job code", "type": "string", "minLength": 1}, "employ_code": {"title": "Employ code", "type": "string", "minLength": 1}, "place_code": {"title": "Place code", "type": "string", "minLength": 1}, "payroll_code": {"title": "Payroll code", "type": "string", "minLength": 1}, "payroll_price": {"title": "Payroll price", "type": "string", "format": "decimal"}, "joining_date": {"title": "Joining date", "type": "string", "format": "date"}}}, "Image": {"type": "object", "properties": {"title": {"title": "Title", "type": "string", "maxLength": 100, "x-nullable": true}, "image": {"title": "Image", "type": "string", "readOnly": true, "format": "uri"}, "uploaded_at": {"title": "Uploaded at", "type": "string", "format": "date-time", "readOnly": true}, "user": {"title": "User", "type": "integer", "x-nullable": true}, "type": {"title": "Type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "main_folder_path": {"title": "Main folder path", "type": "string", "maxLength": 100, "x-nullable": true}}}, "MediaResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1}, "data": {"$ref": "#/definitions/Image"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}}}}, "CountUnreadNotify": {"required": ["total_notify", "total_unread_notify"], "type": "object", "properties": {"total_notify": {"title": "Total notify", "type": "integer"}, "total_unread_notify": {"title": "Total unread notify", "type": "integer"}}}, "CountUnreadNotifyResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/CountUnreadNotify"}, "errors": {"type": "array", "items": {"type": "string", "minLength": 1}, "x-nullable": true}}}, "Notify": {"type": "object", "properties": {"rec_id": {"title": "Rec id", "type": "integer", "readOnly": true}, "type": {"title": "Type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "message": {"title": "Message", "type": "string", "x-nullable": true}, "user_id": {"title": "User id", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "read_at": {"title": "Read at", "type": "string", "format": "date-time", "x-nullable": true}, "payload": {"title": "Payload", "type": "string", "maxLength": 255, "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}}}, "ListNotifyResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/Notify"}}, "errors": {"type": "array", "items": {"type": "string", "minLength": 1}, "x-nullable": true}}}, "SetReadNotifyResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"title": "Data", "type": "boolean"}, "errors": {"type": "array", "items": {"type": "string", "minLength": 1}, "x-nullable": true}}}, "SetReadNotify": {"required": ["notify_ids"], "type": "object", "properties": {"notify_ids": {"type": "array", "items": {"type": "integer"}, "x-nullable": true}}}, "EngSelfAssesment": {"required": ["engineer"], "type": "object", "properties": {"self_assessment_id": {"title": "Self assessment id", "type": "integer", "readOnly": true}, "remote_exp_years": {"title": "Remote exp years", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "remote_job_description": {"title": "Remote job description", "type": "string", "maxLength": 256, "x-nullable": true}, "remote_skill_1": {"title": "Remote skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "remote_skill_2": {"title": "Remote skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "remote_skill_3": {"title": "Remote skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "global_work_exp": {"title": "Global work exp", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "global_skill_1": {"title": "Global skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "global_skill_2": {"title": "Global skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "global_skill_3": {"title": "Global skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "social_style": {"title": "Social style", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "communication_skill_1": {"title": "Communication skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "communication_skill_2": {"title": "Communication skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "communication_skill_3": {"title": "Communication skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "report_skill_1": {"title": "Report skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "report_skill_2": {"title": "Report skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "report_skill_3": {"title": "Report skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "management_skill_1": {"title": "Management skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "management_skill_2": {"title": "Management skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "management_skill_3": {"title": "Management skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "durability_score": {"title": "Durability score", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "created": {"title": "Created", "type": "string", "format": "date-time", "readOnly": true, "x-nullable": true}, "created_user": {"title": "Created user", "type": "string", "maxLength": 60, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "updated_user": {"title": "Updated user", "type": "string", "maxLength": 60, "x-nullable": true}, "engineer": {"title": "Engineer", "type": "integer"}}}, "CategorySkill": {"required": ["skills", "category_id"], "type": "object", "properties": {"skills": {"type": "array", "items": {"$ref": "#/definitions/EngSkill"}}, "category_id": {"title": "Category id", "type": "string", "minLength": 1}}}, "EngHighLightProject": {"required": ["name", "description", "size", "role_name", "responsibilities", "technology_used", "from_date", "to_date"], "type": "object", "properties": {"name": {"title": "Name", "type": "string", "minLength": 1, "x-nullable": true}, "description": {"title": "Description", "type": "string", "minLength": 1, "x-nullable": true}, "size": {"title": "Size", "type": "string", "minLength": 1, "x-nullable": true}, "role_name": {"title": "Role name", "type": "string", "minLength": 1, "x-nullable": true}, "responsibilities": {"title": "Responsibilities", "type": "string", "minLength": 1, "x-nullable": true}, "technology_used": {"title": "Technology used", "type": "string", "minLength": 1, "x-nullable": true}, "from_date": {"title": "From date", "type": "string", "format": "date", "x-nullable": true}, "to_date": {"title": "To date", "type": "string", "format": "date", "x-nullable": true}}}, "UserDetailsSerializers": {"required": ["email", "user_type"], "type": "object", "properties": {"profile_image_path": {"title": "Profile image path", "type": "string", "maxLength": 200, "minLength": 1, "x-nullable": true}, "email": {"title": "Email", "type": "string", "format": "email", "maxLength": 100, "minLength": 1}, "first_name": {"title": "First name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "last_name": {"title": "Last name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "nickname": {"title": "Nickname", "type": "string", "maxLength": 30, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "birth_date": {"title": "Birth date", "type": "string", "format": "date", "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "minLength": 1, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "city_name": {"title": "City name", "type": "string", "maxLength": 50, "minLength": 1, "x-nullable": true}, "passport_number": {"title": "Passport number", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "passport_image_path": {"title": "Passport image path", "type": "string", "maxLength": 200, "minLength": 1, "x-nullable": true}, "educations": {"type": "array", "items": {"$ref": "#/definitions/EngAcademic"}, "readOnly": true}, "languages": {"type": "array", "items": {"$ref": "#/definitions/EngLanguage"}, "readOnly": true}, "qualifications": {"type": "array", "items": {"$ref": "#/definitions/EngLicense"}, "readOnly": true}, "skills": {"type": "array", "items": {"$ref": "#/definitions/EngSkill"}, "readOnly": true}, "experiences": {"type": "array", "items": {"$ref": "#/definitions/EngCareer"}, "readOnly": true}, "requirements": {"$ref": "#/definitions/EngHope"}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "pr": {"title": "Pr", "type": "string", "maxLength": 10000, "minLength": 1, "x-nullable": true}, "self_introduction_url": {"title": "Self introduction url", "type": "string", "minLength": 1, "x-nullable": true}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "user_type": {"title": "User type", "type": "integer", "maximum": **********, "minimum": -**********}, "facebook_url": {"title": "Facebook url", "type": "string"}, "linkedin_url": {"title": "Linkedin url", "type": "string"}, "whatsapp_url": {"title": "Whatsapp url", "type": "string"}, "last_academic_name": {"title": "Last academic name", "type": "string", "readOnly": true}, "self_assesment": {"$ref": "#/definitions/EngSelfAssesment"}, "email_temp": {"title": "Email temp", "type": "string", "maxLength": 100, "x-nullable": true}, "category_skills": {"type": "array", "items": {"$ref": "#/definitions/CategorySkill"}, "readOnly": true}, "skills_for_cv_display": {"title": "Skills for cv display", "type": "string", "x-nullable": true}, "professional_summary": {"title": "Professional summary", "type": "string", "x-nullable": true}, "highlight_projects": {"type": "array", "items": {"$ref": "#/definitions/EngHighLightProject"}, "readOnly": true}}, "x-nullable": true}, "UserDetailsResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/UserDetailsSerializers"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "UpdateEngAcademic": {"type": "object", "properties": {"school": {"title": "School", "type": "string", "minLength": 1, "x-nullable": true}, "type": {"title": "Type", "type": "integer", "maximum": 32767, "minimum": -32768, "x-nullable": true}, "out_date": {"title": "Out date", "type": "string", "format": "date", "x-nullable": true}, "faculty": {"title": "Faculty", "type": "string", "minLength": 1, "x-nullable": true}}, "x-nullable": true}, "UpdateEngLanguage": {"type": "object", "properties": {"language_level_type": {"title": "Language level type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "language_code": {"title": "Language code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}}, "x-nullable": true}, "UpdateEngLicense": {"type": "object", "properties": {"licence_code": {"title": "Licence code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "licence_name": {"title": "Licence name", "type": "string", "x-nullable": true}, "get_date": {"title": "Get date", "type": "string", "maxLength": 8, "x-nullable": true}, "licence_point": {"title": "Licence point", "type": "string", "format": "decimal", "x-nullable": true}}, "x-nullable": true}, "UpdateEngSkill": {"type": "object", "properties": {"skill_code": {"title": "Skill code", "type": "string", "maxLength": 6, "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "level_type": {"title": "Level type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "temp_name": {"title": "Temp name", "type": "string", "minLength": 1, "x-nullable": true}, "temp_category_id": {"title": "Temp category id", "type": "integer", "x-nullable": true}}, "x-nullable": true}, "UpdateCareerJobSkill": {"type": "object", "properties": {"job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "skill_code": {"title": "Skill code", "type": "string", "maxLength": 6, "x-nullable": true}, "years_of_experience": {"title": "Years of experience", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}}}, "UpdateEngCareer": {"required": ["career_job_skills"], "type": "object", "properties": {"company_name": {"title": "Company name", "type": "string", "x-nullable": true}, "career_type": {"title": "Career type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "entering_date": {"title": "Entering date", "type": "string", "format": "date", "x-nullable": true}, "career_job_skills": {"type": "array", "items": {"$ref": "#/definitions/UpdateCareerJobSkill"}}, "job_description": {"title": "Job description", "type": "string", "x-nullable": true}, "quitting_date": {"title": "Quitting date", "type": "string", "format": "date", "x-nullable": true}, "role_name": {"title": "Role name", "type": "string", "maxLength": 1000, "x-nullable": true}, "job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}}, "x-nullable": true}, "UpdateHopeJobSkill": {"type": "object", "properties": {"job_code": {"title": "Job code", "type": "string", "maxLength": 3, "x-nullable": true}, "skill_code": {"title": "Skill code", "type": "string", "maxLength": 6, "x-nullable": true}}}, "UpdateEngHope": {"required": ["payroll_price", "job_skills"], "type": "object", "properties": {"place_code1": {"title": "Place code1", "type": "string", "maxLength": 9, "x-nullable": true}, "place_code2": {"title": "Place code2", "type": "string", "maxLength": 9, "x-nullable": true}, "place_code3": {"title": "Place code3", "type": "string", "maxLength": 9, "x-nullable": true}, "employ_code": {"title": "Employ code", "type": "string", "maxLength": 2, "x-nullable": true}, "payroll_price": {"title": "Payroll price", "type": "string", "minLength": 1, "x-nullable": true}, "payroll_code": {"title": "Payroll code", "type": "string", "maxLength": 3, "x-nullable": true}, "job_skills": {"type": "array", "items": {"$ref": "#/definitions/UpdateHopeJobSkill"}}, "remote_code": {"title": "Remote code", "type": "string", "maxLength": 2, "x-nullable": true}}, "x-nullable": true}, "UpdateEngSelfAssesment": {"type": "object", "properties": {"remote_exp_years": {"title": "Remote exp years", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "remote_job_description": {"title": "Remote job description", "type": "string", "maxLength": 256, "x-nullable": true}, "remote_skill_1": {"title": "Remote skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "remote_skill_2": {"title": "Remote skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "remote_skill_3": {"title": "Remote skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "global_work_exp": {"title": "Global work exp", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "global_skill_1": {"title": "Global skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "global_skill_2": {"title": "Global skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "global_skill_3": {"title": "Global skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "social_style": {"title": "Social style", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "communication_skill_1": {"title": "Communication skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "communication_skill_2": {"title": "Communication skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "communication_skill_3": {"title": "Communication skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "report_skill_1": {"title": "Report skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "report_skill_2": {"title": "Report skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "report_skill_3": {"title": "Report skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "management_skill_1": {"title": "Management skill 1", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "management_skill_2": {"title": "Management skill 2", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "management_skill_3": {"title": "Management skill 3", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}}}, "UpdateEngHighLightProject": {"type": "object", "properties": {"name": {"title": "Name", "type": "string", "minLength": 1, "x-nullable": true}, "description": {"title": "Description", "type": "string", "minLength": 1, "x-nullable": true}, "size": {"title": "Size", "type": "string", "minLength": 1, "x-nullable": true}, "role_name": {"title": "Role name", "type": "string", "minLength": 1, "x-nullable": true}, "responsibilities": {"title": "Responsibilities", "type": "string", "minLength": 1, "x-nullable": true}, "technology_used": {"title": "Technology used", "type": "string", "minLength": 1, "x-nullable": true}, "from_date": {"title": "From date", "type": "string", "format": "date", "x-nullable": true}, "to_date": {"title": "To date", "type": "string", "format": "date", "x-nullable": true}}, "x-nullable": true}, "UpdateUser": {"required": ["educations", "languages", "qualifications", "skills", "experiences", "requirements", "highlight_projects"], "type": "object", "properties": {"profile_image_path": {"title": "Profile image path", "type": "string", "maxLength": 200, "minLength": 1, "x-nullable": true}, "first_name": {"title": "First name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "last_name": {"title": "Last name", "type": "string", "maxLength": 30, "minLength": 1, "x-nullable": true}, "nickname": {"title": "Nickname", "type": "string", "maxLength": 30, "x-nullable": true}, "sex_type": {"title": "Sex type", "type": "integer", "maximum": **********, "minimum": -**********, "x-nullable": true}, "birth_date": {"title": "Birth date", "type": "string", "format": "date", "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "tel": {"title": "Tel", "type": "string", "maxLength": 20, "minLength": 1, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "city_name": {"title": "City name", "type": "string", "maxLength": 50, "minLength": 1, "x-nullable": true}, "passport_number": {"title": "Passport number", "type": "string", "maxLength": 9, "minLength": 1, "x-nullable": true}, "passport_image_path": {"title": "Passport image path", "type": "string", "maxLength": 200, "minLength": 1, "x-nullable": true}, "educations": {"type": "array", "items": {"$ref": "#/definitions/UpdateEngAcademic"}, "x-nullable": true}, "languages": {"type": "array", "items": {"$ref": "#/definitions/UpdateEngLanguage"}, "x-nullable": true}, "qualifications": {"type": "array", "items": {"$ref": "#/definitions/UpdateEngLicense"}, "x-nullable": true}, "skills": {"type": "array", "items": {"$ref": "#/definitions/UpdateEngSkill"}, "x-nullable": true}, "experiences": {"type": "array", "items": {"$ref": "#/definitions/UpdateEngCareer"}, "x-nullable": true}, "requirements": {"$ref": "#/definitions/UpdateEngHope"}, "pr": {"title": "Pr", "type": "string", "maxLength": 10000, "minLength": 1, "x-nullable": true}, "self_introduction_url": {"title": "Self introduction url", "type": "string", "format": "uri"}, "whatsapp_url": {"title": "Whatsapp url", "type": "string"}, "facebook_url": {"title": "Facebook url", "type": "string"}, "linkedin_url": {"title": "Linkedin url", "type": "string"}, "international_tel": {"title": "International tel", "type": "string", "maxLength": 3, "x-nullable": true}, "updated": {"title": "Updated", "type": "string", "format": "date-time", "x-nullable": true}, "last_academic_code": {"title": "Last academic code", "type": "string", "maxLength": 3, "minLength": 1, "x-nullable": true}, "self_assesment": {"$ref": "#/definitions/UpdateEngSelfAssesment"}, "email_temp": {"title": "Email temp", "type": "string", "maxLength": 100, "x-nullable": true}, "optional_engineer_id": {"title": "Optional engineer id", "type": "integer"}, "skills_for_cv_display": {"title": "Skills for cv display", "type": "string", "x-nullable": true}, "professional_summary": {"title": "Professional summary", "type": "string", "x-nullable": true}, "highlight_projects": {"type": "array", "items": {"$ref": "#/definitions/UpdateEngHighLightProject"}, "x-nullable": true}}}, "UpdateUserResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/UserDetailsSerializers"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "SelfAssessmentSheet": {"required": ["text_en", "text_vi", "text"], "type": "object", "properties": {"text_en": {"title": "Text en", "type": "string", "maxLength": 255, "minLength": 1}, "text_vi": {"title": "Text vi", "type": "string", "maxLength": 255, "minLength": 1}, "text": {"title": "Text", "type": "string", "maxLength": 255, "minLength": 1}}, "x-nullable": true}, "EvaluationMethod": {"required": ["text_en", "text_vi", "text"], "type": "object", "properties": {"text_en": {"title": "Text en", "type": "string", "minLength": 1}, "text_vi": {"title": "Text vi", "type": "string", "minLength": 1}, "text": {"title": "Text", "type": "string", "minLength": 1}}}, "Question": {"required": ["text_en", "text_vi", "text"], "type": "object", "properties": {"text_en": {"title": "Text en", "type": "string", "minLength": 1}, "text_vi": {"title": "Text vi", "type": "string", "minLength": 1}, "text": {"title": "Text", "type": "string", "minLength": 1}, "is_expanded": {"title": "Is expanded", "type": "boolean"}}}, "Description": {"required": ["text", "text_en", "text_vi"], "type": "object", "properties": {"text": {"title": "Text", "type": "string", "minLength": 1}, "text_en": {"title": "Text en", "type": "string", "minLength": 1}, "text_vi": {"title": "Text vi", "type": "string", "minLength": 1}}}, "Option": {"required": ["uuid", "text_en", "text_vi", "text", "description"], "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "text_en": {"title": "Text en", "type": "string", "minLength": 1}, "text_vi": {"title": "Text vi", "type": "string", "minLength": 1}, "text": {"title": "Text", "type": "string", "minLength": 1}, "is_selected": {"title": "Is selected", "type": "boolean"}, "description": {"$ref": "#/definitions/Description"}}}, "Questions": {"required": ["question", "options"], "type": "object", "properties": {"question": {"$ref": "#/definitions/Question"}, "options": {"type": "array", "items": {"$ref": "#/definitions/Option"}}}}, "SelfAssessmentData": {"required": ["remote_work_skills", "communication_skills_self_assessment_sheet", "global_responsiveness_skills", "reporting_consultation_skills_self_evaluation", "project_management_skills_self_evaluation", "evaluation_methods", "questions"], "type": "object", "properties": {"remote_work_skills": {"$ref": "#/definitions/SelfAssessmentSheet"}, "communication_skills_self_assessment_sheet": {"$ref": "#/definitions/SelfAssessmentSheet"}, "global_responsiveness_skills": {"$ref": "#/definitions/SelfAssessmentSheet"}, "reporting_consultation_skills_self_evaluation": {"$ref": "#/definitions/SelfAssessmentSheet"}, "project_management_skills_self_evaluation": {"$ref": "#/definitions/SelfAssessmentSheet"}, "evaluation_methods": {"type": "array", "items": {"$ref": "#/definitions/EvaluationMethod"}}, "questions": {"type": "array", "items": {"$ref": "#/definitions/Questions"}}}}, "SelfAssessmentResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/SelfAssessmentData"}, "errors": {"type": "array", "items": {"type": "string", "minLength": 1}, "x-nullable": true}}}, "SelfAssessmentAnswer": {"required": ["list_answer"], "type": "object", "properties": {"list_answer": {"type": "array", "items": {"type": "string", "x-nullable": true}}}}, "SaveCVUploaded": {"required": ["cv_id"], "type": "object", "properties": {"cv_id": {"title": "Cv id", "type": "string", "format": "uuid"}}}, "SaveCVUploadedResponse": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"title": "Data", "type": "string", "minLength": 1, "x-nullable": true}}}, "UpdatePassword": {"required": ["current_password", "new_password"], "type": "object", "properties": {"current_password": {"title": "Current password", "type": "string", "minLength": 1}, "new_password": {"title": "New password", "type": "string", "minLength": 1}}}, "UploadCVUploaded": {"required": ["cv_id", "cv_data"], "type": "object", "properties": {"cv_id": {"title": "Cv id", "type": "string", "minLength": 1}, "cv_data": {"title": "Cv data", "type": "object"}}, "x-nullable": true}, "UploadCVUploadedResponse": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"$ref": "#/definitions/UploadCVUploaded"}}}, "SPCompanyRegistered": {"type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "about_us": {"title": "About us", "type": "string", "maxLength": 5000, "x-nullable": true}, "business_details": {"title": "Business details", "type": "string", "maxLength": 5000, "x-nullable": true}, "country_code": {"title": "Country code", "type": "string", "maxLength": 3, "x-nullable": true}, "address_code": {"title": "Address code", "type": "string", "maxLength": 9, "x-nullable": true}}}, "SPListRegisteredCompanyResponseModel": {"required": ["message", "errors", "data"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}, "data": {"type": "array", "items": {"$ref": "#/definitions/SPCompanyRegistered"}}}}, "SPRequestInterviewBody": {"required": ["host_company_id", "host_company_recruit_id", "engineer_id", "message"], "type": "object", "properties": {"host_company_id": {"title": "Host company id", "type": "integer"}, "host_company_recruit_id": {"title": "Host company recruit id", "type": "integer"}, "engineer_id": {"title": "Engineer id", "type": "integer"}, "message": {"title": "Message", "type": "string", "minLength": 1}}}, "ManageHostCompany": {"type": "object", "properties": {"company_id": {"title": "Company id", "type": "integer", "readOnly": true}, "name": {"title": "Name", "type": "string", "maxLength": 200, "x-nullable": true}, "logo_image_path": {"title": "Logo image path", "type": "string", "maxLength": 500, "x-nullable": true}, "message_count": {"title": "Message count", "type": "integer", "readOnly": true}, "total_applicants": {"title": "Total applicants", "type": "integer", "readOnly": true}, "new_applicants": {"title": "New applicants", "type": "integer", "readOnly": true}, "unprocessed_task": {"title": "Unprocessed task", "type": "integer", "readOnly": true}, "last_status_update": {"title": "Last status update", "type": "string", "format": "date-time", "readOnly": true}, "active_job_listings": {"title": "Active job listings", "type": "integer", "readOnly": true}}}, "ListManageHostCompany": {"required": ["next", "previous", "results"], "type": "object", "properties": {"next": {"title": "Next", "type": "string", "minLength": 1, "x-nullable": true}, "previous": {"title": "Previous", "type": "string", "minLength": 1, "x-nullable": true}, "results": {"type": "array", "items": {"$ref": "#/definitions/ManageHostCompany"}}}}, "ListManageHostCompanyResponseModel": {"required": ["message", "data", "errors"], "type": "object", "properties": {"message": {"title": "Message", "type": "string", "minLength": 1, "x-nullable": true}, "data": {"$ref": "#/definitions/ListManageHostCompany"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorDetail"}, "x-nullable": true}}}, "TokenRefresh": {"required": ["refresh"], "type": "object", "properties": {"refresh": {"title": "Refresh", "type": "string", "minLength": 1}, "access": {"title": "Access", "type": "string", "readOnly": true, "minLength": 1}}}, "TokenVerify": {"required": ["token"], "type": "object", "properties": {"token": {"title": "Token", "type": "string", "minLength": 1}}}}}